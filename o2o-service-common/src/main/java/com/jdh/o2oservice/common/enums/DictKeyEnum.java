package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/22 6:18 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum DictKeyEnum {
    STATION_ASSIGN_TYPE("stationAssignType", "实验室分配规则"),
    SERVICE_RESOURCE_TYPE("serviceResourceType", "服务资源类型"),
    ITEM_TYPE("itemType", "检测项目类型"),
    ITEM_SOURCE("itemSource", "检测项目来源"),
    TEST_RESULT_TYPE("testResultType", "检测结果类型"),
    TEST_WAY("testWay", "检测方法学"),
    MEDICAL_RECORD_COST_TYPE("medicalRecordCostType", "病案首页费用分类"),
    COLLECT_NOTES_TYPE("collectNotesType", "收费票据分类"),
    LOW_VALUE_MATERIAL_LEVEL("lowValueMaterialLevel", "低值耗材分档"),
    SAMPLE_TYPE("sampleType", "样本类型"),
    SEX("sex","性别"),
    ACCOUNTING_SUBJECT_TYPE("accountingSubjectType", "会计科目分类"),
    SKU_SERVICE_TYPE("serviceType","商品服务类型"),
    SKU_SALE_CHANNEL("saleChannel","售卖渠道"),
    YES_OR_NO("yesOrNo","是否"),
    SKU_SALE_APPOINT_TEMPLATE("appointTemplateId","预约模板"),
    SKU_CUSTOMER_CONFIRM_TYPE("customerConfirmType","客户确认事项"),
    SERVICE_RECORD_TYPE("serviceRecordType","服务记录事项")
    ;


    /**
     * key
     */
    private String key;
    /**
     * desc
     */
    private String desc;


}
