package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @date 2025-07-16 14:00
 */

@Getter
@AllArgsConstructor
public enum MedicalPromiseSubStatusEnum {


    /**
     * 样本检查中
     */
    SAMPLE_CHECK(201,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), "样本检查中",BizSceneActionKeyEnum.RECEIVE_SAMPLE),
    /**
     * 样本检查通过
     */
    SAMPLE_CHECK_PASS(202,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本检查通过",BizSceneActionKeyEnum.RECEIVE_SAMPLE),
    /**
     * 样本检查-样本上报异常-重采样-确认中
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT(251,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-重采样-确认中",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本检查中-样本上报异常-重采样-用户同意
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_AGREE(252,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-重采样-用户同意",BizSceneActionKeyEnum.CONCESSION_TEST),
    /**
     * 样本检查中-样本上报异常-重采样-用户不同意
     */
    SAMPLE_CHECK_REFUSE_RE_COLLECT_REFUSE(253,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-重采样-用户不同意",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本检查中-样本上报异常-让步检测-确认中
     */
    SAMPLE_CHECK_REFUSE_CHECK_CONNECT(254,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-让步检测-确认中",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本检查中-样本上报异常-让步检测-用户同意
     */
    SAMPLE_CHECK_REFUSE_CHECK_AGREE(255,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-让步检测-用户同意",BizSceneActionKeyEnum.CONCESSION_TEST),
    /**
     * 样本检查中-样本上报异常-让步检测-用户不同意
     */
    SAMPLE_CHECK_REFUSE_CHECK_REFUSE(256,MedPromiseMainStatusSyncEnum.CHECK.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),"样本上报异常-让步检测-用户不同意",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),

    /**
     * 样本处理中
     */
    SAMPLE_DEAL(301,MedPromiseMainStatusSyncEnum.DEAL.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本处理中",BizSceneActionKeyEnum.RECEIVE_SAMPLE),

    /**
     * 样本处理完成
     */
    SAMPLE_DEAL_FINISH(302,MedPromiseMainStatusSyncEnum.DEAL.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本处理完成",BizSceneActionKeyEnum.RECEIVE_SAMPLE),

    /**
     * 样本处理中-异常-重采样-确认中
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_CONNECT(351,MedPromiseMainStatusSyncEnum.DEAL.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-确认中",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本处理中-异常-重采样-用户同意
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_AGREE(352,MedPromiseMainStatusSyncEnum.DEAL.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户同意",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本处理中-异常-重采样-用户不同意
     */
    SAMPLE_DEAL_ERROR_RE_COLLECT_REFUSE(353,MedPromiseMainStatusSyncEnum.DEAL.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重采样-用户不同意",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 样本上机检测
     */
    SAMPLE_TEST(501,MedPromiseMainStatusSyncEnum.TEST.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"已上机检测",BizSceneActionKeyEnum.TEST_SAMPLE),
    /**
     * 样本上机检测完成
     */
    SAMPLE_TEST_FINISH(502,MedPromiseMainStatusSyncEnum.TEST.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"检测完成",BizSceneActionKeyEnum.TEST_FINISH),
    /**
     * 样本上机检测-异常-重检测
     */
    SAMPLE_TEST_ERROR_RE_TEST(551,MedPromiseMainStatusSyncEnum.TEST.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"样本上报异常-重检测",BizSceneActionKeyEnum.EXCEPTION_SUBMIT),
    /**
     * 报告审核
     */
    REPORT_CHECK(601,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"报告审核中",BizSceneActionKeyEnum.TEST_FINISH),
    /**
     * 报告审核-通过
     */
    REPORT_CHECK_PASS(602,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),"报告审核成功",BizSceneActionKeyEnum.AUDIT_PASS),

    /**
     * 报告审核通过-撤回
     */
    REPORT_RESET(603,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),"报告审核通过-撤回",BizSceneActionKeyEnum.TEST_FINISH),

    /**
     * 报告重上传审核
     */
    RE_CHECK(604,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),"重上传-审核",BizSceneActionKeyEnum.TEST_FINISH),

    /**
     * 报告审核-拒绝
     */
    REPORT_CHECK_ERROR(651,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),"报告审核失败",BizSceneActionKeyEnum.AUDIT_REFUSE),
    /**
     * 报告重审核-拒绝
     */
    REPORT_RE_CHECK_ERROR(652,MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),"报告重审核失败",BizSceneActionKeyEnum.AUDIT_REFUSE),



    ARRIVE_STATION_WAIT_COLLECT(402,MedPromiseMainStatusSyncEnum.ARRIVE.getCallBackMainStatus(),MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), "收样",BizSceneActionKeyEnum.RECEIVE_SAMPLE);


    ;
    /**
     * 子状态值
     */
    private final Integer subStatus;
    /**
     * 回调主状态值
     */
    private final Integer callBackMainStatus;

    /**
     * 检测单状态值
     */
    private final Integer status;

    /**
     * 描述该枚举子状态的详细信息。
     */
    private final String desc;

    private BizSceneActionKeyEnum bizSceneActionKey;


    /**
     * 根据子状态获取对应的枚举值。
     * @param subStatus 子状态值
     * @return 对应的枚举值，若未找到则返回 null
     */
    public static MedicalPromiseSubStatusEnum getEnumBySubStatus(Integer subStatus){
        if (Objects.isNull(subStatus)){
            return null;
        }
        for (MedicalPromiseSubStatusEnum medicalPromiseSubStatusEnum : MedicalPromiseSubStatusEnum.values()){
            if (subStatus.equals(medicalPromiseSubStatusEnum.getSubStatus())){
                return medicalPromiseSubStatusEnum;
            }
        }
        return null;
    }

    public static Set<Integer> getUnCollectSubStatus(){
        return Sets.newHashSet(
                MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT.getSubStatus(),
                MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_CONNECT.getSubStatus()
        );
    }

}
