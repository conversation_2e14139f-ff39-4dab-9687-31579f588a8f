package com.jdh.o2oservice.common.result.response;

import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 通用返回结果
 *
 * @author: yang<PERSON>yu
 * @date: 2022/8/25 10:19 下午
 * @version: 1.0
 */
@ToString
public class ShipResponse<T> extends BaseResponse implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 3281261508261282781L;

    private static final Integer SHIP_SUCCESS = 200;
    private static final Integer SHIP_FAIL = 999;

    private static final String SHIP_SUCCESS_MSG = "success";
    private static final String SHIP_FAIL_MSG = "fail";

    /**
     *
     */
    private T data;

    /**
     *
     */
    public ShipResponse() {
    }

    /**
     *
     */
    public T getData() {
        return this.data;
    }

    /**
     *
     */
    public void setData(T data) {
        this.data = data;
    }


    public static <T> ShipResponse<T> buildNewResult(Integer errorCode, String errorMsg, T data) {
        ShipResponse<T> r = new ShipResponse<T>();
        r.setStatus(errorCode);
        r.setMsg(errorMsg);
        r.setData(data);
        return r;
    }

    /**
     * 组装成功的 ApiResult
     *
     * @param data 成功实体
     * @param <T>
     * @return ApiResult
     */
    public static <T> ShipResponse<T> buildSuccessResult(T data) {
        return buildNewResult(SHIP_SUCCESS, SHIP_SUCCESS_MSG, data);
    }

    /**
     * 组装未知异常的 ApiResult
     *
     * @param <T>
     * @return ApiResult
     */
    public static <T> ShipResponse<T> buildUnknownErrorResult() {
        return buildNewResult(SHIP_FAIL, SHIP_FAIL_MSG, null);
    }

    public static <T> ShipResponse<T> buildErrorResult(Integer errorCode, String errorMsg) {
        return buildNewResult(errorCode, errorMsg, null);
    }

    public static <T> ShipResponse<T> buildHelpErrorResult(Integer errorCode, String errorMsg, String helpMessage) {
        ShipResponse<T> r = buildNewResult(errorCode, errorMsg, null);
        r.setHelpMessage(helpMessage);
        return r;
    }

    public static <T> ShipResponse<T> buildCustomizeErrorResult(Integer errorCode, String errorMsg, T data) {
        return buildNewResult(errorCode, errorMsg, data);
    }

    public static <T> ShipResponse<T> buildCustomizeHelpErrorResult(Integer errorCode, String errorMsg, String helpMessage, T data) {
        ShipResponse<T> r = buildNewResult(errorCode, errorMsg, data);
        r.setHelpMessage(helpMessage);
        return r;
    }

    public static <T> ShipResponse<T> buildCustomizeErrorResult(Integer errorCode, String errorMsg, HashMap<String, String> extMap, T data) {
        ShipResponse<T> r = buildNewResult(errorCode, errorMsg, data);
        r.setExtMap(extMap);
        return r;
    }
}
