package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yaoqing<PERSON>
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum OperatorRoleTypeEnum {

    /**
     * 用户
     */
    USER_SELF(1, "用户"),

    /**
     * 运营
     */
    BIZ_OPERATION(2, "运营"),

    /**
     * 客服
     */
    CUSTOMER_SERVICE(3, "客服"),

    /**
     * 医生
     */
    DOCTOR(4, "医生"),

    /**
     * 系统
     */
    SYSTEM(10, "系统"),

    ;

    /**
     * 类型枚举
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String desc;


}
