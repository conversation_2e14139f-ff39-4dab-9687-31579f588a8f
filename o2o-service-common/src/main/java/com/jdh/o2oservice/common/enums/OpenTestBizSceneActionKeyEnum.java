package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开放平台操作类型
 * <AUTHOR>
 * @date 2025-08-26 18:48
 */

@Getter
@AllArgsConstructor
public enum OpenTestBizSceneActionKeyEnum {


    /**
     * 待接单
     */
    WAITING_RECEIVE("waitingReceive","待接单"),

    /**
     * 已接单
     */
    RECEIVE("receive","已接单"),

    /**
     * 上门
     */
    TO_HOME("toHome","上门"),
    /**
     * 采样
     */
    COLLECT_SAMPLE("collectSample","采样"),
    /**
     * 配送
     */
    DELIVERY("delivery","配送"),

    /**
     * 送达
     */
    DELIVERY_FINISH("deliveryFinish","送达"),

    /**
     * 收样
     */
    RECEIVE_SAMPLE("receiveSample", "收样"),

    /**
     * 生成流转码
     */
    GENERATE_FLOW_CODE("generateFlowCode", "生成流转码"),

    /**
     * 上机
     */
    TEST_SAMPLE("testSample", "上机"),

    /**
     * 检测完成
     */
    TEST_FINISH("testFinish", "检测完成"),

    /**
     * 审核通过
     */
    AUDIT_PASS("auditPass", "审核通过"),

    /**
     * 审核不通过
     */
    AUDIT_REFUSE("auditRefuse", "审核不通过"),

    /**
     * 异常上报
     */
    EXCEPTION_SUBMIT("exceptionSubmit", "异常上报"),

    /**
     * 让步检测
     */
    CONCESSION_TEST("concessionTest", "让步检测"),

    /**
     * 换绑条码
     */
    CHANGE_SPECIMEN_CODE("changeSpecimenCode", "换绑条码");





    ;

    /**
     * 业务场景动作，用于标识不同的业务场景动作。
     */
    private final String bizSceneActionKey;

    /**
     * 业务场景动作描述，用于记录每个业务场景动作的详细信息。
     */
    private final String bizSceneActionDesc;

}
