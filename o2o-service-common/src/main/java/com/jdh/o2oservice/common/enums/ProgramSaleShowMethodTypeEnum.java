package com.jdh.o2oservice.common.enums;

import lombok.Getter;

import java.io.Serializable;

/**
 * 套餐服务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/4 16:35
 */
@Getter
public enum ProgramSaleShowMethodTypeEnum implements Serializable {
    /**
     * 普通服务
     */
    NORMAL(1,"普通服务"),
    /**
     * 组合服务
     */
    COMPOSE(2,"组合服务"),
    /**
     * 9-影子服务（基于普通服务异构化部分属性，比如根据不同渠道调整预约规则）
     */
    SHADOW(9,"影子服务");


    private final Integer typeNo;
    /**
     *
     */
    private final String name;

    /**
     * @param typeNo
     * @param name
     */
    ProgramSaleShowMethodTypeEnum(Integer typeNo, String name) {
        this.typeNo = typeNo;
        this.name = name;
    }
}
