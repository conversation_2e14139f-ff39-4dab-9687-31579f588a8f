package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * @Project : 履约单细分状态
 * <AUTHOR> maoxianglin1
 * @create 2025/7/14 下午6:20
 */
@AllArgsConstructor
@Getter
public enum MedicalPromiseSubdivisionStatusEnum {

    /**
     * 样本待送检
     */
    MATERIAL_INFO_PENDING_INSPECTION(0, MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), Lists.newArrayList(), "样本待送检", 0),

    /**
     * 样本送检中
     */
    MATERIAL_INFO_UNDER_INSPECTION(1, MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), Lists.newArrayList(), "样本送检中", 0),


    /**
     * 样本检查中
     */
    MATERIAL_INFO_INSPECTING(2, MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_CHECK.getSubStatus(), MedicalPromiseSubStatusEnum.SAMPLE_CHECK_PASS.getSubStatus()), "样本检查中", 1),


    /**
     * 样本检查异常
     */
    MATERIAL_INFO_INSPECTING_EXCEPTION(3, MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_AGREE.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_REFUSE.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_CONNECT.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_AGREE.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_REFUSE.getSubStatus()), "样本检查异常", 1),


    /**
     * 样本处理中
     */
    MATERIAL_INFO_PROCESSING(4, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_DEAL_FINISH.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_CONNECT.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_AGREE.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_REFUSE.getSubStatus(),
            MedicalPromiseSubStatusEnum.ARRIVE_STATION_WAIT_COLLECT.getSubStatus()), "样本处理中", 2),


    /**
     * 上机检测中
     */
    MATERIAL_INFO_MACHINE_INSPECTION(5, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_TEST_FINISH.getSubStatus(),
            MedicalPromiseSubStatusEnum.SAMPLE_TEST_ERROR_RE_TEST.getSubStatus()), "上机检测中", 3),


    /**
     * 结果审核中
     */
    MATERIAL_INFO_RESULT_REVIEW(6, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus()), "结果审核中", 4),


    /**
     * 结果审核异常
     */
    MATERIAL_INFO_RESULT_REVIEW_EXCEPTION(7, MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK_ERROR.getSubStatus()), "结果审核异常", 4),


    /**
     * 已完成
     */
    MATERIAL_INFO_INSPECTION_COMPLETED(8, MedicalPromiseStatusEnum.COMPLETED.getStatus(), Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK_PASS.getSubStatus(),
            MedicalPromiseSubStatusEnum.REPORT_RESET.getSubStatus(),
            MedicalPromiseSubStatusEnum.RE_CHECK.getSubStatus(),
            MedicalPromiseSubStatusEnum.REPORT_RE_CHECK_ERROR.getSubStatus()), "检测完成", 0),


    /**
     * 已退款
     */
    MATERIAL_INFO_REFUNDED(9, MedicalPromiseStatusEnum.INVALID.getStatus(), Lists.newArrayList(), "已退款", 0),
    ;


    /**
     * 状态code
     */
    private final Integer statusCode;

    /**
     * 检测单状态（履约节点状态）
     */
    private final Integer medicalPromiseStatus;

    /**
     * 细分节点状态
     */
    private final List<Integer> subdivisionStatusList;

    /**
     * 检测单状态描述
     */
    private final String statusDesc;

    /**
     * 检测中子状态
     */
    private final Integer checkSubdivisionStatus;

    /**
     * 找到特定的履约单细分状态
     *
     * @param medicalPromiseStatus 检测单状态
     * @param subdivisionStatus    细分节点状态
     * @return 状态枚举
     */
    public static MedicalPromiseSubdivisionStatusEnum getMedicalPromiseSubdivisionStatusEnumByCondition(Integer medicalPromiseStatus, Integer subdivisionStatus, Integer promiseStatus) {

        // 特殊逻辑处理：如果当前检测单节点不是完成或者检查中，并且履约单状态不是履约完成或者服务完成，则直接返回样本待送检状态
        // 检测单展示待送检的promise状态
        List<Integer> medicalWaitTolabStatusList = Arrays.asList(11, 17);
        List<Integer> completedAndInCheckMedicalPromiseStatusList = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(), MedicalPromiseStatusEnum.COMPLETED.getStatus());
        if (!completedAndInCheckMedicalPromiseStatusList.contains(medicalPromiseStatus)) {
            if (!medicalWaitTolabStatusList.contains(promiseStatus)) {
                return MedicalPromiseSubdivisionStatusEnum.MATERIAL_INFO_PENDING_INSPECTION;
            }
            medicalPromiseStatus = MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus();
        }

        for (MedicalPromiseSubdivisionStatusEnum value : values()) {
            // 区分有无子节点状态数据
            if (!value.getMedicalPromiseStatus().equals(medicalPromiseStatus)) {
                continue;
            }
            if (subdivisionStatus != null && value.getSubdivisionStatusList().contains(subdivisionStatus)) {
                return value;
            }
        }
        if (MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().equals(medicalPromiseStatus)) {
            return MATERIAL_INFO_PROCESSING;
        }
        if (MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().equals(medicalPromiseStatus)) {
            return MATERIAL_INFO_UNDER_INSPECTION;
        }
        if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromiseStatus)) {
            return MATERIAL_INFO_INSPECTION_COMPLETED;
        }
        if (MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseStatus)) {
            return MATERIAL_INFO_REFUNDED;
        }
        return null;
    }
}
