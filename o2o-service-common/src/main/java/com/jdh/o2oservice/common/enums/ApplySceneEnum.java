package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 应用场景enum
 * @Enum: ApplySceneEnum
 * @Author: wangpengfei144
 * @Date: 2024/8/22
 */
@Getter
@AllArgsConstructor
public enum ApplySceneEnum {

    /**
     * toB
     */
    TO_B(1,"toB"),
    /**
     * toC
     */
    TO_C(2,"toC")
    ;


    /**
     * 场景值
     */
    private final Integer scene;
    /**
     * 描述
     */
    private final String desc;

    public static String getDesc(Integer scene){
        if (Objects.isNull(scene)){
            return null;
        }
        for (ApplySceneEnum applySceneEnum : ApplySceneEnum.values()){
            if (Objects.equals(scene,applySceneEnum.getScene())){
                return applySceneEnum.getDesc();
            }
        }
        return null;

    }
}
