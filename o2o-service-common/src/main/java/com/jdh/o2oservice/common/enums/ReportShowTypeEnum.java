package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告展示类型枚举
 * <AUTHOR>
 * @date 2025-04-10 14:50
 */

@Getter
@AllArgsConstructor
public enum ReportShowTypeEnum {

    /**
     * 结构化页面展示类型
     */
    STRUCT(1,"结构化页面"),
    /**
     * PDF格式的报告展示类型。
     */
    PDF(2,"PDF"),

    ;

    /**
     * 报告展示类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;
}
