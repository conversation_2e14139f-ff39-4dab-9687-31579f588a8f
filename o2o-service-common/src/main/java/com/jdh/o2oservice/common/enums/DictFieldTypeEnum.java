package com.jdh.o2oservice.common.enums;

/**
 * Description: 费项配置字典-字段类型枚举
 * Enum: DictFieldTypeEnum
 * @date 2024-08-20 11:28
 * <AUTHOR>
 */
public enum DictFieldTypeEnum {

    SERVICE_TYPE("SERVICE_TYPE","业务身份"),
    CHANNEL_ID("CHANNEL_ID","销售渠道"),
    ;

    /**
     *
     */
    DictFieldTypeEnum(String fieldValue, String fieldDesc) {
        this.fieldValue = fieldValue;
        this.fieldDesc = fieldDesc;
    }

    /**
     *
     * @param
     * @return
     */
    public static DictFieldTypeEnum getEnumByCode(String fieldValue){
        for (DictFieldTypeEnum DictFieldTypeEnum : DictFieldTypeEnum.values()) {
            if (DictFieldTypeEnum.fieldValue.equals(fieldValue)) {
                return DictFieldTypeEnum;
            }
        }
        return null;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 字段描述
     */
    private String fieldDesc;


}
