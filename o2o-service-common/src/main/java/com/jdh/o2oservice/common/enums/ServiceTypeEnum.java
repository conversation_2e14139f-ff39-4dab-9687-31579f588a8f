package com.jdh.o2oservice.common.enums;


import lombok.Getter;

/**
 * 服务类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Getter
public enum ServiceTypeEnum {

    /**
     *
     */
    VACCINE("vaccine","疫苗"),
    ORAL_CAVITY("oralCavity","口腔"),
    PHYSICAL("physical","体检"),
    COSMETIC("cosmetic","医美"),
    PSYCHOLOGICAL("psychological","心理咨询"),
    /** traditional Chinese medicine简称*/
    TCM("tcm","中医服务"),
    CELL_STORAGE("cellStorage","细胞存储"),
    GENE_DETECTION("geneDetection","基因检测"),
    OPHTHALMIC("ophthalmic","眼科服务"),
    HEALTH_MANAGEMENT("healthManagement","健康管理"),
    REHABILITATION_THERAPY("rehabilitationTherapy","康复治疗"),
    PREGNANCY("pregnancy","孕产服务"),
    PET("pet","宠物"),
    TEST("test","检测"),
    CARE("care","护理"),
    ;

    /**
     *
     */
    ServiceTypeEnum(String serviceType,String desc) {
        this.serviceType = serviceType;
        this.desc = desc;
    }

    /**
     * 按代码获取枚举
     *
     * @param serviceType serviceType
     * @return {@link ServiceTypeEnum}
     */
    public static ServiceTypeEnum getEnumByCode(String serviceType){
        for (ServiceTypeEnum value : ServiceTypeEnum.values()) {
            if (value.serviceType.equals(serviceType)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 服务类型名称
     */
    private String serviceType;

    /**
     * 描述
     */
    private String desc;


}
