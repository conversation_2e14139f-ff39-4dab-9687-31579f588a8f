package com.jdh.o2oservice.common.enums;

import lombok.Getter;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/19 10:52 上午
 * @Description:
 */
@Getter
public enum ImpTypeEnum {

    PRODUCT_INDICATOR_LIST(0, "imp_product_indicator_list_%s", "导入指标列表"),
    PRODUCT_ITEM_LIST(1, "imp_product_item_list_%s", "导入项目列表"),
    PRODUCT_INDICATOR_CATEGORY_LIST(0, "imp_product_indicator_category_list_%s", "导入指标分类列表"),
    ;

    private Integer code;
    /**
     * impKey
     */
    private String impKey;
    /**
     * msg
     */
    private String msg;

    ImpTypeEnum(Integer code, String impKey, String msg) {
        this.code = code;
        this.impKey = impKey;
        this.msg = msg;
    }

    /**
     * @param objects
     * @return
     */
    public String getKey(Object... objects) {
        StringBuilder keyBuilder = new StringBuilder();
        for (Object object : objects) {
            keyBuilder.append(object.toString()).append("_");
        }
        String key = keyBuilder.substring(0, keyBuilder.length() - 1);

        return String.format(this.getImpKey(), key);
    }

    /**
     * @param code
     * @return
     */
    public static ImpTypeEnum getImpTypeEnum(Integer code) {
        for (ImpTypeEnum impTypeEnum : values()) {
            if (impTypeEnum.getCode().equals(code)) {
                return impTypeEnum;
            }
        }
        return null;
    }
}
