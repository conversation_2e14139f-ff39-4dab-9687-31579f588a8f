package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReturnVisitStatusEnum {


    /**
     * 初始状态，无需进行回访操作。
     */
    INIT(0,"无需回访"),

    /**
     * 表示回访状态为“待回访”，需要进行回访操作。
     */
    WAITING(1,"待回访"),

    /**
     * 表示回访状态为“已回访”，回访操作已经完成。
     */
    RETURN_VISIT(2,"已回访"),




    ;
    /**
     * 回访状态
     */
    private final Integer status;


    /**
     * 回访状态描述
     */
    private final String desc;




}
