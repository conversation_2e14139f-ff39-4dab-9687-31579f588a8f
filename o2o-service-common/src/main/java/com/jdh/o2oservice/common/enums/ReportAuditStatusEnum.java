package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 审核状态枚举
 * @date 2025-07-22 21:03
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportAuditStatusEnum {


    /**
     * 初始化状态，表示该项还未被审核。
     */
    INIT(0, "待审核"),



    /**
     * 审核通过状态。
     */
    PASS(1,"审核通过"),



    /**
     * 审核拒绝状态。
     */
    REFUSE(2,"审核拒绝"),


    DATA_ERROR(3,"数据异常，无法审核"),


    PASS_RESET(4,"审核通过-撤回")



    ;


    /**
     * 审核状态
     */
    private final Integer auditStatus;

    /**
     * 审核状态的描述信息
     */
    private final String auditStatusDesc;


    /**
     * 获取最终审核状态集合。
     * @return 包含通过和拒绝的审核状态的集合。
     */
    public static Set<Integer> getFinialStatus(){
        return Sets.newHashSet(ReportAuditStatusEnum.PASS.auditStatus,ReportAuditStatusEnum.REFUSE.auditStatus);
    }




}
