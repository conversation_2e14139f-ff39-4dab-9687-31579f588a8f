package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/1
 */
@Getter
@AllArgsConstructor
public enum CurveValueTypeEnum {

    /**
     * 表示CT-CT值 TM-TM值的枚举类型。
     */
    CT("ct"," CT-CT值 TM-TM值");

    /**
     * 枚举值的类型标识。
     */
    private final String type;

    /**
     * 枚举值的描述信息。
     */
    private final String desc;


    /**
     * 根据给定的类型获取对应的描述信息。
     * @param type CurveValueTypeEnum 枚举的类型属性
     * @return 对应类型的描述信息，若未找到则返回 null
     */
    public static String getDesc(String type) {
        for (CurveValueTypeEnum value : CurveValueTypeEnum.values()) {
            if (StringUtils.equals(value.type, type)) {
                return value.desc;
            }
        }
        return null;
    }


    /**
     * 根据描述获取曲线值类型。
     * @param desc 曲线值类型的描述。
     * @return 对应的曲线值类型，若未找到则返回 null。
     */
    public static String getType(String desc) {
        for (CurveValueTypeEnum value : CurveValueTypeEnum.values()) {
            if (StringUtils.equals(value.desc, desc)) {
                return value.type;
            }
        }
        return null;
    }

}
