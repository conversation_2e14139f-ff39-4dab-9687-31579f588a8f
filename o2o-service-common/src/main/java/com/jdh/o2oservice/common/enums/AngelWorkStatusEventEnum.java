package com.jdh.o2oservice.common.enums;

import lombok.Getter;

/**
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */

@Getter
public enum AngelWorkStatusEventEnum {

    /**
     *
     */
    ANGEL_WORK_RECEIVE("workReceived"),

    AN<PERSON>L_WORK_WAITING_SERVED("workWaitingServed"),

    ANGEL_WORK_IN_SERVE("workInServed"),

    ANGEL_WORK_DONE_SERVE("workDoneServed"),

    ANGEL_WORK_COMPLETE_SERVE("workFinishServed"),

    ANGEL_WORK_CANCEL_SERVE("cancelServed"),

    ANGEL_WORK_REFUND_FREEZE_SERVE("refundFreezeServed"),

    ANGEL_WORK_REFUND_SERVE("refundServed"),

    ANGEL_WORK_C_USER_CANCEL_SERVED("cUserCancelServed"),


    ;


    private String eventCode;


    AngelWorkStatusEventEnum(String eventCode) {
        this.eventCode = eventCode;
    }

}
