package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 指标特殊标签枚举
 * @Enum: IndicatorTagEnum
 * @Author: wangpengfei144
 * @Date: 2024/6/21
 */
@AllArgsConstructor
@Getter
public enum IndicatorTagEnum {

    /**
     * 条件致病
     */
    CONDITIONED_BACTERIA(1,"条件致病菌"),

    ;
    /**
     * 特殊标签类型
     */
    private final Integer tageType;
    /**
     * 特殊标签名称
     */
    private final String tageName;

    public static String getNameByType(Integer tageType){
        if (Objects.isNull(tageType)){
            return null;
        }
        for (IndicatorTagEnum indicatorTagEnum : IndicatorTagEnum.values()){
            if (indicatorTagEnum.getTageType().equals(tageType)){
                return indicatorTagEnum.getTageName();
            }
        }
        return null;
    }

}
