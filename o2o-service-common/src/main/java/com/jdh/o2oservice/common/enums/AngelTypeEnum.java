package com.jdh.o2oservice.common.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName AngelTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/6/9 2:23 PM
 * @Version 1.0
 **/
public enum AngelTypeEnum {

    /**
     *
     */
    DELIVERY(1, "骑手"),

    NURSE(2, "护士"),

    DELIVERY_NURSE(3, "骑手+护士"),
    ;

    /**
     * @param type
     * @param desc
     */
    AngelTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * 根据类型获取枚举对象
     *
     * @param type
     * @return
     */
    public static AngelTypeEnum getByType(Integer type) {
        for (AngelTypeEnum angelTypeEnum : AngelTypeEnum.values()) {
            if (angelTypeEnum.getType().equals(type)) {
                return angelTypeEnum;
            }
        }
        return null;
    }

    /**
     * 包含入参全部类型
     *
     * @param type
     * @return
     */
    public static List<Integer> containAngelType(Integer type) {
        List<Integer> result = new ArrayList<>();
        if(type==null){
            return result;
        }
        for (AngelTypeEnum angelTypeEnum : AngelTypeEnum.values()) {
            if (type.equals(angelTypeEnum.getType() & type)) {
                result.add(angelTypeEnum.getType());
            }
        }
        return result;
    }

    public static void main(String[] args) {
        List<Integer> list = AngelTypeEnum.containAngelType(3);
        if(CollectionUtils.isEmpty(list)){
            System.out.println("空");
        }
        list.forEach(t->{
            System.out.println(t);
        });
    }

    /**
     * 命中入参中任意类型
     *
     * @param type
     * @return
     */
    public static List<Integer> anyOneAngelType(Integer type) {
        List<Integer> result = new ArrayList<>();
        if(type==null){
            return result;
        }
        for (AngelTypeEnum angelTypeEnum : AngelTypeEnum.values()) {
            if ((angelTypeEnum.getType() & type) > 0) {
                result.add(angelTypeEnum.getType());
            }
        }
        return result;
    }

    /**
     * 等于入参类型
     *
     * @param type
     * @return
     */
    public static List<Integer> equalAngelType(Integer type) {
        List<Integer> result = new ArrayList<>();
        if(type==null){
            return result;
        }
        for (AngelTypeEnum angelTypeEnum : AngelTypeEnum.values()) {
            if (angelTypeEnum.getType().equals(type)) {
                result.add(angelTypeEnum.getType());
            }
        }
        return result;
    }

    /**
     * 检查是否包含指定类型
     * 注意：如果传入的source或者target为空，默认是不检查，直接放行返回true
     *
     * @param sourceType
     * @param targetType
     * @return
     */
    public static boolean matchContainType(Integer sourceType, Integer targetType) {
        if(Objects.isNull(sourceType) || Objects.isNull(targetType)) {
            return true;
        }
        return (sourceType & targetType) == targetType;
    }

}
