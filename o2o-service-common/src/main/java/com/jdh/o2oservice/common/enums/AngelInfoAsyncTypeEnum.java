package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum AngelInfoAsyncTypeEnum {

    /** */
    MAIN_DATA(1, "主数据变更"),
    /** */
    STATION(2, "服务站围栏变更"),
    /** */
    SKILL(3, "技能变更"),
    /** */
    WORK_STATUS(4, "开关诊变更"),
    ;


    private Integer code;
    private String desc;

    /**
     * 按代码获取枚举
     *
     * @param code 代码
     * @return {@link BusinessModeEnum}
     */
    public static AngelInfoAsyncTypeEnum getEnumByCode(Integer code) {
        for (AngelInfoAsyncTypeEnum value : AngelInfoAsyncTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
