package com.jdh.o2oservice.common.ext;

import com.jd.matrix.sdk.base.DomainModel;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName:BusinessIdentifier
 * @Description: 业务身份
 * 1、域模型继承DomainModel， 字段就按照自己的业务来就行，后续用于业务身份命中解析器的解析处理
 * @Author: yaoqinghai
 * @Date: 2024/5/14 21:03
 * @Vserion: 1.0
 **/
@Data
@Builder
public class O2oShipBusinessIdentifier implements DomainModel {

    /**
     * 业务垂直身份Code
     */
    private String bizVerticalCode;

    /**
     * 消费医疗垂直Code
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 业务模式
     */
    private String businessMode;

    /**
     * 服务者资源明细类型 201：达达  202：顺丰  203：闪送
     * AngelDetailTypeEnum
     */
    private Integer deliveryType;

    /**
     * 服务者资源明细类型 201：达达  202：顺丰
     * AngelDetailTypeEnum
     */
    private Integer angelDetailType;

    /**
     * 服务者资源类型 1：骑手 2：护士
     * AngelTypeEnum
     */
    private Integer angelType;
}
