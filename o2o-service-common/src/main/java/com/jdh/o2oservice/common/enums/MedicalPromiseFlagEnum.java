package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 项目描述
 * @Enum: MedicalPromiseFlagEnum
 * @Author: wangpengfei144
 * @Date: 2024/8/16
 */
@Getter
@AllArgsConstructor
public enum MedicalPromiseFlagEnum {

    /**
     * 普通项目
     */
    ORDINARY_SERVICE_ITEM("0","普通项目"),
    ADD_SERVICE_ITEM("1","加项订单"),

    ;
    private final String flag;
    private final String desc;

    /**
     *
     * @param flag
     * @return
     */
    public static String getDesc(String flag){
        if (StringUtils.isBlank(flag)){
            return "";
        }
        for (MedicalPromiseFlagEnum medicalPromiseFlagEnum : MedicalPromiseFlagEnum.values()) {
            if (StringUtils.equals(flag,medicalPromiseFlagEnum.getFlag())){
                return medicalPromiseFlagEnum.getDesc();
            }
        }
        return "";

    }



}
