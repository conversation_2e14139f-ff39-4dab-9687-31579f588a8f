package com.jdh.o2oservice.common.enums;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/19 11:00 上午
 * @Description:
 */
public enum ImportStatusEnum {
    /**
     *
     */
    NOT_IMPORT("0", "未导入"),
    IMPORTING("1", "导入中"),
    IMPORTED("2", "导入完成");


    private String code;
    /**
     * msg
     */
    private String msg;

    /**
     * importStatusMap
     */
    public static Map<String, ImportStatusEnum> importStatusMap = new ConcurrentHashMap<>();

    /**
     * @param code
     * @param msg
     */
    ImportStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    static {
        for (ImportStatusEnum importStatusEnum : ImportStatusEnum.values()) {
            importStatusMap.put(importStatusEnum.getCode(), importStatusEnum);
        }
        importStatusMap = Collections.unmodifiableMap(importStatusMap);
    }
}
