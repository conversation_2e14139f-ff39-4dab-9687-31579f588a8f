package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LabCompositeStatusEnum {


    //已出报告
    REPORT(6,"已出报告"),

    //已出报告
    REPORT_RE_UPLOAD(62,"报告重传"),

    //送检中
    DELIVERY(4,"送检中"),
    //已送达待收样
    WAITING_COLLECT(45,"已送达待收样"),
    //检测中
    TEST(5,"检测中"),
    //已退款
    INVALID(-1,"已退款"),
    //报告审核中
    AUDIT(61,"报告审核中"),
    //样本处理中
    SAMPLE_DEAL(46,"样本处理中"),

    //让步检测确认中
    CONCESSION_TEST_CONNECT(47,"让步检测-确认中"),

    //让步检测同意
    CONCESSION_TEST_AGREE(48,"让步检测-同意"),

    /**
     * 待骑手接单
     * 骑手检测场景对应AngelShipStatusEnum#SHIP_ORDER_INIT，AngelShipStatusEnum#WAITING_RECEIVE_ORDER
     * 护士上门检测场景，对应AngelShipStatusEnum#SHIP_ORDER_INIT，AngelShipStatusEnum#WAITING_RECEIVE_ORDER 和 null(即没有ship产生时，也是待接单)
     */
    WAITING_RECEIVE_ORDER(2, "待接单"),
    /**
     * 待骑手取货
     */
    WAITING_RECEIVE_GOODS(51, "上门中"),
    RECEIVE_GOODS(52, "上门采样"),
    /**
     * 配送中
     */
    DELIVERY_IN_PROGRESS(8, "配送中"),

    ;



    /**
     * 组合状态的唯一标识符。
     */
    private final Integer compositeStatus;

    /**
     * 组合状态的描述信息。
     */
    private final String CompositeStatusDesc;






}
