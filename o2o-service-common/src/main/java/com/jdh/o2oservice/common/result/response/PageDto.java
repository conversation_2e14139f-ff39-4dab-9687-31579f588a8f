package com.jdh.o2oservice.common.result.response;

import lombok.Data;

import java.util.List;

/**
 * @author: yang<PERSON><PERSON>
 * @date: 2024/1/23 6:36 下午
 * @version: 1.0
 */
@Data
public class PageDto<T> {

    private  static final PageDto EMPTY_PAGE = new PageDto();

    private long pageNum = 1;

    private long pageSize = 10;

    private long totalCount = 0;

    private long totalPage = 1;

    private List<T> list;


    public static PageDto getEmptyPage(){
        return EMPTY_PAGE;
    }



}
