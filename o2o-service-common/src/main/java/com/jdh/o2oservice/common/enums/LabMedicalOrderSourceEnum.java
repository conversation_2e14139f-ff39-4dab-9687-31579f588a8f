package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2025-09-12 13:56
 */

@Getter
@AllArgsConstructor
public enum LabMedicalOrderSourceEnum {

    /**
     * 用户订单
     */
    USER(1, "用户订单",""),



    /**
     * 质控订单
     */
    QUALITY(2, "质控订单","xfylMerchantQualityTest"),


    ;



    /**
     * 订单来源类型的唯一标识符。
     */
    private final Integer type;

    /**
     * 订单来源的描述信息。
     */
    private final String desc;

    /**
     * 垂直业务代码。
     */
    private final String verticalCode;


    /**
     * 根据垂直码获取订单来源类型
     * @param verticalCode 垂直码
     * @return 订单来源类型
     */
    public static Integer getTypeByVerticalCode(String verticalCode) {
        for (LabMedicalOrderSourceEnum value : LabMedicalOrderSourceEnum.values()) {
            if (StringUtils.isNotBlank(value.getVerticalCode()) && value.getVerticalCode().equals(verticalCode)) {
                return value.getType();
            }
        }
        return LabMedicalOrderSourceEnum.USER.getType();
    }


}
