package com.jdh.o2oservice.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 业务模式
 * @author: yang<PERSON>yu
 * @date: 2023/12/18 6:45 下午
 * @version: 1.0
 */
@Getter
public enum BusinessModeEnum {

    /**
     * 业务模式枚举
     */
    POP_LOC("popLoc", "POP_LOC",null),
    YK("ykwd", "一卡万店",null),
    POP_FREE("popFree", "零元单",null),

    SELF_TEST("selfTest", "骑手上门检测",1),
    ANGEL_TEST("angelTest", "服务者检测",1),
    ANGEL_CARE("angelCare", "服务者护理",2),
    SELF_TEST_TRANSPORT("selfTestTransport", "快递检测",1),
    ANGEL_TEST_NO_LABORATORY("angelTestNoLaboratory", "服务者检测（无实验室履约）",1),
    ;

    /**
     *
     */
    BusinessModeEnum(String code, String name,Integer itemType) {
        this.code = code;
        this.name = name;
        this.itemType = itemType;
    }

    /**
     * 按代码获取枚举
     *
     * @param code 代码
     * @return {@link BusinessModeEnum}
     */
    public static BusinessModeEnum getEnumByCode(String code){
        if(StringUtils.isBlank(code)) {
            return null;
        }
        for (BusinessModeEnum value : BusinessModeEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value;
            }
        }
        return null;
    }

    /**
     * 查询是否上门检测单和护理单
     *
     * @param code
     * @return
     */
    public static boolean checkTestAndCare(String code) {
        return BusinessModeEnum.ANGEL_TEST.getCode().equals(code) ||
                BusinessModeEnum.ANGEL_CARE.getCode().equals(code) ||
                BusinessModeEnum.SELF_TEST.getCode().equals(code) ||
                BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(code);
    }

    /**
     * -- GETTER --
     *
     */
    private final String code;

    /**
     * -- GETTER --
     *
     */
    private final String name;

    /**
     * -- itemType --
     *
     */
    private final Integer itemType;
}
