package com.jdh.o2oservice.common.result.response;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.HashMap;

/**
 * 抽象返回结果
 *
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/26 2:21 下午
 * @version: 1.0
 */
@Getter
@Slf4j
@Data
public abstract class BaseResponse {

    /**
     * 调用成功码
     */
    public static final String SUCCESS = "0000";
    public static final String UNKNOWN_ERROR = "9999";
    public static final String SUCCESS_MSG = "success";
    public static final String UNKNOWN_ERROR_MSG = "未知异常";

    /**
     * -- GETTER --
     */
    private Integer status;

    /**
     * -- GETTER --
     */
    private String code;

    /**
     * -- GETTER --
     */
    private String msg;

    /**
     * traceId
     * -- GETTER --
     * 获取traceId
     *
     * @return {@link String}
     */
    private String traceId;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 扩展参数
     */
    private HashMap<String, String> extMap;

    /**
     * 帮助描述
     */
    private String helpMessage;

    /**
     * ab 测试埋点JSON
     */
    private String buriedJson;

    public BaseResponse() {
        try {
            this.setTraceId(MDC.get("PFTID"));
        } catch (Exception exception) {
            log.error("Response 构建 setTraceId exception", exception);
        }
    }

    public static boolean isSuccess(Response result) {
        return SUCCESS.equals(result.getCode());
    }

    public boolean isSuccess() {
        return SUCCESS.equals(this.code);
    }

    public static boolean isFailure(Response result) {
        return !isSuccess(result);
    }
}
