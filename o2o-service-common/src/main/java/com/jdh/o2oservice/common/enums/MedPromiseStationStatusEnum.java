package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 检测单同步实验室状态枚举
 * @date 2025/2/19
 */
@Getter
@AllArgsConstructor
public enum MedPromiseStationStatusEnum {

    /**
     * “待服务者接单”。
     */
    WAITING_SERVER_ACCEPT(0, "待服务者接单"),
    /**
     * “待采样”。
     */
    WAITING_PICK_UP(2, "待采样"),
    /**
     * “样本送检中”。
     */
    SAMPLE_SENDING(3, "样本送检中"),
    /**
     * “样本送达实验室”。
     */
    SAMPLE_DELIVERY(4, "样本送达实验室"),
    /**
     * “检测中”。
     */
    TESTING(5, "检测中"),
    /**
     * “已出报告”。
     */
    REPORT_OUT(6, "已出报告"),
    /**
     * “已作废”。
     */
    INVALID(8, "已作废"),


    /**
     * 报告重置状态。
     */
    REPORT_RESET(9, "报告重置"),


    ;


    /**
     * 状态码。
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;
}

