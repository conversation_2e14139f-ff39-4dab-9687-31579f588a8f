package com.jdh.o2oservice.common.enums;

/**
 * Description: 操作日志场景枚举
 * Enum: OperateLogSceneEnum
 * @date 2024-08-20 11:28
 * <AUTHOR>
 */
public enum OperateLogSceneEnum {

    FEE_CONFIG("FEE_CONFIG","费项配置"),
    AVAIABLE_ADDRESS_CONFIG("AVAIABLE_ADDRESS_CONFIG","开城配置"),
    FIXED_SKU_CONFIG("FIXED_SKU_CONFIG","一口价sku配置"),
    ;

    /**
     *
     */
    OperateLogSceneEnum(String operateLogScene, String operateLogSceneDesc) {
        this.operateLogScene = operateLogScene;
        this.operateLogSceneDesc = operateLogSceneDesc;
    }

    /**
     *
     * @return
     */
    public static OperateLogSceneEnum getEnumByCode(String operateLogScene){
        for (OperateLogSceneEnum operateLogSceneEnum : OperateLogSceneEnum.values()) {
            if (operateLogSceneEnum.operateLogScene.equals(operateLogScene)) {
                return operateLogSceneEnum;
            }
        }
        return null;
    }

    public String getOperateLogScene() {
        return operateLogScene;
    }

    public void setOperateLogScene(String operateLogScene) {
        this.operateLogScene = operateLogScene;
    }

    public String getOperateLogSceneDesc() {
        return operateLogSceneDesc;
    }

    public void setOperateLogSceneDesc(String operateLogSceneDesc) {
        this.operateLogSceneDesc = operateLogSceneDesc;
    }

    /**
     * 操作日志场景
     */
    private String operateLogScene;

    /**
     * 操作日志场景描述
     */
    private String operateLogSceneDesc;


}
