package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 用户营销类型枚举
 * <AUTHOR>
 * @date 2024-12-09 10:48
 */

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum UserMarketTypeEnum {

    /**
     * 处方单
     */
    NET_FORMULARY("netFormulary","互医处方单"),


    ;
    /**
     * 用户营销类型
     */
    private String type;
    /**
     * 用户营销类型的描述信息
     */
    private String desc;

}
