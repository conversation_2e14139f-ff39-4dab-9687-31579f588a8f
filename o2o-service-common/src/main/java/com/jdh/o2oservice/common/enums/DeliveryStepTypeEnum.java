package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运送步骤枚举
 * <AUTHOR>
 * @date 2025-06-09 17:58
 */

@Getter
@AllArgsConstructor
public enum DeliveryStepTypeEnum {


    /**
     * 表示骑手配送步骤的类型。
     */
    RIDER(1,"骑手运力"),

    /**
     * 无人机运力配送步骤类型。
     */
    UAV(2,"无人机运力")


    ;

    /**
     * 表示当前配送步骤的类型编号。
     */
    private final Integer type;

    /**
     * 配送步骤类型的描述信息。
     */
    private final String desc;


}
