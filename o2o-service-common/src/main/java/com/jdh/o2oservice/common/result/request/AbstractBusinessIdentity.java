package com.jdh.o2oservice.common.result.request;

import lombok.Data;

/**
 * 抽象的业务身份信息
 * @author: yang<PERSON><PERSON>
 * @date: 2023/12/18 4:19 下午
 * @version: 1.0
 */
@Data
public abstract class AbstractBusinessIdentity {

    /**
     * 垂直身份Code
     */
    private String verticalCode;
    /**
     * 服务类型，serviceType和serviceNo至少传一个，优先传serviceType
     */
    private String serviceType;
    /**
     * 服务编号，POP为skuNo,一卡万店为抽象的服务编号
     */
    private String serviceId;
}
