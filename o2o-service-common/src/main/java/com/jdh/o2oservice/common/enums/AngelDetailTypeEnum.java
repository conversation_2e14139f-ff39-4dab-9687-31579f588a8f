package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: yaoqinghai
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum AngelDetailTypeEnum {

    /** */
    AUTO_TEST_SUPPLIER(-1, "自动化测试使用运力",1),
    /** */
    SELF_SUPPLIER(200, "自配",1),

    DADA_SUPPLIER(201, "达达",2),

    SHUNFENG_SUPPLIER(202, "顺丰",4),

    SHANSONG_SUPPLIER(203, "闪送",3),

    JD_LOGISTICS_SUPPLIER(205,"京东物流",100),

    THIRD_SUPPLIER(204, "第三方",5),

    UAV(206, "无人机",6),
    ;

    /**
     * 类型枚举
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String desc;

    /**
     * 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     */
    private Integer deliveryType;


    /**
     * 根据配送方式获取供应商
     * @param deliveryType 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     * @return
     */
    public static Integer getTypeByDelivery(Integer deliveryType){
        if (Objects.isNull(deliveryType)){
            return null;
        }
        for (AngelDetailTypeEnum angelDetailTypeEnum : AngelDetailTypeEnum.values()){
            if (Objects.equals(deliveryType,angelDetailTypeEnum.deliveryType)){
                return angelDetailTypeEnum.type;
            }
        }
        return null;
    };

}
