package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智能体调用记录扩展ID类型
 * @date 2025-05-20 20:57
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AutoBotsExtendIdTypeEnum {


    /**
     * 报告ID
     */
    REPORT_ID(1, "报告ID"),

    ;

    /**
     * 自动体调用记录扩展ID的类型值
     */
    private final Integer type;

    /**
     * 自动体调用记录扩展ID的类型描述
     */
    private final String desc;
}
