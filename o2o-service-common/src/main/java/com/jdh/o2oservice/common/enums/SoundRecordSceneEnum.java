package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum SoundRecordSceneEnum {


    /**
     * 工作电话场景的录音，用于记录护士与客户的沟通内容。
     */
    WORK_PHONE("workPhone","护士与客户沟通录音"),

    /**
     * 上门服务录音场景。
     */
    WORK_SERVICE("workService","上门服务录音")




    ;
    /**
     * 声音记录场景的唯一标识符。
     */
    private final String scene;

    /**
     * 声音记录场景的描述信息。
     */
    private final String desc;


    /**
     * 根据场景获取对应的描述信息。
     * @param scene 场景名称。
     * @return 对应场景的描述信息，若场景不存在则返回 null。
     */
    public static String getDesc(String scene){
        if (StringUtils.isBlank(scene)){
            return null;
        }

        for (SoundRecordSceneEnum value : SoundRecordSceneEnum.values()) {
            if(value.getScene().equals(scene)){
                return value.getDesc();
            }
        }
        return null;
    }

}
