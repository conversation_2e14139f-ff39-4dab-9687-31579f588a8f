package com.jdh.o2oservice.common.enums;

public enum ServiceRecordQuestionGroupStatusEnum {

    INIT(0, "未开始"),
    PROCESS(1, "进行中"),
    FINISH(2, "已完成"),
    ;

    ServiceRecordQuestionGroupStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private Integer status;

    private String desc;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
