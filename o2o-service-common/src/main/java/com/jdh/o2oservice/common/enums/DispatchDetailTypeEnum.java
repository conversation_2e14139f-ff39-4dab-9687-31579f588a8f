package com.jdh.o2oservice.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DispatchDetailTypeEnum
 * @Description 1-派单 2-抢单
 * <AUTHOR>
 * @Date 2024/4/22 11:16
 **/
public enum DispatchDetailTypeEnum {

    /**
     * 派单
     */
    ASSIGN(1, "派单"),

    /**
     * 预约单
     */
    GRAB(2, "抢单"),
    ;

    /**
     * TYPE_MAP
     */
    private static final Map<Integer, DispatchDetailTypeEnum> TYPE_MAP = new HashMap<>();

    /**
     *
     */
    static {
        for (DispatchDetailTypeEnum value : values()) {
            TYPE_MAP.put(value.type, value);
        }
    }

    /**
     * @param type
     * @param desc
     */
    DispatchDetailTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     *
     * @param type
     * @return
     */
    public static DispatchDetailTypeEnum valuesOf(Integer type){
        if(type == null){
            return null;
        }
        return TYPE_MAP.get(type);
    }
}