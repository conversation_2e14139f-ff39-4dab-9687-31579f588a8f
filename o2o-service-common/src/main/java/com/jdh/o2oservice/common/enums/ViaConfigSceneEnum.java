package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Project : 页面场景配置枚举
 * <AUTHOR> maoxianglin1
 * @create 2025/7/10 下午6:26
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ViaConfigSceneEnum {


    selfTestOrderDetail_V3("selfTestOrderDetail_V3","骑手上门服务单详情V3版本"),

    selfTestOrderDetail_V4("selfTestOrderDetail_V4","骑手上门服务单详情V4版本"),

    selfTestOrderDetail_V5("selfTestOrderDetail_V5","骑手上门服务单详情V5版本"),

    selfTestOrderDetail_V6("selfTestOrderDetail_V6","骑手上门服务单详情V6版本"),

    xfylHomeTestOrderDetail("xfylHomeTestOrderDetail","xfyl护士上门检测订单详情页V1版本"),

    xfylHomeTestOrderDetail_V2("xfylHomeTestOrderDetail_V2","xfyl护士上门检测订单详情页V2版本"),


    ;
    /**
     * 页面场景配置
     */
    private String scene;

    /**
     * 页面场景配置描述信息
     */
    private String desc;
}
