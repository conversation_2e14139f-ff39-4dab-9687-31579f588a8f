package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CurveTypeEnum {


    /**
     * 扩增曲线类型，用于表示PCR扩增过程中荧光信号的变化。
     */
    AMPLIFICATION_CURVE(1, "扩增曲线"),
    /**
     * 表示原始曲线类型，用于表示PCR扩增过程中未经处理的荧光信号变化。
     */
    RAW_CURVE(2, "原始曲线"),
    /**
     * 标准曲线类型，用于表示PCR扩增过程中的标准参考曲线。
     */
    STANDARD_CURVE(3, "标准曲线"),
    /**
     * 熔解峰值曲线类型，用于表示PCR扩增过程中熔解阶段的荧光信号峰值变化。
     */
    MELTING_PEAK_CURVE(4, "熔解峰值曲线"),
    /**
     * 熔解原始曲线类型，用于表示PCR扩增过程中熔解阶段的未经处理的荧光信号变化。
     */
    MELTING_RAW_CURVE(5, "熔解原始曲线");

    /**
     * 孔类型
     */
    private final Integer curveType;

    /**
     * 孔类型的描述信息。
     */
    private final String curveTypeDesc;
}
