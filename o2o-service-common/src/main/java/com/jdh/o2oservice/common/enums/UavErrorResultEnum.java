package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9
 * @description 无人机异常码
 */
@Getter
public enum UavErrorResultEnum {

    KY1("KY1","前一天空域申请时未允许"),

    KY2("KY2","起飞前临时通知空域限制"),

    TQ3("TQ3","全天天气限制，无法无人机运输"),

    TQ4("TQ4","临时天气变化，无法无人机运输"),

    GZ5("GZ5","无人机故障，全天无法运输"),

    GZ6("GZ6","无人机故障，在应急点降落"),

    GZ7("GZ7","无人机飞行中炸机"),

    YL8("YL8","突发爆单，备用的无人机运力无法满足"),

    YL9("YL9","航路拥堵，无人机无法按点起飞"),
    ;




    private String errorCode;

    private String desc;

    UavErrorResultEnum(String errorCode,String desc){
        this.errorCode = errorCode;
        this.desc = desc;
    }

    public static List<String> limitScene(){
        return Lists.newArrayList(KY1.errorCode, KY2.errorCode, TQ3.errorCode, TQ4.errorCode);
    }

    public static List<String> machineFaultScene(){
        return Lists.newArrayList(GZ5.errorCode, GZ6.errorCode, GZ7.errorCode);
    }

    public static List<String> faultScene(){
        return Lists.newArrayList(KY1.errorCode, KY2.errorCode, TQ3.errorCode, TQ4.errorCode, GZ5.errorCode, GZ6.errorCode, GZ7.errorCode);
    }

    public static List<String> timeOutScene(){
        return Lists.newArrayList(YL8.errorCode, YL9.errorCode);
    }
}
