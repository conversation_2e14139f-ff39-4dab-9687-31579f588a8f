package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum BizActionEnum {

    /** */
//    ANGEL_TRACK("queryAngelTrack", Lists.newArrayList(BizSceneEnum.B_RIDER_DETAIL.getScene())),
//
//    DO_SHIP("doShip", Lists.newArrayList(BizSceneEnum.CREATE_SHIP.getScene(), BizSceneEnum.RE_CREATE_SHIP.getScene(), BizSceneEnum.CANCEL_SHIP.getScene(),BizSceneEnum.CHECK_SHIP.getScene())),

    ;

    /**
     * 码
     */
    private String code;

    /**
     * 场景
     */
    private List<String> sceneList;

    /**
     * 查询业务行为对应的场景列表
     * @param bizAction
     * @return
     */
    public static List<String> fetchScene(String bizAction) {
        if(StringUtils.isBlank(bizAction)){
            return Lists.newArrayList();
        }
        for (BizActionEnum value : BizActionEnum.values()) {
            if(value.getCode().equalsIgnoreCase(bizAction)) {
                return value.sceneList;
            }
        }
        return Lists.newArrayList();
    }
}
