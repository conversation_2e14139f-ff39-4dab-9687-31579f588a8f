package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * ws事件类型列举
 *
 * <AUTHOR>
 * @date 2025/01/17
 */
@Getter
@AllArgsConstructor
public enum WsEventTypeEnum {

    /**
     * 长连接的下行事件类型
     */
    LAB_WAITING_TEST_TIMEOUT("labWaitingTestTimeout", "实验室收样超时"),
    LAB_TESTING_TIMEOUT("labTestingTimeout", "实验室检测超时"),
    LAB_RECEIVE("labReceive", "实验室待收样"),
    LAB_STATUS_SYNC("labStatusSync", "实验室状态同步"),
    USER_CHECK_RESULT_OUT("userCheckResultOut", "用户检测结果已出"),
    QC_CHECK_RESULT_OUT("qcCheckResultOut", "质控单检测结果已出"),
    USER_AGREE_CONCESSION_TEST("userAgreeConcessionTest", "用户已同意让步检测"),
    ;

    /**
     * ws事件类型
     */
    private String type;

    /**
     * desc
     */
    private String desc;
}
