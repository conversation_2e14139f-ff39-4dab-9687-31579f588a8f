package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * EBS结算费用拆分维度
 *
 * <AUTHOR>
 * @date 2024/05/28
 */
@Getter
@AllArgsConstructor
public enum EbsSettleSplitTypeEnum {
    /**
     * 结算费用拆分维度
     */
    SKU_FEE("skuFee", "商品费用"),
    SERVICE_ITEM_FEE("serviceItemFee", "服务项目费用"),
    HOME_PERIOD_FEE("homePeriodFee", "上门(时段)费用"),
    PLATFORM_SERVICE_FEE("platformFee", "平台服务费"),
    ;


    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String desc;
}
