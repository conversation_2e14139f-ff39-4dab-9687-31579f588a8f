package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 审批状态
 * @Enum: XfylApprovalStatusTypeEnum
 * @Author: wangpengfei144
 * @Date: 2024/7/16
 */
@Getter
@AllArgsConstructor
public enum XfylApprovalStatusTypeEnum {
    /**
     * 审核状态 1待完善 2-待审核（待确认、审核中） 3-审核通过（已确认） 4已过期
     */
    STATUS_TO_IMPROVED(1, "待审核"),
    STATUS_TO_APPROVAL(2, "审核中"),
    STATUS_APPROVAL_PASS(3, "审核通过"),
    STATUS_APPROVAL_EXPIRE(4, "已过期"),
    STATUS_APPROVAL_REJECT(5, "审核驳回"),
    STATUS_APPROVAL_REVOKE(6, "已撤销"),
    STATUS_TO_EXCEPTION(7, "审批流异常，请取消审批并重新提交"),
    ;

    private final Integer type;

    /**
     *
     */
    private final String desc;

    /**
     * @param type
     * @return
     */
    public static String getDescOfType(Integer type) {
        if (type == null) {
            return "";
        }
        for (XfylApprovalStatusTypeEnum skuStoreEnum : XfylApprovalStatusTypeEnum.values()) {
            if (skuStoreEnum.getType().equals(type)) {
                return skuStoreEnum.getDesc();
            }
        }
        return "";
    }
}
