package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 同步到实验室状态枚举
 */
@Getter
@AllArgsConstructor
public enum MedPromiseMainStatusSyncEnum {


    /**
     * 待采样
     */
    WAITING_COLLECT(0,"待采样",Boolean.FALSE,null),
    /**
     * 已收取样本
     */
    COLLECT(2,"已收取样本（骑手或护士已取到样本）",Boolean.FALSE,null),
    /**
     * 样本配送中
     */
    DELIVERY(3,"样本配送中（骑手或护士将样本送往实验室途中）",Boolean.FALSE,null),
    /**
     * 样本已送达实验室
     */
    ARRIVE(4,"样本已送达实验室",Boolean.TRUE,401),

    /**
     * 实验室样本检查状态，表示实验室人员正在检查样本保存状态，判断是否符合上机要求。
     * 主状态为20，描述为“实验室样本检查（实验室人员检查样本保存状态，是否符合上机要求）”，有子状态，且默认子状态为202。
     */
    CHECK(20,"实验室样本检查（实验室人员检查样本保存状态，是否符合上机要求）",Boolean.TRUE,202),

    /**
     * 样本处理
     */
    DEAL(30,"样本处理（上机实验前对样本处理操作）",Boolean.TRUE,302),
    /**
     * 实验室检测中
     */
    TEST(5,"实验室检测中",Boolean.FALSE,null),
    /**
     * 实验室已出报告
     */
    REPORT(6,"实验室已出报告" ,Boolean.TRUE,602),
    /**
     * 检测单作废
     */
    INVALID(8,"检测单已作废",Boolean.FALSE,null),




    ;

    /**
     * 主状态
     */
    private final Integer callBackMainStatus;

    /**
     * 主状态描述
     */
    private final String callBackMainStatusDesc;

    /**
     * 是否有子状态
     */
    private final Boolean haveSubStatus;


    /**
     * 默认子状态
     */
    private final Integer defaultSubStatus;


    public static String getCallBackMainStatusDesc(Integer callBackMainStatus){
        if(callBackMainStatus == null){
            return null;
        }
        for(MedPromiseMainStatusSyncEnum medPromiseMainStatusSyncEnum : values()){
            if(Objects.equals(medPromiseMainStatusSyncEnum.getCallBackMainStatus(),callBackMainStatus) ){
                return medPromiseMainStatusSyncEnum.getCallBackMainStatusDesc();
            }
        }
        return null;
    }


}
