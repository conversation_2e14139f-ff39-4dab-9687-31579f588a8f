package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/8 2:37 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum BizSceneEnum {
    /** */
//    C_ANGEL_DETAIL("angelDetail", "护士/护工详情"),
//
//    B_RIDER_DETAIL("riderDetail", "骑手详情"),
//
//
//
//    /** */
//    CREATE_SHIP("createShip", "创建运单"),
//    RE_CREATE_SHIP("reCreateShip", "重新创建运单"),
//    CANCEL_SHIP("cancelShip", "取消运单"),
//    CHECK_SHIP("checkShip", "运单状态检查"),
    ;

    /**
     * 场景
     */
    private String scene;

    /**
     * 描述
     */
    private String desc;

    /**
     * 匹配业务场景
     *
     * @param bizAction
     * @return
     */
    public static Boolean matchBizBusiness(String bizAction) {
        if(StringUtils.isBlank(bizAction)) {
            return false;
        }

        for (BizSceneEnum value : BizSceneEnum.values()) {
            if(value.getScene().equalsIgnoreCase(bizAction)) {
                return true;
            }
        }

        return false;
    }
}
