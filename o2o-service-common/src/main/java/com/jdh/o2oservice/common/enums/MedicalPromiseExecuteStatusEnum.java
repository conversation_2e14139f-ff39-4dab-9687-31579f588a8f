package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/15
 */
@Getter
@AllArgsConstructor
public enum MedicalPromiseExecuteStatusEnum {



    /**
     * 执行状态为正常。
     */
    normal(1,"正常"),
    /**
     * 执行状态为异常。
     */
    error(2,"异常")




    ;

    /**
     * 状态码，表示医疗承诺执行的状态。
     */
    private  final Integer status;

    /**
     * 描述执行状态的文本信息。
     */
    private  final String desc ;
}
