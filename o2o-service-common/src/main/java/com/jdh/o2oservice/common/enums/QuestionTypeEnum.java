package com.jdh.o2oservice.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 节点类型
 */
@Getter
public enum QuestionTypeEnum {

    SELECT(1,"单选","select"),

    SELECT_MULTI(2,"多选","selectMulti"),

    INPUT(3,"填空","input"),


    UPLOAD(4,"图片/文件上传","upload"),

    SIGN(5,"签字","sign"),

    QUESTIONS(6,"题库","questions"),

    MAPPING(7,"题映射","mapping"),

    ;


    private Integer type;

    private String desc;

    private String beanName;//

    QuestionTypeEnum(Integer type, String desc,String beanName){
        this.type = type;
        this.desc = desc;
        this.beanName = beanName;
    }

    public static QuestionTypeEnum getByCode(Integer code){
        for (QuestionTypeEnum questionTypeEnum:QuestionTypeEnum.values()) {
            if(questionTypeEnum.type.equals(code)){
                return questionTypeEnum;
            }
        }
        return null;
    }


}
