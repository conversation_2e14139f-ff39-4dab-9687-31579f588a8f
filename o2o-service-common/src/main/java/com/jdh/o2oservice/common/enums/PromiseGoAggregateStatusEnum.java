package com.jdh.o2oservice.common.enums;

import lombok.Getter;


/**
 * promiseGo聚合状态
 * <AUTHOR>
 * @date 2024/04/17
 */
@Getter
public enum PromiseGoAggregateStatusEnum {
    DELIVERING("delivering", "配送阶段"),
    DELIVERY_WAITING("deliveryWaiting", "等待配送阶段"),

    TO_HOME_WAITING("toHomeWaiting", "上门等待")
    ;


    /**
     * 假期类型
     *
     * @param type 类型
     * @param desc desc
     */
    PromiseGoAggregateStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String desc;

}
