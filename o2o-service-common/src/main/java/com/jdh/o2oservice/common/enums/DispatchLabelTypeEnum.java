package com.jdh.o2oservice.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DispatchTypeEnum
 * @Description 1-即时单 2-预约单
 * <AUTHOR>
 * @Date 2024/4/22 11:16
 **/
public enum DispatchLabelTypeEnum {

    /**
     * 即时单
     */
    URGENT(1, "急","https://img14.360buyimg.com/imagetools/jfs/t1/239511/35/18117/2516/671b0da5F851979d9/641842815d0958a0.png"),

    /**
     * 预约单
     */
    NOTURGENT(2, "不急","")
    ;


    /**
     * @param type
     * @param desc
     */
    DispatchLabelTypeEnum(Integer type, String desc,String icon) {
        this.type = type;
        this.desc = desc;
        this.icon = icon;
    }

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;
    /**
     * 图标
     */
    private String icon;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }
}