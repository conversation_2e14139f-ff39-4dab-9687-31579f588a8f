package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/9/11
 * @description 工单运单拉完成执行器枚举
 */
@Getter
@AllArgsConstructor
public enum AutoCompleteProcessEnum {

    HOME_TEST("angelTest","homeTestProcessImpl"),

    HOME_CARE("angelCare","homeCareProcessImpl"),

    SLEF_TEST("selfTest","selfTestProcessImpl");

    private String businessMode;//业务模式

    private String beanName;//bean名称

    public static AutoCompleteProcessEnum getBybusinessMode(String businessMode){
        for (AutoCompleteProcessEnum autoCompleteProcessEnum: AutoCompleteProcessEnum.values()){
            if(autoCompleteProcessEnum.getBusinessMode().equals(businessMode)){
                return autoCompleteProcessEnum;
            }
        }
        return null;
    }

}
