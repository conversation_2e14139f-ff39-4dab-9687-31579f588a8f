package com.jdh.o2oservice.common.enums;

public enum VoucherOpStatusEnum {

    /**
     * 冻结
     */
    FREEZE(1, "冻结"),

    /**
     * 解冻
     */
    UN_FREEZE(2, "解冻"),

    /**
     * 作废
     */
    INVALID(3, "作废"),

    /**
     * 延期
     */
    DELAY(4, "延期"),
    ;

    /**
     * @param status
     * @param desc
     */
    VoucherOpStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String desc;

    /**
     * 获取状态
     *
     * @return {@link Integer}
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取描述
     *
     * @return {@link String}
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 描述集
     *
     * @param desc 说明
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }


}
