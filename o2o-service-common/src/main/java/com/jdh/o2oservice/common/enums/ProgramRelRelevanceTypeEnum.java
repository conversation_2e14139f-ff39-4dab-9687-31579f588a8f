package com.jdh.o2oservice.common.enums;

import lombok.Getter;

import java.io.Serializable;

/**
 * 套餐服务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/4 16:35
 */
@Getter
public enum ProgramRelRelevanceTypeEnum implements Serializable {
    /**
     * 基于某套餐升级
     */
    UPGRADE(1,"替换（基于某套餐升级）"),
    /**
     * 主套餐+加项
     */
    ADDITION(2,"加项（主套餐+加项）"),
    /**
     * 多个套餐
     */
    AGGREGATION(3,"聚合（多个套餐）");


    private final Integer typeNo;
    /**
     *
     */
    private final String name;

    /**
     * @param typeNo
     * @param name
     */
    ProgramRelRelevanceTypeEnum(Integer typeNo, String name) {
        this.typeNo = typeNo;
        this.name = name;
    }
}
