package com.jdh.o2oservice.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DispatchTypeEnum
 * @Description 1-即时单 2-预约单
 * <AUTHOR>
 * @Date 2024/4/22 11:16
 **/
public enum DispatchTypeEnum {

    /**
     * 即时单
     */
    IMMEDIATELY(1, "即时单"),

    /**
     * 预约单
     */
    APPOINTMENT(2, "预约单"),
    ;

    /**
     * TYPE_MAP
     */
    private static final Map<Integer, DispatchTypeEnum> TYPE_MAP = new HashMap<>();

    /**
     *
     */
    static {
        for (DispatchTypeEnum value : values()) {
            TYPE_MAP.put(value.type, value);
        }
    }

    /**
     * @param type
     * @param desc
     */
    DispatchTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     *
     * @param type
     * @return
     */
    public static DispatchTypeEnum valuesOf(Integer type){
        if(type == null){
            return null;
        }
        return TYPE_MAP.get(type);
    }
}