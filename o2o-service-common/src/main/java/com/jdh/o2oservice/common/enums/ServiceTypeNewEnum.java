package com.jdh.o2oservice.common.enums;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

/**
 * @ClassName ServiceTypeNewEnum
 * @Description   服务类型枚举
 * <AUTHOR>
 * @Date 2024/6/9 2:23 PM
 * @Version 1.0
 **/
public enum ServiceTypeNewEnum {

    /**
     *
     */
    KNIGHT_TEST(1, "骑手上门检测"),

    ANGEL_TEST(2, "护士上门检测"),

    ANGEL_CARE(3, "护士上门护理"),
    /**
     * 快递检测
     */
    TRANSPORT_TEST(4, "快递寄送检测"),

    /**
     * 康复师上门护理
     */
    KFS_CARE(5, "康复师上门护理"),
    ;

    /**
     * @param type
     * @param desc
     */
    ServiceTypeNewEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * 根据类型获取枚举对象
     *
     * @param type
     * @return
     */
    public static ServiceTypeNewEnum getByType(Integer type) {
        for (ServiceTypeNewEnum angelTypeEnum : ServiceTypeNewEnum.values()) {
            if (angelTypeEnum.getType().equals(type)) {
                return angelTypeEnum;
            }
        }
        return null;
    }

    /**
     * 上门护理服务类型检查
     *
     * @param serviceType
     * @return
     */
    public static boolean isHomeCare(Integer serviceType) {
        return ANGEL_CARE.getType().equals(serviceType) || KFS_CARE.getType().equals(serviceType);
    }

}
