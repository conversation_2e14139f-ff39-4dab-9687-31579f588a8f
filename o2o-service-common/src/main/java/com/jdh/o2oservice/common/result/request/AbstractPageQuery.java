package com.jdh.o2oservice.common.result.request;


/**
 * 客户端分页查询参数
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/26 2:10 下午
 * @version: 1.0
 */
public abstract class AbstractPageQuery extends AbstractQuery {

    /** */
    private int pageNum = 1;
    /** */
    private int pageSize = 10;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
