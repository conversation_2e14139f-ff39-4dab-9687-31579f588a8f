package com.jdh.o2oservice.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 预约回调通知类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/3 14:32
 */
@Getter
public enum AppointCallBackEnum {
    /**
     * 预约回调通知类型
     */
    APPOINTMENT_SUCC("10000", "预约成功"),
    APPOINTMENT_FAIL("19999", "预约失败"),
    MODIFY_SUCC("20000", "修改预约成功"),
    MODIFY_FAIL("29999", "修改预约失败"),
    CANCEL_SUCC("30000", "取消预约成功"),
    CANCEL_FAIL("39999", "取消预约失败"),
    // 客户已经抵达门店，但是服务未提供
    ARRIVED_STORE("40000", "到店成功通知"),
    CHECK_SUCCESS("40001", "已到检通知"),

    /**
     * 到家业务
     */
    // 派单成功就是预约成功，在回调上隔离开
    DISPATCH_SUCCESS("50000", "派单成功"),
    SERVICE_READY("50001", "待服务"),
    SERVICING("50002", "服务中"),
    SERVICE_COMPLETE("50003", "服务完成"),

    PROMISE_COMPLETE("60000", "履约完成"),

    /**
     * 逆向逻辑
     */
    ANGEL_TASK_CANCEL("70000", "服务者取消任务，无法完成履约"),

    ;
    /**
     * resultCode
     */
    private String code;
    /**
     * desc
     */
    private String desc;

    /**
     * AppointCallBackEnum
     * @param code
     * @param desc
     */
    AppointCallBackEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * TYPE_MAP
     */
    private static final Map<String, AppointCallBackEnum> TYPE_MAP = new HashMap<>(6);

    static {
        for (AppointCallBackEnum value : values()) {
            TYPE_MAP.put(value.code, value);
        }
    }

    /**
     * 转换
     *
     * @param code 代码
     * @return {@link AppointCallBackEnum}
     */
    public static AppointCallBackEnum convert(String code){
        if (StringUtils.isBlank(code)){
            return null;
        }
        return TYPE_MAP.get(code);
    }

}
