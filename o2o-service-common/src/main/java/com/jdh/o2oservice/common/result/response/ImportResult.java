package com.jdh.o2oservice.common.result.response;

import lombok.Data;
import org.apache.commons.collections.map.HashedMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/26 3:00 下午
 * @Description:
 */
@Data
public class ImportResult {

    /**
     * isSuccess
     */
    boolean isSuccess;

    /**
     * ImportResult
     */
    public ImportResult() {
    }

    /**
     * @param success
     * @param msg
     */
    public ImportResult(boolean success, String msg) {
        super();
        this.isSuccess = success;
        this.msg = msg;
    }

    /**
     * msg
     */
    String msg;

    /**
     * fileName
     */
    private String fileName;

    /**
     * 总行数
     */
    private int totalNum;

    /**
     * 成功数
     */
    private int successNum;

    /**
     * 导入失败数
     */
    private int failNum;

    /**
     * 空行
     */
    private int nullRowNum;
    /**
     * 重复数
     */
    private int repeatNum;


    /**
     * repeatRows
     */
    private List<String> repeatRows = new ArrayList<String>();


    /**
     * 记录未导入成功数据的
     */
    private List<Map<?, ?>> failRows = new ArrayList<>();

    /**
     * 解析失败数
     */
    private int parseFailNum;

    /**
     * 解析失败行
     */
    private List<Integer> parseFailRows = new ArrayList<Integer>();

    /**
     * importTime
     */
    private String importTime;

    /**
     * startTime
     */
    private String startTime;

    /**
     * 错误文件下载链接
     */
    private String errorFilePath;

    /**
     * parseFailRowsCause
     */
    List<String> parseFailRowsCause = new ArrayList<String>();


    /**
     * successAdd
     */
    public void successAdd() {
        successNum++;
    }

    /**
     * @param num
     */
    public void successAdd(int num) {
        successNum = successNum + num;
    }

    /**
     * failAdd
     */
    public void failAdd() {
        failNum++;
    }

    /**
     * @param num
     */
    public void failAdd(int num) {
        failNum = failNum + num;
    }

    /**
     * nullRowNumAdd
     */
    public void nullRowNumAdd() {
        nullRowNum++;
    }

    /**
     * parseFailAdd
     */
    public void parseFailAdd() {
        parseFailNum++;
    }

    /**
     * @return getNullRowNum
     */
    public int getNullRowNum() {
        return nullRowNum;
    }

    /**
     * repeatAdd
     */
    public synchronized void repeatAdd() {
        this.repeatNum++;
    }

    /**
     * @param failed
     */
    public synchronized void addFailRows(Map failed) {
        failRows.add(failed);
    }

    /**
     * @param rowNum
     * @param msg
     */
    public synchronized void addFailRows(Long rowNum, String msg) {
        Map<Long, String> failMap = new HashedMap();
        failMap.put(rowNum,msg);
        failRows.add(failMap);
    }

    /**
     * @param list
     */
    public synchronized void addAllFailRows(List<Map<Long, String>> list) {
        failRows.addAll(list);
    }

    /**
     * @param id
     */
    public synchronized void addParseFailRows(Integer id) {
        parseFailRows.add(id);
    }

    /**
     * @param venderSku
     */
    public synchronized void addRepeatRows(String venderSku) {
        repeatRows.add(venderSku);
    }

    /**
     * totalNumDubtract
     */
    public void totalNumDubtract() {
        totalNum--;
    }
}
