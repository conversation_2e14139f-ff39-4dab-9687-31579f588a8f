package com.jdh.o2oservice.common.enums;

import lombok.Getter;

@Getter
public enum ServiceRecordFlowStatusEnum {

    INIT(0, "初始"),

    FINISH(1, "提交通过"),
    HIGH_RISK(-1, "评估结果高风险"),
    ;

    ServiceRecordFlowStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private Integer status;

    private String desc;

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
