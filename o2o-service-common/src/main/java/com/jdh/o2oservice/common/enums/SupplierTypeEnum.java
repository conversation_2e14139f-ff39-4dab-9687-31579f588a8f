package com.jdh.o2oservice.common.enums;

import com.jd.matrix.report.util.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 运营商
 * @Enum: SupplierTypeEnum
 * @Author: wangpengfei144
 * @Date: 2024/10/18
 */
@Getter
@AllArgsConstructor
public enum SupplierTypeEnum {
    /**
     * 达达
     */
    DADA(1,"达达",2),

    SHUN_FENG(2,"顺丰",4),

/*
    注释代码 目前没用到
    ANGEL(2,"护士",null),
    *//**
     * 闪送
     *//*
    SHAN_SONG(3,"闪送",3),

    SHUN_FENG(4,"顺丰",4)*/
    ;

    ;

    /**
     *
     */
    private final Integer type;
    /**
     *
     */
    private final String desc;
    /**
     *
     */
    private final Integer delivery;

    public static Integer  getDelivery(Integer type) {
        if (type == null) {
            return null;
        }
         for (SupplierTypeEnum typeEnum : SupplierTypeEnum.values()) {
              if (typeEnum.getType().intValue() == type.intValue()) {
                   return typeEnum.delivery;
              }
         }
         //默认自配送
         return 1;
    }
}
