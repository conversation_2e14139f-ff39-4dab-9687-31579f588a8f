package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 申请状态enum
 * @Enum: BillRecordStatusEnum
 * @Author: wangpengfei144
 * @Date: 2024/7/17
 */
@Getter
@AllArgsConstructor
public enum BillRecordStatusEnum {
    /**
     * 账单状态
     */
    WAIT_STORE_CONFIRM(0,"待实验室确认"),
    WAIT_JD_CONFIRM(1,"待京东确认"),
    CONFIRMED(2,"已确认"),

    ;

    /**
     *
     */
    private final Integer type;

    /**
     *
     */
    private final String desc;

    /**
     * 转成map
     * @return
     */
    public static Map<Integer,String> getDetailToMap(){
        Map<Integer,String> map = new HashMap<>();
        for (BillRecordStatusEnum billRecordStatusEnum: BillRecordStatusEnum.values()){
            map.put(billRecordStatusEnum.getType(),billRecordStatusEnum.getDesc());
        }
        return map;
    }
}

