package com.jdh.o2oservice.common.result.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;

import java.io.Serializable;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/26 10:21 上午
 * @Description:
 */
public class BaseExcelModel implements IExcelModel, Serializable, IExcelDataModel {


    /**
     * errorMsg
     */
    @Excel(name = "错误信息")
    private String errorMsg;

    /**
     * rowNum
     */
    private int rowNum;

    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }
}
