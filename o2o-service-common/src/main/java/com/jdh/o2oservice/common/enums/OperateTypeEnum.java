package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Description: 操作类型枚举
 * Enum: OperateTypeEnum
 * @date 2024-08-16 11:28
 * <AUTHOR>
 */
public enum OperateTypeEnum {

    ADD("ADD","新增操作"),
    UPDATE("UPDATE","修改操作"),
    DELETE("DELETE","删除操作"),
    ;

    /**
     *
     */
    OperateTypeEnum(String operateType,String operateDesc) {
        this.operateType = operateType;
        this.operateDesc = operateDesc;
    }

    /**
     *
     * @param operateType
     * @return
     */
    public static OperateTypeEnum getEnumByCode(String operateType){
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.operateType.equals(operateType)) {
                return operateTypeEnum;
            }
        }
        return null;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getOperateDesc() {
        return operateDesc;
    }

    public void setOperateDesc(String operateDesc) {
        this.operateDesc = operateDesc;
    }

    /**
     * 操作类型名称
     */
    private String operateType;

    /**
     * 操作类型描述
     */
    private String operateDesc;


}
