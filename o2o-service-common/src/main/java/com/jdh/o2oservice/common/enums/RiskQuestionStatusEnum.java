package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * 风险评估单问题问答情况
 * <AUTHOR>
 * @date 2025-06-26 16:08
 */

@Getter
@AllArgsConstructor
public enum RiskQuestionStatusEnum {
    /**
     * 待回答
     */
    WAITING_ANSWER(1,"待回答"),

    /**
     * 已回答。
     */
    ANSWER(2,"已回答"),

    /**
     * 作废。
     */
    INVALID(3, "无效");



    /**
     * 风险评估单状态的唯一标识符。
     */
    private final Integer status;


    /**
     * 风险评估单状态的描述信息。
     */
    private final String desc;



    /**
     * 获取无效列表
     * @return 包含通过、拒绝和无效状态的列表
     */
    public static Set<Integer> getInvalidStatus(){
        return Sets.newHashSet(RiskQuestionStatusEnum.INVALID.getStatus());
    }


    /**
     * 根据状态码获取问答状态
     * @param status 状态码。
     * @return 对应状态码的状态描述，若无匹配状态则返回 null。
     */
    public static String getDescByStatus(Integer status){
        for(RiskQuestionStatusEnum statusEnum : RiskQuestionStatusEnum.values()){
            if(Objects.equals(status,statusEnum.getStatus())){
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}
