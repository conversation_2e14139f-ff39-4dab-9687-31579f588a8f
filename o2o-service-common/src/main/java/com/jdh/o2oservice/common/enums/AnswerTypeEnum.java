package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2025-07-28 10:22
 */

@Getter
@AllArgsConstructor
public enum AnswerTypeEnum {


    /**
     * 表示AI异常判读的枚举值。
     */
    AI(1,"AI异常判读"),

    /**
     * 表示规则异常判读的枚举值。
     */
    RULE(2,"规则异常判读"),



    ;

    /**
     * 答案类型的唯一标识符。
     */
    private final Integer answerType;

    /**
     * 答案类型的描述信息。
     */
    private final String answerTypeDesc;


    public static String getDescByType(Integer answerType) {
        if(answerType == null) {
            return null;
        }
        for (AnswerTypeEnum answerTypeEnum : AnswerTypeEnum.values()) {
            if (answerTypeEnum.getAnswerType().equals(answerType)) {
                return answerTypeEnum.getAnswerTypeDesc();
            }
        }
        return null;
    }

}
