package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HoleTypeEnum {


    /**
     * 孔位缩略图类型。
     */
    HOLE_THUMBNAIL(1,"孔位缩略图"),


    /**
     * 试剂分布图类型。
     */
    REAGENT_DISTRIBUTION_MAP(2,"试剂分布图")



    ;

    /**
     * 孔类型
     */
    private final Integer holeType;

    /**
     * 孔类型的描述信息。
     */
    private final String holeTypeDesc;

}
