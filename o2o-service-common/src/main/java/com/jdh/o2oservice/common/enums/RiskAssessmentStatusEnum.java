package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * 风险评估单状态枚举
 */
@Getter
@AllArgsConstructor
public enum RiskAssessmentStatusEnum {


    /**
     * 表示风险评估单状态为待评估。
     */
    WAITING_ASS(1,"待评估"),

    /**
     * 表示风险评估单状态已通过审核。
     */
    PASS(2,"通过"),

    /**
     * 表示风险评估单状态已被驳回。
     */
    REFUSE(3, "驳回"),

    /**
     * 表示风险评估单状态无效。
     */
    INVALID(4, "无效"),


;

    /**
     * 风险评估单状态的唯一标识符。
     */
    private final Integer status;


    /**
     * 风险评估单状态的描述信息。
     */
    private final String desc;


    /**
     * 获取最终评估状态列表
     * @return 包含通过、拒绝的列表
     */
    public static Set<Integer> getFinallyAssStatus(){
        return Sets.newHashSet(RiskAssessmentStatusEnum.PASS.getStatus(),
                RiskAssessmentStatusEnum.REFUSE.getStatus(),
                RiskAssessmentStatusEnum.INVALID.getStatus()
        );
    }


    /**
     * 获取最终状态列表
     * @return 包含通过、拒绝和无效状态的列表
     */
    public static Set<Integer> getFinallyStatus(){
        return Sets.newHashSet(RiskAssessmentStatusEnum.PASS.getStatus(), RiskAssessmentStatusEnum.REFUSE.getStatus(), RiskAssessmentStatusEnum.INVALID.getStatus());
    }

    /**
     * 获取最终状态列表
     * @return 包含通过、拒绝和无效状态的列表
     */
    public static Set<Integer> getFinishStatus(){
        return Sets.newHashSet(RiskAssessmentStatusEnum.PASS.getStatus(), RiskAssessmentStatusEnum.REFUSE.getStatus());
    }

    /**
     * 根据状态码获取风险评估状态描述。
     * @param status 状态码。
     * @return 对应状态码的状态描述，若无匹配状态则返回 null。
     */
    public static String getDescByStatus(Integer status){
        for(RiskAssessmentStatusEnum statusEnum : RiskAssessmentStatusEnum.values()){
            if(Objects.equals(status,statusEnum.getStatus())){
                return statusEnum.getDesc();
            }
        }
        return null;
    }


}
