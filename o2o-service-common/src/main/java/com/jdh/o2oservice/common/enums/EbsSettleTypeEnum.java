package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * EBS结算类型
 *
 * <AUTHOR>
 * @date 2024/05/28
 */
@Getter
@AllArgsConstructor
public enum EbsSettleTypeEnum {

    /**
     * 结算类型
     */
    INCOME(1,"income", "收入"),
    EXPEND(2,"expend",  "支出"),
    REFUND(-1,"refund",  "冲收入"),
    ;


    /**
     * 类型
     */
    private Integer type;

    /**
     * 类型code码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;
}
