package com.jdh.o2oservice.common.enums;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 节点类型
 */
public enum QuestionGroupTypeEnum {

    PREDANGERASSESSMENT("preDangerAssessment","评估师评估"),

    PRERECEIVEASSESSMENT("preReceiveAssessment","接单前评估"),

    PRESERVICEASSESSMENT("preServiceAssessment","服务前评估"),

    PRESERVICESIGNATURE("preServiceSignature","服务前签字"),

    SUPPLYVERIFICATION("supplyVerification","耗材确认"),

    SERVICERECORD("serviceRecord","服务记录"),

    HEALTHEDU("healthEdu","健康宣教"),

    RECORDUPLOAD("recordUpload","记录上传"),

    SIGNCONFIRM("signConfirm","签字确认"),


    ;


    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    QuestionGroupTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }


}
