package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 曲线状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReportCurveStatusEnum {


    /**
     * 表示曲线状态为阴性的枚举值。
     */
    NORMAL(0,"阴性","1"),

    /**
     * 表示曲线状态为弱阳性的枚举值。
     */
    WEAK_POSITIVE(1,"弱阳性","7"),

    /**
     * 表示曲线状态为阳性的枚举值。
     */
    POSITIVE(2,"阳性","0")


    ;
    /**
     * 曲线状态
     */
    private final Integer curveStatus;

    /**
     * 曲线状态的描述信息。
     */
    private final String curveStatusDesc;


    private final String sxResult;



    public static String getCurveStatusDesc(Integer curveStatus) {
        for (ReportCurveStatusEnum value : ReportCurveStatusEnum.values()) {
            if (Objects.equals(value.curveStatus, curveStatus)) {
                return value.curveStatusDesc;
            }
        }

        return null;
    }


    public static Integer getCurveStatus(String curveStatusDesc) {
        for (ReportCurveStatusEnum value : ReportCurveStatusEnum.values()) {
            if (Objects.equals(value.curveStatusDesc, curveStatusDesc)) {
                return value.curveStatus;
            }
        }
        return null;
    }

    public static Integer getCureStatusBySxResult(String sxResult) {
        for (ReportCurveStatusEnum value : ReportCurveStatusEnum.values()) {
            if (StringUtils.equals(value.sxResult, sxResult)) {
                return value.curveStatus;
            }
        }
        return null;
    }

}
