package com.jdh.o2oservice.common.ext;

import com.jdh.o2oservice.common.result.response.BaseResponse;
import com.jdh.o2oservice.common.result.response.Response;
import lombok.Data;

/**
 * @ClassName:ExtResponse
 * @Description: 扩展点返回接口
 * @Author: yaoqinghai
 * @Date: 2024/5/14 20:24
 * @Vserion: 1.0
 **/
@Data
public class ExtResponse<T> extends BaseResponse {

    /**
     * 处理结果
     */
    private T data;

    /**
     * 构建成功结果
     *
     * @param data
     * @return
     * @param <T>
     */
    public static <T> ExtResponse<T> buildSuccess(T data){
        ExtResponse<T> extResponse = new ExtResponse<>();
        extResponse.setData(data);
        extResponse.setCode(SUCCESS);
        extResponse.setMsg(SUCCESS_MSG);
        return extResponse;
    }

    /**
     * 构建失败结果
     * @param data
     * @return
     * @param <T>
     */
    public static <T> ExtResponse<T> buildFail(T data){
        ExtResponse<T> extResponse = new ExtResponse<>();
        extResponse.setData(data);
        extResponse.setCode(UNKNOWN_ERROR);
        extResponse.setMsg(UNKNOWN_ERROR_MSG);
        return extResponse;
    }
}
