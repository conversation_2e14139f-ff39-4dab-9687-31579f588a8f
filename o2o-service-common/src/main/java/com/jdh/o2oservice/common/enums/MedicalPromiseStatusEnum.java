package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Description: 检测单状态枚举
 * Enum: MedicalPromiseStatusEnum
 * @date 2024-04-16 11:28
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MedicalPromiseStatusEnum {

    /**
     * 待采集
     */
    WAIT_COLLECTED(1, "1","待采集"),

    /**
     * 已采集
     */
    COLLECTED(2,"2","已采集"),

    /**
     * 待收样
     */
    MEDICAL_CHECK_WAIT(3,"3","待收样"),

    /**
     * 检测中
     */
    MEDICAL_CHECK_ING(4,"4","检测中"),

    /**
     * 已完成
     */
    COMPLETED(6,"6", "已完成"),


    /**
     * 已作废
     */
    INVALID(8,"8", "已作废"),

    ;
    /**
     * 检测单状态
     */
    private final Integer status;

    private final String statusStr;
    /**
     * 检测单状态描述
     */
    private final String statusDesc;

    /**
     * 不可重新绑码的状态
     */
    public static final Set<Integer> UN_BIND_STATUS = Sets.newHashSet(INVALID.status);


    /**
     * 派发实验室 检测单状态黑名单
     * @return
     */
    public static Set<Integer> getDispatchBlackStatus(){
        Set<Integer> blackStatusSet = new HashSet<>();
        blackStatusSet.add(MedicalPromiseStatusEnum.COMPLETED.getStatus());
        blackStatusSet.add(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
        blackStatusSet.add(MedicalPromiseStatusEnum.INVALID.getStatus());
        return blackStatusSet;
    }

    private static final Map<Integer, MedicalPromiseStatusEnum> TYPE_MAP = Maps.newHashMap();
    static {
        for (MedicalPromiseStatusEnum value : values()) {
            TYPE_MAP.put(value.status, value);
        }
    }

    public static MedicalPromiseStatusEnum getByType(Integer status){
        if (Objects.isNull(status)){
            return null;
        }
        return TYPE_MAP.get(status);

    }

    public static String getDescByStatus(Integer status){
        if (Objects.isNull(status)){
            return "";
        }
        for (MedicalPromiseStatusEnum medicalPromiseStatusEnum : MedicalPromiseStatusEnum.values()){
            if (Objects.equals(status,medicalPromiseStatusEnum.getStatus())){
                return medicalPromiseStatusEnum.getStatusDesc();
            }
        }
        return "";
    }

    /**
     * 需要变更绑码状态的检测单状态
     *
     * @param medicalPromiseStatus
     * @return
     */
    public static Boolean needChangeBindStatus(Integer medicalPromiseStatus){
        return WAIT_COLLECTED.getStatus().equals(medicalPromiseStatus) || COLLECTED.getStatus().equals(medicalPromiseStatus);
    }

    /**
     * 报告是否已出
     * @return
     */
    public static boolean isReportOut(Integer status){
        if( COMPLETED.getStatus().equals(status)){
            return true;
        }
        return false;
    }
}
