package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品扩展信息枚举
 *
 * <AUTHOR>
 * @date 2025/03/06
 */
@Getter
@AllArgsConstructor
public enum SkuExtendAttributeEnum {

    HIGH_QUALITY_STORE_ID("highQualityStoreId", "高优匹配实验室")
    ;

    /**
     * resultCode
     */
    private final String attribute;
    /**
     * desc
     */
    private final String attributeDesc;
}
