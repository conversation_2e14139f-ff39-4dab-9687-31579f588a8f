package com.jdh.o2oservice.common.enums;

/**
 * 业务分类枚举
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
public enum BizCategorySceneEnum {

    STANDARD_ITEM_CATEGORY("standardItemCategory", "标准项目分类");

    /**
     * 分类场景编码
     */
    private final String code;

    /**
     * 分类场景名称
     */
    private final String name;

    BizCategorySceneEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
