package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自动化场景
 * @date 2025-05-16 14:06
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AutoBotsSceneEnum {

    /**
     * 数字人报告解读场景。
     * <p>
     * 该场景用于处理与数字人报告解读相关的业务逻辑。
     */
    DIGITAL_REPORT_ANALYSE("digitalReportAnalyse","数字人报告解读")



    ;


    /**
     * 自动化场景的唯一标识符。
     */
    private String scene;

    /**
     * 自动化场景的描述信息。
     */
    private String desc;
}
