package com.jdh.o2oservice.application.product.service.impl;

import org.junit.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;

import static org.junit.Assert.*;

/**
 * 测试多边形重叠检测功能
 */
public class ProductApplicationImplPolygonTest {

    private final GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
    private final ProductApplicationImpl productApplication = new ProductApplicationImpl();

    @Test
    public void testEnsureClosed_WithOpenPolygon() {
        // 测试开放多边形（未闭合）
        Coordinate[] openCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        
        Coordinate[] closedCoords = ProductApplicationImpl.ensureClosed(openCoords);
        
        // 验证结果是闭合的
        assertEquals(5, closedCoords.length);
        assertTrue(closedCoords[0].equals2D(closedCoords[closedCoords.length - 1]));
    }

    @Test
    public void testEnsureClosed_WithClosedPolygon() {
        // 测试已闭合多边形
        Coordinate[] closedCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1),
            new Coordinate(0, 0)
        };
        
        Coordinate[] result = ProductApplicationImpl.ensureClosed(closedCoords);
        
        // 验证结果长度不变
        assertEquals(5, result.length);
        assertTrue(result[0].equals2D(result[result.length - 1]));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testEnsureClosed_WithInsufficientPoints() {
        // 测试点数不足的情况
        Coordinate[] insufficientCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0)
        };
        
        ProductApplicationImpl.ensureClosed(insufficientCoords);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testEnsureClosed_WithNullCoords() {
        // 测试空坐标数组
        ProductApplicationImpl.ensureClosed(null);
    }

    @Test
    public void testPolygonOverlap_WithValidPolygons() {
        // 测试两个重叠的多边形
        Coordinate[] polygon1 = {
            new Coordinate(0, 0),
            new Coordinate(2, 0),
            new Coordinate(2, 2),
            new Coordinate(0, 2)
        };
        
        Coordinate[] polygon2 = {
            new Coordinate(1, 1),
            new Coordinate(3, 1),
            new Coordinate(3, 3),
            new Coordinate(1, 3)
        };
        
        boolean overlaps = productApplication.polygonOverlap(geometryFactory, polygon1, polygon2);
        assertTrue("两个重叠的多边形应该返回true", overlaps);
    }

    @Test
    public void testPolygonOverlap_WithNonOverlappingPolygons() {
        // 测试两个不重叠的多边形
        Coordinate[] polygon1 = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        
        Coordinate[] polygon2 = {
            new Coordinate(2, 2),
            new Coordinate(3, 2),
            new Coordinate(3, 3),
            new Coordinate(2, 3)
        };
        
        boolean overlaps = productApplication.polygonOverlap(geometryFactory, polygon1, polygon2);
        assertFalse("两个不重叠的多边形应该返回false", overlaps);
    }

    @Test
    public void testPolygonOverlap_WithInvalidCoordinates() {
        // 测试无效坐标的情况
        Coordinate[] invalidCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0)
        };
        
        Coordinate[] validCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        
        // 应该返回false而不是抛出异常
        boolean overlaps = productApplication.polygonOverlap(geometryFactory, invalidCoords, validCoords);
        assertFalse("无效坐标应该返回false", overlaps);
    }
}
