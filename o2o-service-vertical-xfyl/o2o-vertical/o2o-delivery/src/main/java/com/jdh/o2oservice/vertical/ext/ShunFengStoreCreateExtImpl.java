/*
package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.domain.angel.core.ext.CreateThirdStoreExt;
import com.jdh.o2oservice.domain.angel.core.ext.param.CreateThirdStoreParam;
import com.jdh.o2oservice.vertical.DeliveryApp;
import com.jdh.o2oservice.vertical.dto.ShunFengAddShopRequest;
import com.jdh.o2oservice.vertical.dto.ShunFengVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

*/
/**
 * @ClassName ShunFengStoreCreateExtImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/19 4:21 PM
 * @Version 1.0
 **//*

@Extension(code = DeliveryApp.CODE)
@Slf4j
public class ShunFengStoreCreateExtImpl implements CreateThirdStoreExt {

    @Autowired
    private DuccConfig duccConfig;


    */
/**
     * 创建顺丰门店
     * @param createThirdStoreParam
     * @return
     *//*

    @Override
    public ExtResponse<Boolean> createThirdStore(CreateThirdStoreParam createThirdStoreParam) {
        log.info("我是创建门店扩展点->创建顺丰门店");
        ShunFengVO shunFengVO = this.createShunFengShop(createThirdStoreParam);
        if(shunFengVO==null){
            log.info("我是创建门店扩展点,门店创建失败!!!");
            return ExtResponse.buildSuccess(false);
        }
        log.info("我是创建门店扩展点,门店创建成功daDaShopVO={}", JSON.toJSONString(shunFengVO));
        return ExtResponse.buildSuccess(true);
    }

    */
/**
     * 创建顺丰门店
     * @param createThirdStoreParam
     * @return
     *//*

    private ShunFengVO createShunFengShop(CreateThirdStoreParam createThirdStoreParam){
        String methodUrl = "/open/api/external/createshop";
        ShunFengAddShopRequest shunFengAddShopRequest = new ShunFengAddShopRequest();
        //第三方店铺id
        shunFengAddShopRequest.setOutShopId(createThirdStoreParam.getAngelStationId()+"");
        //开发者账号
        shunFengAddShopRequest.setDevId(duccConfig.getShunFengConfig().getDevId());
        //店铺所属商家id
        shunFengAddShopRequest.setSupplierId(duccConfig.getShunFengConfig().getSupplierId());

        return null;
    }

}
*/
