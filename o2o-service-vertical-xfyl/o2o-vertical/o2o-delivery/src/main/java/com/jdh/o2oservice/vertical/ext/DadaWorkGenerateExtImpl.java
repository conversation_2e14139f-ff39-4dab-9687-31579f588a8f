package com.jdh.o2oservice.vertical.ext;

import com.jd.fastjson.JSON;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.reponse.AngelWorkPlanTime;
import com.jdh.o2oservice.ext.work.WorkGenerateExt;
import com.jdh.o2oservice.ext.work.param.WorkBizParamCheckParam;
import com.jdh.o2oservice.vertical.DeliveryApp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName:WorkGenerateExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/24 10:29
 * @Vserion: 1.0
 **/
@Extension(code = DeliveryApp.CODE)
@Slf4j
public class DadaWorkGenerateExtImpl implements WorkGenerateExt {

    @Resource
    private DuccConfig duccConfig;

    private final static Integer DELAY_TIME_BUFFER = 10;

    /**
     * 创建工单后置处理逻辑
     *
     * @param workBizParamCheckParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> bizParamCheck(WorkBizParamCheckParam workBizParamCheckParam) {
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 计算工单计划时间
     *
     * @param planTimeCalculate 业务参数
     * @return
     */
    @Override
    public ExtResponse<AngelWorkPlanTime> planTimeCalculate(WorkBizParamCheckParam planTimeCalculate) {
        log.info("[DadaWorkGenerateExtImpl -> planTimeCalculate],planTimeCalculate={}", JSON.toJSONString(planTimeCalculate));
        //用户预约开始时间
        Date expectStartTime = planTimeCalculate.getWorkStartTime();
        if (Objects.isNull(expectStartTime)) {
            return ExtResponse.buildSuccess(null);
        }
        //计划预约运单时间
        Integer dadaDelayPublishTime = duccConfig.getDadaDelayPublishTime();
        LocalDateTime localDateTime = TimeUtils.dateToLocalDateTime(expectStartTime);
        LocalDateTime expectPaiDateTime = localDateTime.minusMinutes(dadaDelayPublishTime);

        //预约运单gap时间
        LocalDateTime nowLocalDateTime = LocalDateTime.now();
        LocalDateTime paiGapDateTime = nowLocalDateTime.plusMinutes(DELAY_TIME_BUFFER);

        //预约时间
        AngelWorkPlanTime angelWorkPlanTime = new AngelWorkPlanTime();
        if (expectPaiDateTime.isAfter(paiGapDateTime)) {
            angelWorkPlanTime.setPlanCallTime(TimeUtils.localDateTimeToDate(expectPaiDateTime));
            return ExtResponse.buildSuccess(angelWorkPlanTime);
        }
        angelWorkPlanTime.setPlanCallTime(TimeUtils.localDateTimeToDate(nowLocalDateTime));
        return ExtResponse.buildSuccess(angelWorkPlanTime);
    }
}
