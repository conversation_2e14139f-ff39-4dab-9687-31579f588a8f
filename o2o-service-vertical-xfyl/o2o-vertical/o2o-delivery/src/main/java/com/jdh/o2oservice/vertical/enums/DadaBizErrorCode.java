package com.jdh.o2oservice.vertical.enums;

import com.jdh.o2oservice.base.enums.AlarmLevelEnum;
import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;

import java.util.Objects;

/**
 * @InterfaceName:DadaBizErrorCode
 * @Description: 达达运力错误码
 *
 * @Author: yaoqing<PERSON>
 * @Date: 2024/4/18 16:54
 * @Vserion: 1.0
 **/
public enum DadaBizErrorCode implements AbstractErrorCode {

    /**
     *
     */
    VERIFY_SIGNATURE_ERROR("DD10001", "达达回传消息验签失败!"),

    DELIVER_STATUS_INVALID("DD10002", "当前状态值不合法!"),

    DELIVER_TARGET_STATUS_INVALID("DD10003", "目标状态不允许变更!"),

    VERIFY_SIGNATURE_PARAM_EMPTY("DD10004", "验证签名参数为空!"),

    ;


    DadaBizErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    DadaBizErrorCode(String code, String description, AlarmLevelEnum alarmLevelEnum) {
        this.code = code;
        this.description = description;
        this.alarmLevelEnum = alarmLevelEnum;
    }

    private String code;

    private String description;

    /**
     * 报警级别
     */
    private AlarmLevelEnum alarmLevelEnum;

    /**
     * 错误码值
     *
     * @return
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 错误码描述信息
     *
     * @return
     */
    @Override
    public String getDescription() {
        return description;
    }

    /**
     * 获取报警级别
     *
     * @return
     */
    @Override
    public AlarmLevelEnum getAlarmLevel() {
        if(Objects.isNull(alarmLevelEnum)) {
            return AlarmLevelEnum.WARING;
        }
        return alarmLevelEnum;
    }
}
