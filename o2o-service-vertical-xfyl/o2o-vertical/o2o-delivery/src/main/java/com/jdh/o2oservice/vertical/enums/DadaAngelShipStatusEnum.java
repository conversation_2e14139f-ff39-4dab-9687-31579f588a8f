package com.jdh.o2oservice.vertical.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 达达运单状态枚举
 * @ClassName:AngelShipStatusEnum
 * @Description: 运单状态
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2023/11/25 15:04
 * @Vserion: 1.0
 **/
@Getter
public enum DadaAngelShipStatusEnum {

    /**
     *
     */
    SHIP_ORDER_INIT(0, "初始状态", Sets.newHashSet(1, 2, 3, 4, 5, 8, 9, 10, 100, 1000)),

    WAITING_RECEIVE_ORDER(1, "待接单", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100, 1000)),

    WAITING_RECEIVE_GOODS(2, "待取货", Sets.newHashSet(3, 4, 5, 8, 9, 10, 100)),

    DELIVERING_GOODS(3, "配送中", Sets.newHashSet(4, 5, 8, 9, 10)),

    ORDER_SHIP_FINISH(4, "已完成", Sets.newHashSet()),

    ORDER_SHIP_CANCEL(5, "已取消", Sets.newHashSet(5)),

    DISPATCH_SHIP_ORDER(8, "派单", Sets.newHashSet(2, 3, 4, 5, 9, 10, 100)),

    DELIVERED_BACK(9, "妥投异常之物品返回中", Sets.newHashSet(4, 10)),

    DELIVERED_BACK_FINISH(10, "妥投异常之物品返回完成", Sets.newHashSet()),

    KNIGHT_REACH_STORE(100, "骑士到店", Sets.newHashSet(3, 4, 5, 8, 9, 10)),

    CREATE_ORDER_FAIL(1000, "创建达达运单失败", Sets.newHashSet()),

    ;


    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 运单状态描述
     */
    private String shipStatusDesc;

    /**
     * 下一状态集合
     */
    private Set<Integer> nextStatusSet;

    DadaAngelShipStatusEnum(Integer shipStatus, String shipStatusDesc, Set<Integer> nextStatusSet) {
        this.shipStatus = shipStatus;
        this.shipStatusDesc = shipStatusDesc;
        this.nextStatusSet = nextStatusSet;
    }

    private static List<Integer> refundShipStatusSet = Lists.newArrayList(DadaAngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus(), DadaAngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), DadaAngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());
    private static List<Integer> illegalShipStatusSet = Lists.newArrayList(DadaAngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), DadaAngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchRefundStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return refundShipStatusSet.contains(status);
    }

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchShipIllegalStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return illegalShipStatusSet.contains(status);
    }

    /**
     * 骑手接单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchReceiveOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return DadaAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(status);
    }

    /**
     * 派单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchCallOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return DadaAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(status);
    }

    /**
     * 匹配运单id
     *
     * @param shipStatus
     * @return
     */
    public static DadaAngelShipStatusEnum matchShipStatusEnum(Integer shipStatus){
        for (DadaAngelShipStatusEnum value : DadaAngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(shipStatus)){
                return value;
            }
        }
        return null;
    }

    /**
     * 判断当前运单状态是否可以取消
     *
     * @param shipStatus
     * @return
     */
    public static boolean matchCanCancelStatus(Integer shipStatus) {
        List<Integer> shipStatusList = Lists.newArrayList(DadaAngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(), DadaAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus(),
                DadaAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(),
                DadaAngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus());
        if(shipStatusList.contains(shipStatus)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 判断正向状态
     * @param shipStatus
     * @return
     */
    public static Boolean checkForwardStatus(Integer shipStatus){
        return DadaAngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus().equals(shipStatus) ||  DadaAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(shipStatus)
                ||  DadaAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(shipStatus) ||  DadaAngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(shipStatus)
                ||  DadaAngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus().equals(shipStatus);
    }
}
