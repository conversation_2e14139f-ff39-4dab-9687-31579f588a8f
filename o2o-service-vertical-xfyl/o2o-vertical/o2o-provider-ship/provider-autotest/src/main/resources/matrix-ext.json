{"bizCode": "jdh-o2o-autotest", "version": "1.0.0", "system": "default", "baseScanPackage": "com.jdh.o2oservice.vertical", "extMappings": [{"extCode": "EXT_JDH_O2O_ANGEL_STATION_CreateThirdStoreExt_createThirdStore", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_STATION_UpdateThirdStoreExt_updateThirdStore", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_STATION_QueryThirdStoreExt_queryThirdStore", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_callTransfer", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_reCallTransfer", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryStatusBackExt_verifySignature", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_cancelTransfer", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryTrackExt_queryDeliveryTrack", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_CallbackExt_shipInfoConvert", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_getShipOrderDetail", "priority": [{"code": "jdh-o2o-autotest", "type": "Y"}]}]}