package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.domain.angel.core.ext.CreateThirdStoreExt;
import com.jdh.o2oservice.domain.angel.core.ext.dto.ThirdStoreInfoDto;
import com.jdh.o2oservice.domain.angel.core.ext.param.CreateThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.QueryThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.UpdateThirdStoreParam;
import com.jdh.o2oservice.vertical.AutoTestApp;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName AutoTestStoreCreateExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/2/13 15:56
 **/
@Extension(code = AutoTestApp.CODE)
@Slf4j
public class AutoTestStoreCreateExtImpl implements CreateThirdStoreExt {

    @Override
    public ExtResponse<Boolean> createThirdStore(CreateThirdStoreParam createThirdStoreParam) {
        log.info("AutoTestStoreCreateExtImpl我是创建门店扩展点,我执行了");
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 查询第三方门店扩展点
     *
     * @param queryThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<ThirdStoreInfoDto> queryThirdStore(QueryThirdStoreParam queryThirdStoreParam) {
        log.info("AutoTestStoreCreateExtImpl我是查询门店扩展点,我执行了");
        return ExtResponse.buildSuccess(null);
    }

    /**
     * 创建第三方门店扩展点
     *
     * @param updateThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> updateThirdStore(UpdateThirdStoreParam updateThirdStoreParam) {
        log.info("AutoTestStoreCreateExtImpl我是更新门店扩展点,我执行了");
        return ExtResponse.buildSuccess(true);
    }
}