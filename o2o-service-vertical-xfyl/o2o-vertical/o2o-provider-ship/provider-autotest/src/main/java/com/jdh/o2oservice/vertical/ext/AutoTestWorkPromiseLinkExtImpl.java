package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.work.WorkPromiseLinkExt;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import com.jdh.o2oservice.vertical.AutoTestApp;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName AutoTestWorkPromiseLinkExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/2/13 15:57
 **/
@Extension(code = AutoTestApp.CODE)
@Slf4j
public class AutoTestWorkPromiseLinkExtImpl implements WorkPromiseLinkExt {

    /**
     * 查询轨迹链接
     *
     * @param trackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryTrackResponse> getTransferTrack(TrackParam trackParam) {
        DeliveryTrackResponse response = new DeliveryTrackResponse();
        log.info("[AutoTestWorkPromiseLinkExtImpl.getTransferTrack],response={}", JSON.toJSONString(response));
        return ExtResponse.buildSuccess(response);
    }

    /**
     * 查询服务者实时轨迹
     *
     * @param realTrackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryRealTrackResponse> getTransferRealTrack(RealTrackParam realTrackParam) {
        log.info("[AutoTestWorkPromiseLinkExtImpl -> getTransferRealTrack],查询骑手实时位置!realTrackParam={}", JSON.toJSONString(realTrackParam));
        DeliveryRealTrackResponse realTrackResponse = new DeliveryRealTrackResponse();
        return ExtResponse.buildSuccess(realTrackResponse);
    }
}