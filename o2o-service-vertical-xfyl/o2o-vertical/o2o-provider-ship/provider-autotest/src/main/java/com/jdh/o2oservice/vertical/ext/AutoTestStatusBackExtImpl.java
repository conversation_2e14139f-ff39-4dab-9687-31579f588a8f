package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.DeliveryStatusBackExt;
import com.jdh.o2oservice.vertical.AutoTestApp;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName AutoTestStatusBackExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/2/13 15:55
 **/
@Extension(code = AutoTestApp.CODE)
@Slf4j
public class AutoTestStatusBackExtImpl implements DeliveryStatusBackExt {

    /**
     * 签名验证
     *
     * @param verifyParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> verifySignature(String verifyParam) {
        return ExtResponse.buildSuccess(true);
    }
}