package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.vertical.AutoTestApp;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AutoTestCreateShipExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/2/13 15:45
 **/
@Extension(code = AutoTestApp.CODE)
@Slf4j
public class AutoTestCreateShipExtImpl implements CreateShipExt {

    private static final List<Integer> STATUS_LIST = Lists.newArrayList(2, 100, 3);

    /**
     *
     * @param createDadaShip
     * @return
     */
    @Override
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShip, Date planOutTime, String providerShopNo) {
        log.info("[AutoTestCreateShipExtImpl -> callTransfer],createAutotestShip={}", JSON.toJSONString(createDadaShip));
        CreateShipExtResponse createShipExtResponse = new CreateShipExtResponse();
        createShipExtResponse.setTotalDistance(BigDecimal.ZERO);
        createShipExtResponse.setEstimateCallTime(TimeUtils.localDateTimeToDate(LocalDateTime.now().withSecond(0)));

        CreateShipExtFeeResponse extFeeResponse = new CreateShipExtFeeResponse();
        extFeeResponse.setAmount(BigDecimal.ZERO);
        extFeeResponse.setTotalAmount(BigDecimal.ZERO);
        extFeeResponse.setDiscountAmount(BigDecimal.ZERO);
        createShipExtResponse.setExtFeeResponse(extFeeResponse);
        createShipExtResponse.setOutOrderNo(SpringUtil.getBean(GenerateIdFactory.class).getIdStr());
        return ExtResponse.buildSuccess(createShipExtResponse);
    }

    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        log.info("[AutoTestCreateShipExtImpl -> reCallTransfer],createDadaShipParam={}", JSON.toJSONString(createDadaShipParam));
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 取消运力
     *
     * @param cancelDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        log.info("[AutoTestCreateShipExtImpl -> cancelTransfer],cancelDadaShipParam={}", JSON.toJSONString(cancelDadaShipParam));
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 运力供应商状态回传转换
     *
     * @param callbackParamMap
     * @return
     */
    @Override
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        log.info("[AutoTestCreateShipExtImpl -> parseCallbackParam],callbackParamMap={}", JSON.toJSONString(callbackParamMap));
        return ExtResponse.buildSuccess(null);
    }

    /**
     * 查询起手轨迹
     *
     * @param deliveryTrackParam 查询起手轨迹参数
     * @return
     */
    @Override
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        log.info("[AutoTestCreateShipExtImpl -> getTransferTrack],deliveryTrackParam={}", JSON.toJSONString(deliveryTrackParam));
        throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
    }

    /**
     * 查询运单详情
     *
     * @param deliveryOrderDetailRequest
     * @return
     */
    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        log.info("[AutoTestCreateShipExtImpl -> getShipOrderDetail],deliveryOrderDetailRequest={}", JSON.toJSONString(deliveryOrderDetailRequest));
        DeliveryOrderDetailResponse deliveryOrderDetailResponse = new DeliveryOrderDetailResponse();
        return ExtResponse.buildSuccess(deliveryOrderDetailResponse);
    }
}