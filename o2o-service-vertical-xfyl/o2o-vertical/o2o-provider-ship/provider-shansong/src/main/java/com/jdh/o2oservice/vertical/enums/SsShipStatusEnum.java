package com.jdh.o2oservice.vertical.enums;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName SsShipStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/9/29 23:11
 */
@Getter
@AllArgsConstructor
public enum SsShipStatusEnum {

    WAITING_RECEIVE(20, "派单中",  1, "派单中", StanderAngelShipStatusEnum.WAITING_RECEIVE_ORDER),

    WAITING_TRANSFER(20, "派单中",  2, "转单改派中", StanderAngelShipStatusEnum.WAITING_TRANSFER),

    INPLACE_PICKUPING(30, "待取货",  1, "待取货", StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS),

    INPLACE_PICKUPED(30, "待取货",  2, "已就位", StanderAngelShipStatusEnum.KNIGHT_REACH_STORE),

    DELIVERY_SENDING(40, "闪送中",  1, "闪送中", StanderAngelShipStatusEnum.DELIVERING_GOODS),

    DELIVERY_CANCELING(40, "闪送中",  2, "申请取消中", StanderAngelShipStatusEnum.ORDER_SHIP_CANCELING),

    DELIVERY_BACKING(40, "闪送中",  3, "物品送回中", StanderAngelShipStatusEnum.DELIVERED_BACK),

    DELIVERY_COMMISSIONING(40, "闪送中",  4, "取消单客服介入中", StanderAngelShipStatusEnum.ORDER_SHIP_CANCEL_COMMISSIONING),

    FINISH_COMPLETE(50, "已完成",  1, "已完成", StanderAngelShipStatusEnum.ORDER_SHIP_FINISH),

    FINISH_REFUND(50, "已完成",  2, "已退款", StanderAngelShipStatusEnum.ORDER_SHIP_CANCEL),

    FINISH_CANCELED(60, "",  null, "已取消订单", StanderAngelShipStatusEnum.ORDER_SHIP_CANCEL),
    ;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单状态描述
     */
    private String statusDesc;

    /**
     * 订单子状态
     */
    private Integer subStatus;

    /**
     * 订单子状态描述
     */
    private String subStatusDesc;

    /**
     * 标准订单状态
     */
    private StanderAngelShipStatusEnum shipStatusEnum;

    public static Integer transferStanderShipStatus(Integer status, Integer subStatus) {
        if(Objects.isNull(status)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        //闪送运单取消
        if(FINISH_CANCELED.getStatus().equals(status)) {
            return FINISH_CANCELED.getShipStatusEnum().getShipStatus();
        }
        //闪送运单状态匹配
        for (SsShipStatusEnum value : SsShipStatusEnum.values()) {
            if(value.getStatus().equals(status) && value.getSubStatus().equals(subStatus)) {
                return value.getShipStatusEnum().getShipStatus();
            }
        }
        throw new BusinessException(BusinessErrorCode.SHIP_STATUS_TRANSFER_ERROR);
    }

}
