package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.domain.angel.core.ext.CreateThirdStoreExt;
import com.jdh.o2oservice.domain.angel.core.ext.dto.ThirdStoreInfoDto;
import com.jdh.o2oservice.domain.angel.core.ext.param.CreateThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.QueryThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.UpdateThirdStoreParam;
import com.jdh.o2oservice.vertical.SsApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName DadaStoreCreateExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/6/7 10:26 AM
 * @Version 1.0
 **/

@Extension(code = SsApp.CODE)
@Slf4j
public class SsStoreCreateExtImpl implements CreateThirdStoreExt {


    @Override
    public ExtResponse<Boolean> createThirdStore(CreateThirdStoreParam createThirdStoreParam) {
        log.info("我是创建门店扩展点,我执行了");
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 查询第三方门店扩展点
     *
     * @param queryThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<ThirdStoreInfoDto> queryThirdStore(QueryThirdStoreParam queryThirdStoreParam) {
        log.info("我是查询门店扩展点,我执行了");
        return ExtResponse.buildSuccess(null);
    }

    /**
     * 创建第三方门店扩展点
     *
     * @param updateThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> updateThirdStore(UpdateThirdStoreParam updateThirdStoreParam) {
        log.info("我是更新门店扩展点,我执行了");
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 更新达达门店
     * @param updateThirdStoreParam
     * @return
     */
    private Boolean updateDadaShop(UpdateThirdStoreParam updateThirdStoreParam) {
        return true;
    }

    /**
     * 调用商家信息
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        return null;
    }

    /**
     * 生成签名(具体参考文档: http://newopen.imdada.cn/#/quickStart/develop/safety?_k=kklqac)
     */
    private String signature(Map<String, Object> data, String appSecret) {
        // 请求参数按照【属性名】字典升序排序后，按照属性名+属性值拼接
        String signStr = data.keySet().stream()
                .sorted()
                .map(it -> String.format("%s%s", it, data.get(it)))
                .collect(Collectors.joining(""));

        // 拼接后的结果首尾加上appSecret
        String finalSignStr = appSecret + signStr + appSecret;

        // MD5加密并转为大写
        return DigestUtils.md5Hex(finalSignStr).toUpperCase();
    }
}
