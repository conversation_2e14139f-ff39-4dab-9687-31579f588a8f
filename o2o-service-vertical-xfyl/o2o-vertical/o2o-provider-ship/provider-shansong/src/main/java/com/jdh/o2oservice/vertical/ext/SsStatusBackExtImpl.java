package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.DeliveryStatusBackExt;
import com.jdh.o2oservice.ext.ship.param.CheckDeliverStatusParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryStatusBackParam;
import com.jdh.o2oservice.vertical.SsApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName:DeliveryStatusBackExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/28 11:03
 * @Vserion: 1.0
 **/
@Extension(code = SsApp.CODE)
@Slf4j
public class SsStatusBackExtImpl implements DeliveryStatusBackExt {

    @Resource
    private DuccConfig duccConfig;

    /**
     * 签名验证
     *
     * @param verifyParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> verifySignature(String verifyParam) {
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 验证签名字符串拼接
     *
     * @param param
     * @return
     */
    private String generateSignature(DeliveryStatusBackParam param) {
        // 将签名相关字段加入list
        List<String> list = new ArrayList<>(4);
        list.add(param.getClientId() == null ? "" : param.getClientId());
        list.add(param.getOrderId() == null ? "" :param.getOrderId().toString());
        list.add(param.getUpdateTime() == null ? "" : param.getUpdateTime().toString());

        // 将参与签名的字段的值进行升序排列
        Collections.sort(list);

        // 将排序过后的参数，进行字符串拼接
        String joinedStr = String.join("", list);

        // 对拼接后的字符串进行md5加密
        return DigestUtils.md5Hex(joinedStr);
    }

}
