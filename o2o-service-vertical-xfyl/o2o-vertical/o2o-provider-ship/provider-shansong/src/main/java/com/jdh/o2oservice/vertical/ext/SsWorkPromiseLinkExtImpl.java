package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.CoordinateUtil;
import com.jdh.o2oservice.base.util.TextUtil;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.template.context.TemplateContext;
import com.jdh.o2oservice.core.domain.support.template.core.FreeMarkerService;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.work.WorkPromiseLinkExt;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import com.jdh.o2oservice.vertical.SsApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: SsWorkPromiseLinkExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/28 13:37
 * @Vserion: 1.0
 **/
@Slf4j
@Extension(code = SsApp.CODE)
public class SsWorkPromiseLinkExtImpl implements WorkPromiseLinkExt {

    /** */
    @Value("${o2o.angelPromise.angelTransferTrack}")
    private String angelTrackPrefix;

    @Value("${shansong.config.clientId}")
    private String clientId;

    @Value("${shansong.config.shopId}")
    private String shopId;

    @Value("${shansong.config.accessToken}")
    private String accessToken;

    @Value("${shansong.config.url}")
    private String url;

    @Resource
    private FreeMarkerService freeMarkerService;

    private static final String COURIER_INFO_METHODE = "/openapi/merchants/v5/courierInfo";

    private static final List<Integer> STATUS_LIST = Lists.newArrayList(2, 3);

    /**
     * 查询轨迹链接
     *
     * @param trackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryTrackResponse> getTransferTrack(TrackParam trackParam) {
        AssertUtils.nonNull(trackParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(trackParam.getPromiseId(), "履约单id不能为空");
        AssertUtils.nonNull(trackParam.getShipId(), "运单id不能为空");
        AssertUtils.nonNull(trackParam.getShipStatus(), "运单状态不能为空");
        AssertUtils.nonNull(trackParam.getAngelType(), "服务者类型不能为空");

        if(Objects.nonNull(trackParam) && !STATUS_LIST.contains(trackParam.getShipStatus())) {
            return ExtResponse.buildFail(null);
        }

        String angelTrackUrl = String.format(angelTrackPrefix, trackParam.getPromiseId(), trackParam.getShipId(), trackParam.getAngelType());
        log.info("[SsWorkPromiseLinkExtImpl.getTransferTrack],angelTrackUrl={}", angelTrackUrl);
        DeliveryTrackResponse response = new DeliveryTrackResponse();
        response.setTrackUrl(angelTrackUrl);
        log.info("[SsWorkPromiseLinkExtImpl.getTransferTrack],response={}", JSON.toJSONString(response));
        return ExtResponse.buildSuccess(response);
    }

    /**
     * 查询服务者实时轨迹
     *
     * @param realTrackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryRealTrackResponse> getTransferRealTrack(RealTrackParam realTrackParam) {
        log.info("[SsWorkPromiseLinkExtImpl -> getTransferRealTrack],查询骑手实时位置!realTrackParam={}", JSON.toJSONString(realTrackParam));
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("url", url.concat(COURIER_INFO_METHODE));
        paramMap.put("clientId",clientId);
        paramMap.put("shopId",shopId);
        paramMap.put("accessToken",accessToken);
        paramMap.put("timestamp",String.valueOf(new Date().getTime()));

        JSONObject data = new JSONObject();
        data.put("issOrderNo", TextUtil.replaceSpace(realTrackParam.getOutShipId()));

        paramMap.put("data",data.toJSONString());
        paramMap.put("sign",sign(accessToken, paramMap));

        TemplateContext context = new TemplateContext();
        paramMap.put("context", context);

        freeMarkerService.parseFileToStr("ssCourierInfo.ftl", paramMap);
        if(!context.getTemplateResponseModel().getSuccess()) {
            log.error("[SsWorkPromiseLinkExtImpl -> getTransferRealTrack],获取实时位置异常!context={}", JSON.toJSONString(context));
            throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
        }
        log.info("[SsWorkPromiseLinkExtImpl -> getTransferRealTrack],context={}", JSON.toJSONString(context));
        Map<String, Object> returnData = context.getTemplateResponseModel().getReturnData();
        DeliveryRealTrackResponse realTrackResponse = JSON.parseObject(JSON.toJSONString(returnData), DeliveryRealTrackResponse.class);
        parseDistanceInfo(realTrackResponse);

        //百度坐标系转换滕讯坐标系
        double[] coordinateRst = CoordinateUtil.bd09ToGcj02(realTrackResponse.getLng(), realTrackResponse.getLat());
        realTrackResponse.setLng(coordinateRst[0]);
        realTrackResponse.setLat(coordinateRst[1]);

        return ExtResponse.buildSuccess(realTrackResponse);
    }

    public static void main(String[] args) {
        DeliveryRealTrackResponse realTrackResponse = new DeliveryRealTrackResponse();
        realTrackResponse.setEstimateDeliveryTimeTip("距离5.1公里，预计16分钟上门");
        parseDistanceInfo(realTrackResponse);
    }

    /**
     * 解析骑手轨迹中的距离和时间信息
     *
     * @param realTrackResponse
     */
    public static void parseDistanceInfo(DeliveryRealTrackResponse realTrackResponse) {
        if(StringUtils.isBlank(realTrackResponse.getEstimateDeliveryTimeTip())) {
            log.error("[SsWorkPromiseLinkExtImpl -> parseDistanceInfo],骑手预计送达时间为空!");
            return;
        }
        // 创建一个 Pattern 对象，用于匹配数字
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
        // 创建一个 Matcher 对象，用于在输入字符串中查找匹配项
        Matcher matcher = pattern.matcher(realTrackResponse.getEstimateDeliveryTimeTip());
        // 遍历所有匹配项并打印它们
        int cnt = 0;
        while (matcher.find()) {
            cnt += 1;
            if(cnt == 1) {
                log.error("[SsWorkPromiseLinkExtImpl -> parseDistanceInfo],剩余距离={}", matcher.group());
                realTrackResponse.setRemainDistance(Double.valueOf(matcher.group()));
            }
            if(cnt == 2) {
                log.error("[SsWorkPromiseLinkExtImpl -> parseDistanceInfo],剩余时间={}", matcher.group());
                realTrackResponse.setRemainDuration(Integer.valueOf(matcher.group()));
            }
        }
        log.info("[SsWorkPromiseLinkExtImpl -> parseDistanceInfo],解析骑手信息。realTrackResponse={}", JSON.toJSONString(realTrackResponse));
    }

    /**
     * 获取签名
     *
     * @param s
     * @param param
     * @return
     */
    private static String sign(String s, Map param) {
        StringBuilder sb = new StringBuilder();
        sb.append(s).append("clientId").append(param.get("clientId"));
        if (Objects.nonNull(param.get("data"))) {
            sb.append("data").append(param.get("data"));
        }
        sb.append("shopId").append(param.get("shopId"))
                .append("timestamp").append(param.get("timestamp"));
        String aa = bytesToMD5(sb.toString().getBytes(StandardCharsets.UTF_8));
        return aa;
    }

    private static String bytesToMD5(byte[] input) {
        String md5str = null;
        try {
            //创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            //计算后获得字节数组
            byte[] buff = md.digest(input);
            //把数组每一字节换成16进制连成md5字符串
            md5str = bytesToHex(buff);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5str.toUpperCase();
    }

    //把字节数组转成16进位制数
    private static String bytesToHex(byte[] bytes) {
        StringBuffer md5str = new StringBuffer();
        //把数组每一字节换成16进制连成md5字符串
        int digital;
        for (int i = 0; i < bytes.length; i++) {
            digital = bytes[i];
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }
}
