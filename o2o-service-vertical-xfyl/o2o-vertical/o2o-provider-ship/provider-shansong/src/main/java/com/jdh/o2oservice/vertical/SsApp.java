package com.jdh.o2oservice.vertical;

import com.jd.matrix.sdk.annotation.App;
import com.jd.matrix.sdk.base.BaseApp;

/**
 * @ClassName:TransferApp
 * @Description:
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/5/14 22:01
 * @Vserion: 1.0
 **/
@App(code = SsApp.CODE,
        name = "闪送",
        priority = 1,
        parserClass = SsAppCodeParser.class,
        version = "1.0.0")
public class SsApp extends BaseApp {

    public static final String CODE="jdh-o2o-shansong";
}
