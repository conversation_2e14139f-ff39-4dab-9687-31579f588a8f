package com.jdh.o2oservice.vertical.ext;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jd.jim.cli.Cluster;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.CoordinateUtil;
import com.jdh.o2oservice.base.util.TextUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.template.context.TemplateContext;
import com.jdh.o2oservice.core.domain.support.template.core.FreeMarkerService;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.vertical.SsApp;
import com.jdh.o2oservice.vertical.enums.SsShipStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ClassName: SsCreateShipExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/27 02:22
 * @Vserion: 1.0
 **/
@Extension(code = SsApp.CODE)
@Slf4j
public class SsCreateShipExtImpl implements CreateShipExt {

    @Resource
    private FreeMarkerService freeMarkerService;

    @Value("${shansong.config.clientId}")
    private String clientId;

    @Value("${shansong.config.shopId}")
    private String shopId;

    @Value("${shansong.config.accessToken}")
    private String accessToken;

    @Value("${shansong.config.url}")
    private String url;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private Cluster jimClient;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;
    /**
     *
     * @param createDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {
        //获取城市开通的闪送店铺，目前是配置ducc做的城市映射，后续要解开放在运营端
        List<String> firstAreaList = duccConfig.getFirstAreaList();
        log.info("[SsCreateShipExtImpl -> callTransfer], firstAreaList={}", JSON.toJSONString(firstAreaList));
        Map<String, String> ssCityCodeMapJdCityCode = duccConfig.getSsCityCodeMapJdCityCode();
        log.info("[SsCreateShipExtImpl -> callTransfer], ssCityCodeMapJdCityCode={}", JSON.toJSONString(ssCityCodeMapJdCityCode));
        String ssCityCode = firstAreaList.contains(String.valueOf(createDadaShipParam.getFirstArea())) ? ssCityCodeMapJdCityCode.get(String.valueOf(createDadaShipParam.getFirstArea())) : ssCityCodeMapJdCityCode.get(String.valueOf(createDadaShipParam.getSecondArea()));
        log.info("[SsCreateShipExtImpl -> callTransfer], 闪送城市映射配置!ssCityCode={}", ssCityCode);
        if(StringUtils.isBlank(ssCityCode)) {
            log.error("[SsCreateShipExtImpl -> callTransfer],该城市没有开通闪送店铺!createDadaShipParam={}", JSON.toJSONString(createDadaShipParam));
            throw new BusinessException(BusinessErrorCode.CITY_NOT_OPEN);
        }
        String[] cityInfo = ssCityCode.split("-");

        //分页查询商户店铺
        Map<String, Object> stringObjectMap = queryAllStore(cityInfo);
        if(Objects.isNull(stringObjectMap)){
            log.error("[SsCreateShipExtImpl -> callTransfer],没有查询到店铺信息!");
            throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
        }

        //结果映射，应该放在模板中映射
        NumberFormat numberFormat = NumberFormat.getInstance();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        log.info("[SsCreateShipExtImpl -> callTransfer], 开始异步执行");
        //调用计费接口
        AtomicReference<Map<String, Object>> calculateMap = new AtomicReference<>(Maps.newHashMap());
        futures.add(CompletableFuture.runAsync(() -> {
                    calculateMap.set(orderCalculate(stringObjectMap, createDadaShipParam));
                    createDadaShipParam.setShopNo(String.valueOf(stringObjectMap.get("storeId")));
                }, executorPoolFactory.getForkJoinPoolByPFinder()));

        //闪送eta数据，查询预计时间
        AtomicReference<Map<String, Object>> etaMap = new AtomicReference<>(Maps.newHashMap());
        futures.add(CompletableFuture.runAsync(() -> {
            etaMap.set(orderEta(createDadaShipParam));
        }, executorPoolFactory.getForkJoinPoolByPFinder()));
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("[SsCreateShipExtImpl -> callTransfer], 结束异步执行");

        //提单接口
        Map<String, Object> orderMap = orderPlace(createDadaShipParam, calculateMap.get());

        //拼装结果返回
        CreateShipExtResponse extResponse = new CreateShipExtResponse();
        try {
            Integer estimateGrabSecond = Objects.nonNull(etaMap.get().get("estimateGrabSecond")) ? numberFormat.parse(String.valueOf(etaMap.get().get("estimateGrabSecond"))).intValue() : 0;
            Integer estimateReceiveSecond = Objects.nonNull(etaMap.get().get("estimateReceiveSecond")) ? numberFormat.parse(String.valueOf(etaMap.get().get("estimateReceiveSecond"))).intValue() : 0;
            Integer estimatePickUpSecond = Objects.nonNull(etaMap.get().get("estimatePickUpSecond")) ? numberFormat.parse(String.valueOf(etaMap.get().get("estimatePickUpSecond"))).intValue() : 0;
            //实付
            Integer totalFeeAfterSave = Objects.nonNull(orderMap.get("totalFeeAfterSave")) ? numberFormat.parse(String.valueOf(orderMap.get("totalFeeAfterSave"))).intValue() : 0;
            //总金额
            Integer totalAmount =  Objects.nonNull(orderMap.get("totalAmount")) ? numberFormat.parse(String.valueOf(orderMap.get("totalAmount"))).intValue() : 0;
            //优惠金额
            Integer couponSaveFee =  Objects.nonNull(orderMap.get("couponSaveFee")) ? numberFormat.parse(String.valueOf(orderMap.get("couponSaveFee"))).intValue() : 0;

            CreateShipExtFeeResponse feeResponse = new CreateShipExtFeeResponse();

            String feeInfoList = String.valueOf(orderMap.get("feeInfoList"));
            String interestDtoList = String.valueOf(orderMap.get("interestDtoList"));
            feeResponse.setAmount(BigDecimal.valueOf(totalFeeAfterSave).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            feeResponse.setTotalAmount(BigDecimal.valueOf(totalAmount).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            feeResponse.setDiscountAmount(BigDecimal.valueOf(couponSaveFee).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            feeResponse.setTotalAmountDetail(feeInfoList);
            feeResponse.setInterestList(interestDtoList);
            extResponse.setExtFeeResponse(feeResponse);

            extResponse.setOutOrderNo(String.valueOf(orderMap.get("orderNumber")));
            extResponse.setTotalDistance(BigDecimal.valueOf(numberFormat.parse(String.valueOf(orderMap.get("totalDistance"))).doubleValue()));
            extResponse.setTotalDistance(BigDecimal.valueOf(numberFormat.parse(String.valueOf(orderMap.get("totalDistance"))).doubleValue()));
            LocalDateTime currentLocalDateTime = TimeUtils.getCurrentLocalDateTime();
            if(estimateGrabSecond > 0) {
                extResponse.setEstimateGrabTime(TimeUtils.localDateTimeToDate(currentLocalDateTime.plusSeconds(estimateGrabSecond)));
            }
            if(estimateReceiveSecond > 0) {
                extResponse.setEstimateReceiveTime(TimeUtils.localDateTimeToDate(currentLocalDateTime.plusSeconds(estimateReceiveSecond)));
            }
            if(estimatePickUpSecond > 0) {
                extResponse.setEstimatePickUpTime(TimeUtils.localDateTimeToDate(currentLocalDateTime.plusSeconds(estimatePickUpSecond)));
            }
            extResponse.setEstimateCallTime(DateUtil.date().toJdkDate());
        } catch (Exception ex) {
            log.error("[SsCreateShipExtImpl -> callTransfer], 数字转换异常!", ex);
            throw new BusinessException(BusinessErrorCode.CALL_RIDER_ERROR);
        }
        extResponse.setShopNo(String.valueOf(stringObjectMap.get("storeId")));
        return ExtResponse.buildSuccess(extResponse);
    }

    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 取消运力
     *
     * @param cancelDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        log.info("[SsCreateShipExtImpl -> cancelTransfer],cancelDadaShipParam={}", JSON.toJSONString(cancelDadaShipParam));
        JSONObject param = new JSONObject();
        param.put("clientId", clientId);
        param.put("shopId", shopId);
        param.put("timestamp",String.valueOf(new Date().getTime()));
        JSONObject data = new JSONObject();
        data.put("issOrderNo",cancelDadaShipParam.getProviderOrderId());
        param.put("data",data.toJSONString());
        param.put("sign",sign(accessToken, param));
        param.put("url", url.concat("/openapi/merchants/v5/abortOrder"));
        //填充上下文
        TemplateContext context = new TemplateContext();
        param.put("context", context);
        freeMarkerService.parseFileToStr("ssAbortOrder.ftl", param);
        log.info("[SsCreateShipExtImpl -> cancelTransfer],context={}", JSON.toJSONString(context));
        if(!context.getTemplateResponseModel().getSuccess()){
            throw new BusinessException(BusinessErrorCode.CANCEL_SHIP_ERROR);
        }
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 运力供应商状态回传转换
     *
     * @param callbackParamMap
     * @return
     */
    @Override
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        TemplateContext context = new TemplateContext();
        callbackParamMap.put("context", context);
        freeMarkerService.parseFileToStr("ssStatusCallbackInfo.ftl", callbackParamMap);
        if(MapUtils.isEmpty(context.getRequestParamsMap())) {
            log.error("[SsCreateShipExtImpl -> parseCallbackParam],状态回传信息为空!callbackParamMap={}", JSON.toJSONString(callbackParamMap));
            return ExtResponse.buildFail(null);
        }
        ShipCallbackParamResponse callbackFunction = JSON.parseObject(JSON.toJSONString(context.getRequestParamsMap().get("callbackFunction")), ShipCallbackParamResponse.class);

        //转换订单状态
        Integer status = callbackFunction.getStatus();
        Integer subStatus = callbackFunction.getSubStatus();
        callbackFunction.setOrderStatus(SsShipStatusEnum.transferStanderShipStatus(status,subStatus));
        callbackFunction.setCancelFrom(convertCancelFrom(callbackFunction.getCancelFrom()));
        return ExtResponse.buildSuccess(callbackFunction);
    }

    /**
     * 取消来源映射
     *
     * @param cancelFrom
     * @return
     */
    private Integer convertCancelFrom(Integer cancelFrom) {
        if(Objects.isNull(cancelFrom)) {
            return null;
        }
        switch (cancelFrom){
            case 1: return 2;
            case 10: return 3;
            case 3:
            default:
                return 1;
        }
    }

    /**
     * 查询起手轨迹
     *
     * @param deliveryTrackParam 查询轨迹入参
     * @return
     */
    @Override
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        return null;
    }

    /**
     * 查询运单详情
     *
     * @param deliveryOrderDetailRequest
     * @return
     */
    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        return ExtResponse.buildSuccess(null);
    }

    /**
     * 提交运单
     *
     * @param createDadaShipParam
     * @param calculateMap
     * @return
     */
    private Map<String, Object> orderPlace(CreateDadaShipParam createDadaShipParam, Map<String, Object> calculateMap) {
        JSONObject param = new JSONObject();
        param.put("clientId", clientId);
        param.put("shopId", shopId);
        param.put("timestamp",String.valueOf(new Date().getTime()));
        JSONObject data = new JSONObject();
        data.put("issOrderNo", TextUtil.replaceSpace(String.valueOf(calculateMap.get("orderNumber"))));
        param.put("data",data.toJSONString());
        param.put("sign",sign(accessToken, param));
        param.put("url", url.concat("/openapi/merchants/v5/orderPlace"));
        //填充上下文
        TemplateContext context = new TemplateContext();
        param.put("context", context);
        freeMarkerService.parseFileToStr("ssOrderPlace.ftl", param);
        log.info("[SsCreateShipExtImpl -> callTransfer],context={}", JSON.toJSONString(context));
        if(!context.getTemplateResponseModel().getSuccess()) {
            throw new BusinessException(BusinessErrorCode.CREATE_SHIP_ERROR);
        }
        return context.getTemplateResponseModel().getReturnData();
    }

    /**
     * 计算闪送配送的eta
     *
     * @param createDadaShipParam
     * @return
     */
    private Map<String, Object> orderEta(CreateDadaShipParam createDadaShipParam) {
        JSONObject param = new JSONObject();
        param.put("clientId", clientId);
        param.put("shopId", shopId);
        param.put("timestamp",String.valueOf(new Date().getTime()));
        JSONObject data = new JSONObject();
        data.put("cityId", createDadaShipParam.getSecondArea());

        //转换经纬度
        double[] supplierCoordinate = CoordinateUtil.gcj02ToBd09(createDadaShipParam.getSupplierLng(), createDadaShipParam.getSupplierLat());
        double[] receiverCoordinate = CoordinateUtil.gcj02ToBd09(createDadaShipParam.getReceiverLng(), createDadaShipParam.getReceiverLat());
        data.put("fromLat", supplierCoordinate[1]);
        data.put("fromLng", supplierCoordinate[0]);
        data.put("toLat", receiverCoordinate[1]);
        data.put("toLng", receiverCoordinate[0]);
        param.put("data",data.toJSONString());
        param.put("sign",sign(accessToken, param));
        param.put("url", url.concat("/openapi/merchants/v5/orderEta"));
        //填充上下文
        TemplateContext context = new TemplateContext();
        param.put("context", context);
        freeMarkerService.parseFileToStr("ssOrderEta.ftl", param);
        log.info("[SsCreateShipExtImpl -> orderEta],context={}", JSON.toJSONString(context));

        return context.getTemplateResponseModel().getReturnData();
    }

    /**
     * 调用订单计费接口查询计费信息
     *
     * @param shopMap
     * @param createDadaShipParam
     */
    private Map<String, Object> orderCalculate(Map<String, Object> shopMap, CreateDadaShipParam createDadaShipParam) {
        JSONObject param = new JSONObject();
        param.put("clientId", clientId);
        param.put("shopId", shopId);
        param.put("timestamp",String.valueOf(new Date().getTime()));
        JSONObject data = new JSONObject();
        data.put("pickupPwd", 1);
        data.put("cityName", TextUtil.replaceSpace(String.valueOf(shopMap.get("cityName"))));
        data.put("appointType", createDadaShipParam.getIsExpectFinishOrder());
        if(Objects.nonNull(createDadaShipParam.getExpectFinishTimeLimit())){
            Date date = new Date(createDadaShipParam.getExpectFinishTimeLimit() * 1000L); // 将秒级时间戳转换为毫秒级时间戳
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = sdf.format(date);
            data.put("appointmentDate", TextUtil.replaceSpace(formattedDate));
        }

        data.put("storeId", TextUtil.replaceSpace(String.valueOf(shopMap.get("storeId"))));
        //发件人信息
        JSONObject sender = new JSONObject();
        if(duccConfig.getGisConvertSwitch()) {
            log.info("[SsCreateShipExtImpl -> orderCalculate],sender使用国测坐标系");
            data.put("lbsType", CommonConstant.ONE);
            sender.put("fromLatitude", createDadaShipParam.getSupplierLat());
            sender.put("fromLongitude", createDadaShipParam.getSupplierLng());
        }else {
            log.info("[SsCreateShipExtImpl -> orderCalculate],sender使用百度坐标系");
            double[] supplierBd09 = CoordinateUtil.gcj02ToBd09(createDadaShipParam.getSupplierLng(), createDadaShipParam.getSupplierLat());
            sender.put("fromLatitude", supplierBd09[1]);
            sender.put("fromLongitude", supplierBd09[0]);
        }
        sender.put("fromAddress", TextUtil.replaceSpace(createDadaShipParam.getSupplierAddress()));
        sender.put("fromAddressDetail", TextUtil.replaceSpace(createDadaShipParam.getSupplierAddress()));
        sender.put("fromSenderName", TextUtil.replaceSpace(createDadaShipParam.getSupplierName()));
        sender.put("fromMobile", TextUtil.replaceSpace(createDadaShipParam.getSupplierPhone()));
        data.put("sender", sender);

        //收件人信息
        JSONArray receiverArr = new JSONArray();
        JSONObject receiver = new JSONObject();
        receiverArr.add(receiver);
        receiver.put("orderNo", TextUtil.replaceSpace(createDadaShipParam.getOriginId()));
        receiver.put("toAddress", TextUtil.replaceSpace(createDadaShipParam.getReceiverAddress()));
        receiver.put("toAddressDetail", TextUtil.replaceSpace(createDadaShipParam.getReceiverAddress()));

        //转换百度和gcj02经纬度
        if(duccConfig.getGisConvertSwitch()) {
            log.info("[SsCreateShipExtImpl -> orderCalculate],receiver使用国测坐标系");
            receiver.put("toLatitude", createDadaShipParam.getReceiverLat());
            receiver.put("toLongitude", createDadaShipParam.getReceiverLng());
        }else {
            log.info("[SsCreateShipExtImpl -> orderCalculate],receiver使用百度坐标系");
            double[] bd09 = CoordinateUtil.gcj02ToBd09(createDadaShipParam.getReceiverLng(), createDadaShipParam.getReceiverLat());
            receiver.put("toLatitude", bd09[1]);
            receiver.put("toLongitude", bd09[0]);
        }
        receiver.put("toReceiverName", TextUtil.replaceSpace(createDadaShipParam.getReceiverName()));
        receiver.put("toMobile", TextUtil.replaceSpace(createDadaShipParam.getReceiverPhone()));
        receiver.put("goodType", 13);
        receiver.put("weight", 1);
        receiver.put("remarks", TextUtil.replaceSpace(createDadaShipParam.getInfo()));
        data.put("receiverList", receiverArr);


        param.put("data",data.toJSONString());
        param.put("sign",sign(accessToken, param));
        param.put("url", url.concat("/openapi/merchants/v5/orderCalculate"));
        //填充上下文
        TemplateContext context = new TemplateContext();
        param.put("context", context);
        freeMarkerService.parseFileToStr("ssOrderCalculate.ftl", param);
        log.info("[SsCreateShipExtImpl -> orderCalculate],context={}", JSON.toJSONString(context));
        return context.getTemplateResponseModel().getReturnData();
    }

    /**
     * 查询所有的店铺列表
     *
     * @param cityInfo
     * @return
     */
    private Map<String, Object> queryAllStore(String[] cityInfo) {
        //先查询缓存是否存在
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SHANSONG_SHOP_CACHE_PREFIX, cityInfo[0]);
        String shopCache = jimClient.get(redisKey);
        if(StringUtils.isNotBlank(shopCache)) {
            return JSON.parseObject(shopCache, Map.class);
        }
        String queryAllStoreUrl = url.concat("/openapi/merchants/v5/queryAllStores");
        int pageNum = 1;
        int pageSize = 10;
        do {
            JSONObject param = new JSONObject();
            param.put("clientId", clientId);
            param.put("shopId", shopId);
            param.put("timestamp",String.valueOf(new Date().getTime()));
            JSONObject data = new JSONObject();
            data.put("pageNo", pageNum);
            data.put("pageSize", pageSize);
            param.put("data",data.toJSONString());
            param.put("url", queryAllStoreUrl);
            param.put("sign",sign(accessToken, param));
            //填充上下文
            TemplateContext context = new TemplateContext();
            param.put("context", context);
            freeMarkerService.parseFileToStr("ssQueryAllStore.ftl", param);
            log.info("[SsCreateShipExtImpl -> queryAllStore],context={}", JSON.toJSONString(context));
            List<Map<String, Object>> returnListData = context.getTemplateResponseModel().getReturnListData();
            if(CollectionUtils.isEmpty(returnListData)) {
                return null;
            }
            NumberFormat instance = NumberFormat.getInstance();
            for (Map<String, Object> returnListDatum : returnListData) {
                if(Objects.isNull(returnListDatum.get("cityId"))) {
                    continue;
                }
                try {
                    Number number = instance.parse(String.valueOf(returnListDatum.get("cityId")));
                    if(number.longValue() == Long.valueOf(cityInfo[0])) {
                        jimClient.setEx(redisKey, JSON.toJSONString(returnListDatum), RedisKeyEnum.SHANSONG_SHOP_CACHE_PREFIX.getExpireTime(), RedisKeyEnum.SHANSONG_SHOP_CACHE_PREFIX.getExpireTimeUnit());
                        return returnListDatum;
                    }
                } catch (Exception ex) {
                    log.error("[SsCreateShipExtImpl -> queryAllStore],数字类型解析异常!", ex);
                }

            }
            pageNum++;
        }while(pageNum * pageSize < CommonConstant.NUMBER_THOUSAND);
        return null;
    }

    private static String sign(String s, Map param) {
        StringBuilder sb = new StringBuilder();
        sb.append(s).append("clientId").append(param.get("clientId"));
        if (Objects.nonNull(param.get("data"))) {
            sb.append("data").append(param.get("data"));
        }
        sb.append("shopId").append(param.get("shopId"))
                .append("timestamp").append(param.get("timestamp"));
        String aa = bytesToMD5(sb.toString().getBytes(StandardCharsets.UTF_8));
        return aa;
    }
    private static String bytesToMD5(byte[] input) {
        String md5str = null;
        try {
            //创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            //计算后获得字节数组
            byte[] buff = md.digest(input);
            //把数组每一字节换成16进制连成md5字符串
            md5str = bytesToHex(buff);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5str.toUpperCase();
    }
    //把字节数组转成16进位制数
    private static String bytesToHex(byte[] bytes) {
        StringBuffer md5str = new StringBuffer();
        //把数组每一字节换成16进制连成md5字符串
        int digital;
        for (int i = 0; i < bytes.length; i++) {
            digital = bytes[i];
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }

    public static void main(String[] args) {
        String orderTime = String.valueOf(new Date().getTime());
        JSONObject param = new JSONObject();
        param.put("clientId", "ssmYC81DSQxAowEti");
        param.put("shopId", "20000000000005675");
        param.put("timestamp",String.valueOf(new Date().getTime()));
        JSONObject data = new JSONObject();
        data.put("issOrderNo", "TDH2024102176668903");
        data.put("thirdOrderNo", "162241666089145");
        param.put("data",data.toJSONString());
        param.put("sign",sign("8R27KzL15SJhBwewYojeab6VuPyJYjW8", param));
        System.out.println(JSON.toJSONString(param));
        System.out.println(JSON.toJSONString(param));
    }
}
