package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.application.angel.AngelLocationExtApplication;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.query.AngelLocationRequest;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.bo.AngelShipPositionBo;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.CreateShipExtResponse;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.ship.reponse.ShipCallbackParamResponse;
import com.jdh.o2oservice.vertical.NurseApp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/9
 * @description 护士自配送扩展点
 */
@Extension(code = NurseApp.CODE)
@Slf4j
public class NurseCreateShipExtImpl implements CreateShipExt {

    @Resource
    private AngelLocationExtApplication angelLocationExtApplication;


    @Override
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {
        log.info("NurseCreateShipExtImpl.callTransfer 我是自配送扩展点,但是方法是空!!!");
        return null;
    }

    @Override
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        log.info("NurseCreateShipExtImpl.reCallTransfer 我是自配送扩展点,但是方法是空!!!");
        return null;
    }

    @Override
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        log.info("NurseCreateShipExtImpl.cancelTransfer 我是自配送扩展点,但是方法是空!!!");
        return null;
    }

    @Override
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        log.info("NurseCreateShipExtImpl.parseCallbackParam 我是自配送扩展点,但是方法是空!!!");
        return null;
    }

    @Override
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        log.info("NurseCreateShipExtImpl.getTransferTrack 我是自配送扩展点,但是方法是空!!!");
        return null;
    }

    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        log.info("NurseCreateShipExtImpl.getShipOrderDetail 我是自配送扩展点");
        AngelLocationRequest request = new AngelLocationRequest();
        request.setAngelId(Long.parseLong(deliveryOrderDetailRequest.getAngelId()));
        Response<AngelLocationDto> angelLocationDtoResponse = angelLocationExtApplication.getLocation(request);
        if (angelLocationDtoResponse.getData()!=null){
            DeliveryOrderDetailResponse deliveryOrderDetailResponse = new DeliveryOrderDetailResponse();
            deliveryOrderDetailResponse.setTransporterLng(angelLocationDtoResponse.getData().getLongitude()+"");
            deliveryOrderDetailResponse.setTransporterLat(angelLocationDtoResponse.getData().getLatitude()+"");
            return ExtResponse.buildSuccess(deliveryOrderDetailResponse);
        }
        return ExtResponse.buildSuccess(null);
    }
}
