package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.core.domain.support.ship.dto.RiderLatestPositionDto;
import com.jdh.o2oservice.core.domain.support.ship.enums.ShunFengEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.ship.event.ShunFengAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.model.ShunFengAngelShipSupport;
import com.jdh.o2oservice.core.domain.support.ship.param.*;
import com.jdh.o2oservice.vertical.ShunFengApp;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.ship.ShunFengRpc;
import com.jdh.o2oservice.core.domain.support.ship.dto.CreateShunFengOrderDto;
import com.jdh.o2oservice.core.domain.support.ship.dto.PreCreateShunFengOrderDto;
import com.jdh.o2oservice.core.domain.support.ship.dto.ShunFengRiderViewDto;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * @ClassName ShunFengCreateShipExtImpl
 * @Description 运单相关能力扩展点
 * <AUTHOR>
 * @Date 2024/12/13 1:30 PM
 * @Version 1.0
 **/
@Extension(code = ShunFengApp.CODE)
@Slf4j
public class ShunFengCreateShipExtImpl implements CreateShipExt {

    @Resource
    private ShunFengRpc shunFengRpc;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private EventCoordinator eventCoordinator;


    /**
     * 呼叫运力
     * @param createDadaShipParam
     * @param planOutTime
     * @param providerShopNo
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {


        //预创建订单,获取eta预估数据
        PreCreateShunFengOrderDto preCreateShunFengOrderDto =  this.preCreateOrder(createDadaShipParam);
        //正式创建订单
        CreateShunFengOrderDto createShunFengOrderDto =  this.createOrder(createDadaShipParam);


        //返回结果
        CreateShipExtResponse createShipExtResponse = new CreateShipExtResponse();
        //顺丰运单号
        createShipExtResponse.setOutOrderNo(createShunFengOrderDto.getSfOrderId());
        //门店编号，在供应商侧创建的店铺编码
        createShipExtResponse.setShopNo(createDadaShipParam.getShopNo());
        ///配送距离
        createShipExtResponse.setTotalDistance(BigDecimal.valueOf(preCreateShunFengOrderDto.getDeliveryDistanceMeter()));
        ///预计呼叫运力时间
        createShipExtResponse.setEstimateCallTime(new Date(preCreateShunFengOrderDto.getStartTime()*1000L));
        ///预计上门时间
        createShipExtResponse.setEstimatePickUpTime(new Date((preCreateShunFengOrderDto.getExpectTime()-preCreateShunFengOrderDto.getPromiseDeliveryTime()*60)*1000L));
        ///预计送达时间
        createShipExtResponse.setEstimateReceiveTime(new Date((preCreateShunFengOrderDto.getExpectTime()*1000L)));

        CreateShipExtFeeResponse extFeeResponse = new CreateShipExtFeeResponse();
        extFeeResponse.setTotalAmount(BigDecimal.valueOf(createShunFengOrderDto.getTotalPayMoney()/100));
        extFeeResponse.setAmount(BigDecimal.valueOf(createShunFengOrderDto.getRealPayMoney()/100));
        extFeeResponse.setDiscountAmount(BigDecimal.valueOf(createShunFengOrderDto.getCouponsTotalFee()));
        createShipExtResponse.setExtFeeResponse(extFeeResponse);

        if(createShunFengOrderDto!=null&& StringUtils.isNotEmpty(createShunFengOrderDto.getSfOrderId())){
            log.info("callTransfer 顺丰运力创建成功.发送模拟事件");
            //发送创建运力的延迟事件
            ShunFengAngelShipSupport shunFengAngelShipSupport = ShunFengAngelShipSupport.builder().shipId(Long.parseLong(createDadaShipParam.getOriginId())).build();
            ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody = new ShunFengAngelShipSupportEventBody();
            shunFengAngelShipSupportEventBody.setSfOrderId(createShunFengOrderDto.getSfOrderId());
            shunFengAngelShipSupportEventBody.setUrlIndex("sf_create");
            shunFengAngelShipSupportEventBody.setOrderStatus(1);
            shunFengAngelShipSupportEventBody.setOrderId(createDadaShipParam.getOriginId());
            Event publishEvent = EventFactory.newDelayEvent(shunFengAngelShipSupport, ShunFengEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CREATE, shunFengAngelShipSupportEventBody,(createShipExtResponse.getEstimateCallTime().getTime()-System.currentTimeMillis()/1000));
            eventCoordinator.publishDelay(publishEvent);
        }
        return ExtResponse.buildSuccess(createShipExtResponse);
    }


    /**
     * 正式创建订单
     * @param createDadaShipParam
     * @return
     */
    private CreateShunFengOrderDto createOrder(CreateDadaShipParam createDadaShipParam) {
        CreateShunFengOrderParam createShunFengOrderParam = new CreateShunFengOrderParam();
        createShunFengOrderParam.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        //接入方服务站id
        //createShunFengOrderParam.setShopId(createDadaShipParam.getShopNo());
        //为了回调,要写死
        createShunFengOrderParam.setShopId("3243279847393");
        //接入方店铺id
        createShunFengOrderParam.setShopType(2);
        //接入方订单id
        createShunFengOrderParam.setShopOrderId(createDadaShipParam.getOriginId());
        //订单来源
        createShunFengOrderParam.setOrderSource("京东健康-消费医疗");
        //用户下单时间-使用默认时间
        createShunFengOrderParam.setOrderTime((int)(System.currentTimeMillis()/1000));
        //0：非预约单；1：预约单 ;固定传预约单
        createShunFengOrderParam.setIsAppoint(1);
        //预约单的时候传入,1：预约单送达单；2：预约单上门单
        createShunFengOrderParam.setAppointType(2);
        //用户期望上门时间,预约单才维护该字段
        createShunFengOrderParam.setExpectPickupTime((int)(createDadaShipParam.getWorkStartTime().getTime()/1000));

        //推单时间
        createShunFengOrderParam.setPushTime((int)(System.currentTimeMillis()/1000));
        //2：从用户取件送至门店
        createShunFengOrderParam.setRiderPickMethod(2);
        //参照文档主版本号填写
        //如：文档版本号1.9,version=19，推荐使用版本19
        createShunFengOrderParam.setVersion(19);
        //订单备注
        createShunFengOrderParam.setRemark(createDadaShipParam.getInfo());
        //全部返回为填入511
        createShunFengOrderParam.setReturnFlag(511);

        //门店信息
        CreateShunFengOrderParam.Shop shop = new CreateShunFengOrderParam.Shop();
        shop.setShopAddress(createDadaShipParam.getReceiverAddress());
        shop.setShopLng(createDadaShipParam.getReceiverLng()+"");
        shop.setShopLat(createDadaShipParam.getReceiverLat()+"");
        shop.setShopPhone(createDadaShipParam.getReceiverPhone());
        shop.setShopName(createDadaShipParam.getReceiverName());
        createShunFengOrderParam.setShop(shop);

        //收货人信息
        CreateShunFengOrderParam.Receive receive = new CreateShunFengOrderParam.Receive();
        receive.setUserAddress(createDadaShipParam.getSupplierAddress());
        receive.setUserLat(createDadaShipParam.getSupplierLat()+"");
        receive.setUserLng(createDadaShipParam.getSupplierLng()+"");
        //receive.setUserName(createDadaShipParam.getSupplierName());
        //为了回调先写死
        receive.setUserName("顺丰同城");
        //receive.setUserPhone(createDadaShipParam.getSupplierPhone());
        //为了回调先写死
        receive.setUserPhone("18510654114");
        createShunFengOrderParam.setReceive(receive);

        //商品信息
        CreateShunFengOrderParam.OrderDetail orderDetail = new CreateShunFengOrderParam.OrderDetail();
        orderDetail.setTotalPrice(1);
        orderDetail.setProductType(2);
        orderDetail.setWeightGram(1);
        orderDetail.setProductNum(createDadaShipParam.getCargoNum()==null?1:createDadaShipParam.getCargoNum());
        orderDetail.setProductTypeNum(1);

        CreateShunFengOrderParam.ProductDetail productDetail = new CreateShunFengOrderParam.ProductDetail();
        productDetail.setProductName("医疗用品");
        productDetail.setProductNum(1);
        orderDetail.setProductDetail(Collections.singletonList(productDetail));

        createShunFengOrderParam.setOrderDetail(orderDetail);
        return shunFengRpc.createOrder(createShunFengOrderParam);
    }

    /**
     * 预创建顺丰订单
     * @param createDadaShipParam
     * @return
     */
    private PreCreateShunFengOrderDto preCreateOrder(CreateDadaShipParam createDadaShipParam){
        // 预创建订单
        PreCreateShunFengOrderParam preCreateShunFengOrderParam = new PreCreateShunFengOrderParam();
        preCreateShunFengOrderParam.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        preCreateShunFengOrderParam.setPushTime((int)(System.currentTimeMillis()/1000));
        //53 冷链医药
        preCreateShunFengOrderParam.setProductType(2);
        preCreateShunFengOrderParam.setShopId(createDadaShipParam.getShopNo());
        //1：顺丰店铺ID ；2：接入方店铺ID
        preCreateShunFengOrderParam.setShopType(2);
        //重量默认1克
        preCreateShunFengOrderParam.setWeight(1);
        //返回全部字段
        preCreateShunFengOrderParam.setReturnFlag(511);
        //2：从用户取件送至门店
        preCreateShunFengOrderParam.setRiderPickMethod(2);
        //2：预约单上门单
        preCreateShunFengOrderParam.setAppointType(2);
        //传预约单
        preCreateShunFengOrderParam.setIsAppoint(1);

        //门店信息
        PreCreateShunFengOrderParam.Shop shop = new PreCreateShunFengOrderParam.Shop();
        shop.setShopAddress(createDadaShipParam.getReceiverAddress());
        shop.setShopLng(createDadaShipParam.getReceiverLng()+"");
        shop.setShopLat(createDadaShipParam.getReceiverLat()+"");
        shop.setShopPhone(createDadaShipParam.getReceiverPhone());
        shop.setShopName(createDadaShipParam.getReceiverName());
        preCreateShunFengOrderParam.setShop(shop);

        //收货人信息
        preCreateShunFengOrderParam.setUserAddress(createDadaShipParam.getSupplierAddress());
        preCreateShunFengOrderParam.setUserLat(createDadaShipParam.getSupplierLat()+"");
        preCreateShunFengOrderParam.setUserLng(createDadaShipParam.getSupplierLng()+"");

        //预约信息,预约单才维护期望上门时间
        preCreateShunFengOrderParam.setExpectPickupTime((int)(createDadaShipParam.getWorkStartTime().getTime()/1000));


        return shunFengRpc.preCreateOrder(preCreateShunFengOrderParam);
    }

    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        return null;
    }

    /**
     * 取消呼叫运力
     * @param cancelDadaShipParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        CancelShunFengOrderParam cancelOrderParam  = new CancelShunFengOrderParam();
        cancelOrderParam.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        cancelOrderParam.setOrderId(cancelDadaShipParam.getProviderOrderId());
        cancelOrderParam.setPushTime((int)(System.currentTimeMillis()/1000));
        shunFengRpc.cancelOrder(cancelOrderParam);
        //发送取消的延迟事件
        ShunFengAngelShipSupport shunFengAngelShipSupport = ShunFengAngelShipSupport.builder().shipId(Long.parseLong(cancelDadaShipParam.getOrderId())).build();
        ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody = new ShunFengAngelShipSupportEventBody();
        shunFengAngelShipSupportEventBody.setSfOrderId(cancelDadaShipParam.getProviderOrderId());
        shunFengAngelShipSupportEventBody.setUrlIndex("sf_cancel");
        shunFengAngelShipSupportEventBody.setOrderStatus(2);
        shunFengAngelShipSupportEventBody.setOrderId(cancelDadaShipParam.getOrderId());
        Event publishEvent = EventFactory.newDelayEvent(shunFengAngelShipSupport, ShunFengEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL, shunFengAngelShipSupportEventBody,5L);
        eventCoordinator.publishDelay(publishEvent);
        return ExtResponse.buildSuccess(Boolean.TRUE);
    }

    /**
     * 运力供应商状态回传转换
     * @param callbackParamMap
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        return null;
    }

    /**
     * 查询骑手轨迹
     * @param deliveryTrackParam
     * @return
     */
    @Override
    @LogAndAlarm
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        ShunFengRiderViewParam riderViewParam = new ShunFengRiderViewParam();
        riderViewParam.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        riderViewParam.setOrderType(1);
        riderViewParam.setOrderId(deliveryTrackParam.getOrderId());
        riderViewParam.setPushTime((int)(System.currentTimeMillis()/1000));

        ShunFengRiderViewDto shunFengRiderViewDto =  shunFengRpc.riderView(riderViewParam);

        DeliveryTrackResponse deliveryTrackResponse = new DeliveryTrackResponse();
        deliveryTrackResponse.setTrackUrl(shunFengRiderViewDto.getUrl());
        return deliveryTrackResponse;
    }

    /**
     * 查询运单详情
     * @param deliveryOrderDetailRequest
     * @return
     */
    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        RiderLatestPositionParam riderLatestPositionParam = new RiderLatestPositionParam();
        riderLatestPositionParam.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        riderLatestPositionParam.setOrderId(deliveryOrderDetailRequest.getOutOrderId());
        riderLatestPositionParam.setPushTime((int)(System.currentTimeMillis()/1000));
        RiderLatestPositionDto riderLatestPosition =  shunFengRpc.riderLatestPosition(riderLatestPositionParam);
        DeliveryOrderDetailResponse deliveryOrderDetailResponse = new DeliveryOrderDetailResponse();
        deliveryOrderDetailResponse.setTransporterLat(riderLatestPosition.getRiderLat());
        deliveryOrderDetailResponse.setTransporterLng(riderLatestPosition.getRiderLng());
        return ExtResponse.buildSuccess(deliveryOrderDetailResponse);
    }
}
