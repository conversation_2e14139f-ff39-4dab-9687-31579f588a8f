package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.support.store.response.ThirdStoreBo;
import com.jdh.o2oservice.vertical.ShunFengApp;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.store.StoreServiceRpc;
import com.jdh.o2oservice.core.domain.support.store.request.CreateStoreCmd;
import com.jdh.o2oservice.domain.angel.core.ext.CreateThirdStoreExt;
import com.jdh.o2oservice.domain.angel.core.ext.dto.ThirdStoreInfoDto;
import com.jdh.o2oservice.domain.angel.core.ext.param.CreateThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.QueryThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.UpdateThirdStoreParam;
import com.jdh.o2oservice.ext.ship.reponse.DaDaResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName 顺丰创建门店
 * @Description
 * <AUTHOR>
 * @Date 2024/6/7 10:26 AM
 * @Version 1.0
 **/

@Extension(code = ShunFengApp.CODE)
@Slf4j
public class SfStoreCreateExtImpl implements CreateThirdStoreExt {

    /**
     * 基础地址:达达域名
     */
    @Value("${dada.base.url}")
    private String baseUrl;

    /**
     * 商户id
     */
    @Value("${dada.sourceId}")
    private String sourceId;

    /**
     * app_key
     */
    @Value("${dada.appKey}")
    private String dadaAppKey;

    /**
     * app_secret
     */
    @Value("${dada.appSecret}")
    private String dadaAppSecret;
    /**
     * duccConfig
     */
    @Autowired
    private DuccConfig duccConfig;
    /**
     *
     */
    private final Long supplierId = 2275848642305L;
    /**
     * storeServiceRpc
     */
    @Autowired
    private StoreServiceRpc storeServiceRpc;


    @Override
    public ExtResponse<Boolean> createThirdStore(CreateThirdStoreParam createThirdStoreParam) {
        log.info("我是创建门店扩展点,我执行了");
        Boolean createResult = this.createSfStore(createThirdStoreParam);

        log.info("创建顺丰门店扩展点,门店创建{},thirdStoreId={}",createResult,createThirdStoreParam.getAngelStationId());
        return ExtResponse.buildSuccess(createResult);
    }

    /**
     * 创建顺丰门店
     * @param createThirdStoreParam
     * @return
     */
    private Boolean createSfStore(CreateThirdStoreParam createThirdStoreParam) {
        CreateStoreCmd createStoreCmd = new CreateStoreCmd();
        createStoreCmd.setCityName(createThirdStoreParam.getCityName());
        createStoreCmd.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        createStoreCmd.setSupplierId(supplierId);
        createStoreCmd.setOutShopId(String.valueOf(createThirdStoreParam.getAngelStationId()));
        createStoreCmd.setShopAddress(createThirdStoreParam.getStoreAddr());
        // 2-药品
        createStoreCmd.setShopProductTypes("2");
        createStoreCmd.setShopContactName(createThirdStoreParam.getContactName());
        createStoreCmd.setShopContactPhone(createThirdStoreParam.getContactPhone());

        long time = System.currentTimeMillis() / CommonConstant.NUMBER_THOUSAND;
        createStoreCmd.setPushTime(Integer.parseInt(String.valueOf(time)));
        createStoreCmd.setLatitude(String.valueOf(createThirdStoreParam.getLat()));
        createStoreCmd.setLongitude(String.valueOf(createThirdStoreParam.getLng()));
        createStoreCmd.setShopName(createThirdStoreParam.getStoreName());
        return storeServiceRpc.createThirdSfStore(createStoreCmd);
    }

    /**
     * 查询第三方门店扩展点
     *
     * @param queryThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<ThirdStoreInfoDto> queryThirdStore(QueryThirdStoreParam queryThirdStoreParam) {
        log.info("我是查询门店扩展点,我执行了");
        CreateStoreCmd createStoreCmd = new CreateStoreCmd();
        createStoreCmd.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        createStoreCmd.setSupplierId(supplierId);
        createStoreCmd.setOutShopId(String.valueOf(queryThirdStoreParam.getAngelStationId()));
        // 2-药品
        createStoreCmd.setShopProductTypes(CommonConstant.TWO_STR);
        long time = System.currentTimeMillis() / CommonConstant.NUMBER_THOUSAND;
        createStoreCmd.setPushTime(Integer.parseInt(String.valueOf(time)));
        ThirdStoreBo thirdStoreBo = storeServiceRpc.queryShopInfo(createStoreCmd);
        if(Objects.isNull(thirdStoreBo)){
            return ExtResponse.buildSuccess(null);
        }
        ThirdStoreInfoDto thirdStoreInfoDto = new ThirdStoreInfoDto();
        thirdStoreInfoDto.setStationName(thirdStoreBo.getShopName());
        return ExtResponse.buildSuccess(thirdStoreInfoDto);
    }

    /**
     * 创建第三方门店扩展点
     *
     * @param updateThirdStoreParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> updateThirdStore(UpdateThirdStoreParam updateThirdStoreParam) {
        log.info("我是更新门店扩展点,我执行了,updateThirdStoreParam={}", updateThirdStoreParam);
        Boolean result = this.updateDadaShop(updateThirdStoreParam);
        if(!result){
            log.info("我是更新门店扩展点,门店更新失败!!!");
            return ExtResponse.buildSuccess(false);
        }
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 更新达达门店
     * @param updateThirdStoreParam
     * @return
     */
    private Boolean updateDadaShop(UpdateThirdStoreParam updateThirdStoreParam) {
        CreateStoreCmd createStoreCmd = new CreateStoreCmd();
        createStoreCmd.setCityName(updateThirdStoreParam.getCityName());
        createStoreCmd.setDevId(Integer.parseInt(duccConfig.getShunFengConfig().getDevId()));
        createStoreCmd.setSupplierId(supplierId);
        createStoreCmd.setOutShopId(String.valueOf(updateThirdStoreParam.getOutShopId()));
        createStoreCmd.setShopAddress(updateThirdStoreParam.getStoreAddr());
        // 2-药品
        createStoreCmd.setShopProductTypes(CommonConstant.TWO_STR);
        createStoreCmd.setShopContactName(updateThirdStoreParam.getContactName());
        createStoreCmd.setShopContactPhone(updateThirdStoreParam.getContactPhone());

        long time = System.currentTimeMillis() / CommonConstant.NUMBER_THOUSAND;
        createStoreCmd.setPushTime(Integer.parseInt(String.valueOf(time)));
        createStoreCmd.setLatitude(String.valueOf(updateThirdStoreParam.getLat()));
        createStoreCmd.setLongitude(String.valueOf(updateThirdStoreParam.getLng()));
        createStoreCmd.setShopName(updateThirdStoreParam.getStoreName());
        return storeServiceRpc.updateThirdSfStore(createStoreCmd);
    }

    /**
     * 调用商家信息
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = baseUrl.concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        Map<String, Object> paramMap = generateParams(requestObj);
        log.info("paramMap={}", JSON.toJSONString(paramMap));

        String httpResponse = SimpleHttpClient.simplePost(requestUrl, headMap, JSON.toJSONString(paramMap));
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        DaDaResponse response = JSON.parseObject(httpResponse, new TypeReference<DaDaResponse>() {
        });
        if (Objects.nonNull(response) && Objects.equals(response.getStatus(), "success")) {
            Object result = response.getResult();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        } else {
            log.error("[XfylOrderShipRpcServiceImpl->processResponse],达达接口访问返回异常!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 组装请求参数
     *
     * @param body
     * @return
     */
    private Map<String, Object> generateParams(Object body) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("source_id", sourceId);
        data.put("app_key", dadaAppKey);
        data.put("timestamp", LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
        data.put("format", "json");
        data.put("v", "1.0");
        data.put("body", JSON.toJSONString(body));
        data.put("signature", signature(data, dadaAppSecret));
        return data;
    }

    /**
     * 生成签名(具体参考文档: http://newopen.imdada.cn/#/quickStart/develop/safety?_k=kklqac)
     */
    private String signature(Map<String, Object> data, String appSecret) {
        // 请求参数按照【属性名】字典升序排序后，按照属性名+属性值拼接
        String signStr = data.keySet().stream()
                .sorted()
                .map(it -> String.format("%s%s", it, data.get(it)))
                .collect(Collectors.joining(""));

        // 拼接后的结果首尾加上appSecret
        String finalSignStr = appSecret + signStr + appSecret;

        // MD5加密并转为大写
        return DigestUtils.md5Hex(finalSignStr).toUpperCase();
    }
}
