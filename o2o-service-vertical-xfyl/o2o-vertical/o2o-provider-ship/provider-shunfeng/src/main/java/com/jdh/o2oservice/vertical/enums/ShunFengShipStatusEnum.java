package com.jdh.o2oservice.vertical.enums;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName SsShipStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/9/29 23:11
 */
@Getter
@AllArgsConstructor
public enum ShunFengShipStatusEnum {

    WAITING_RECEIVE(1,  "待骑手接单", StanderAngelShipStatusEnum.WAITING_RECEIVE_ORDER),

    WAITING_TRANSFER(22,  "骑手转单", StanderAngelShipStatusEnum.WAITING_TRANSFER),

    INPLACE_PICKUPING(10,  "骑手正在赶来", StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS),

    INPLACE_PICKUPED(12,  "骑手已上门", StanderAngelShipStatusEnum.KNIGHT_REACH_STORE),

    DELIVERY_SENDING(15,  "配送中", StanderAngelShipStatusEnum.DELIVERING_GOODS),

    FINISH_COMPLETE(17,  "已送到实验室", StanderAngelShipStatusEnum.ORDER_SHIP_FINISH),

    ORDER_EXCEPTION(91,  "订单异常回调",null),

    FINISH_CANCELED(2,  "取消订单", StanderAngelShipStatusEnum.ORDER_SHIP_CANCEL),

    ;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单状态描述
     */
    private String statusDesc;


    /**
     * 标准订单状态
     */
    private StanderAngelShipStatusEnum shipStatusEnum;


    /**
     * 转换成标准运单状态
     * @param order_status
     * @return
     */
    public static Integer transferToStandShipStatus(Integer order_status) {
        for(ShunFengShipStatusEnum shunFengShipStatusEnum : ShunFengShipStatusEnum.values()){
            if(shunFengShipStatusEnum.status.equals(order_status)){
                if(shunFengShipStatusEnum.shipStatusEnum!=null){
                    return shunFengShipStatusEnum.shipStatusEnum.getShipStatus();
                }
            }
        }
        throw new BusinessException(new DynamicErrorCode("-1","顺丰运单状态映射标准运单状态失败"));
    }
}
