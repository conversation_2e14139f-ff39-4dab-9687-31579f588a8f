package com.jdh.o2oservice.vertical;

import com.jd.matrix.sdk.annotation.App;
import com.jd.matrix.sdk.base.BaseApp;

/**
 * @ClassName:顺丰
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/5/14 22:01
 * @Vserion: 1.0
 **/
@App(code = ShunFengApp.CODE,
        name = "顺丰",
        priority = 1,
        parserClass = ShunFengAppCodeParser.class,
        version = "1.0.0")
public class ShunFengApp extends BaseApp {

    public static final String CODE="jdh-o2o-shunfeng";
}
