package com.jdh.o2oservice.vertical.ext;

/**
 * @ClassName ShunFengPromiseLinkExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/12/24 11:03 AM
 * @Version 1.0
 **/

import com.google.common.collect.Lists;
import com.jd.jsf.gd.util.StringUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.work.WorkPromiseLinkExt;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import com.jdh.o2oservice.vertical.ShunFengApp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * @ClassName ShunFengPromiseLinkExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/12/24 11:03 AM
 * @Version 1.0
 **/
@Slf4j
@Extension(code = ShunFengApp.CODE)
public class ShunFengPromiseLinkExtImpl  implements WorkPromiseLinkExt {

    @Resource
    private CreateShipExt shunFengCreateShipExtImpl;

    private static final List<Integer> STATUS_LIST = Lists.newArrayList(2, 100, 3);

    /**
     * 查询H5页面骑手轨迹
     * @param trackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryTrackResponse> getTransferTrack(TrackParam trackParam) {
        if(Objects.isNull(trackParam.getOrderId())
                || (Objects.nonNull(trackParam) && !STATUS_LIST.contains(trackParam.getShipStatus()))) {
            return ExtResponse.buildFail(null);
        }

        DeliveryTrackParam deliveryTrackParam = new DeliveryTrackParam();
        deliveryTrackParam.setOrderId(trackParam.getOrderId());

        DeliveryTrackResponse transferTrack = shunFengCreateShipExtImpl.getTransferTrack(deliveryTrackParam);

        if(Objects.isNull(transferTrack) || StringUtils.isBlank(transferTrack.getTrackUrl())){
            log.error("[ShunFengPromiseLinkExtImpl -> getTransferTrack],查询轨迹为空!");
            return ExtResponse.buildFail(null);
        }
        //http转成https
        transferTrack.convertToHttps();
        return ExtResponse.buildSuccess(transferTrack);
    }

    @Override
    public ExtResponse<DeliveryRealTrackResponse> getTransferRealTrack(RealTrackParam realTrackParam) {
        log.info("ShunFengPromiseLinkExtImpl getTransferRealTrack 方法未实现");
        return null;
    }
}
