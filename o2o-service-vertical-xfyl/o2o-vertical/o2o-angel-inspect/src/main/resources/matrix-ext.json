{"bizCode": "jdh-o2o-angelInspect", "version": "1.0.0", "system": "default", "baseScanPackage": "com.jdh.o2oservice.vertical", "extMappings": [{"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_WorkStatusHandleExt_checkTaskStatusAlready", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_AngelTaskExtStatusExt_getStartVerticalStatus", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_WorkGenerateExt_bizParamCheck", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_WorkGenerateExt_planTimeCalculate", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryTrackExt_queryDeliveryTrack", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryTrackExt_queryDeliveryRealTrack", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}, {"extCode": "EXT_JDH_O2O_ANGEL_PROMISE_WorkStatusHandleExt_getTaskStatus", "priority": [{"code": "jdh-o2o-angelInspect", "type": "Y"}]}]}