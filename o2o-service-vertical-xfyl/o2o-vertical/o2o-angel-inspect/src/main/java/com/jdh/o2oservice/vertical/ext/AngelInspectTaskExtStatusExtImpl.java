package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.task.AngelTaskExtStatusExt;
import com.jdh.o2oservice.vertical.AngelInspectApp;
import com.jdh.o2oservice.vertical.enums.AngelInspectBizExtStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName:AngelInspectTaskExtStatusExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/19 21:14
 * @Vserion: 1.0
 **/
@Extension(code = AngelInspectApp.CODE)
@Slf4j
public class AngelInspectTaskExtStatusExtImpl implements AngelTaskExtStatusExt {

    /**
     * 验证码验证成功状态扩展点
     *
     * @return
     */
    @Override
    public ExtResponse<Integer> getStartVerticalStatus() {
        return ExtResponse.buildSuccess(AngelInspectBizExtStatus.CODE_WRITE_OFF.getType());
    }
}
