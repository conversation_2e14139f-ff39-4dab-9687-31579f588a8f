package com.jdh.o2oservice.vertical.ext;

import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.reponse.AngelWorkPlanTime;
import com.jdh.o2oservice.ext.work.WorkGenerateExt;
import com.jdh.o2oservice.ext.work.param.WorkBizParamCheckParam;
import com.jdh.o2oservice.vertical.AngelInspectApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName:InspectWorkGenerateExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/26 19:14
 * @Vserion: 1.0
 **/
@Extension(code = AngelInspectApp.CODE)
@Slf4j
public class InspectWorkGenerateExtImpl implements WorkGenerateExt {

    /**
     * 创建工单后置处理逻辑
     *
     * @param workBizParamCheckParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> bizParamCheck(WorkBizParamCheckParam workBizParamCheckParam) {
        String angelId = workBizParamCheckParam.getAngelId();
        if(StringUtils.isBlank(angelId)){
            return ExtResponse.buildFail(false);
        }
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 计算工单计划时间
     *
     * @param workBizParamCheckParam 业务参数
     * @return
     */
    @Override
    public ExtResponse<AngelWorkPlanTime> planTimeCalculate(WorkBizParamCheckParam workBizParamCheckParam) {
        AngelWorkPlanTime angelWorkPlanTime = new AngelWorkPlanTime();
        angelWorkPlanTime.setPlanOutTime(Objects.nonNull(workBizParamCheckParam.getPlanOutTime()) ? workBizParamCheckParam.getPlanOutTime() : TimeUtils.add(workBizParamCheckParam.getWorkStartTime(), Calendar.HOUR, -1));
        return ExtResponse.buildSuccess(angelWorkPlanTime);
    }
}
