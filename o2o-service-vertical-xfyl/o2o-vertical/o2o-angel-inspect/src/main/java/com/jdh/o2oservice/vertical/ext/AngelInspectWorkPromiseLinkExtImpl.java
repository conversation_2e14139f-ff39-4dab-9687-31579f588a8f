package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelRealTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.work.WorkPromiseLinkExt;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import com.jdh.o2oservice.vertical.AngelInspectApp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelWorkPromiseLinkExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/28 13:40
 * @Vserion: 1.0
 **/
@Extension(code = AngelInspectApp.CODE)
@Slf4j
public class AngelInspectWorkPromiseLinkExtImpl implements WorkPromiseLinkExt {

    /** */
    @Value("${o2o.angelPromise.angelTrack}")
    private String angelTrackPrefix;


    /** 到家（无实验室）地址*/
    @Value("${o2o.angelPromise.angelNoLabTrack}")
    private String angelNoLabTrackPrefix;

    @Resource
    private AngelRealTrackRpc angelRealTrackRpc;

    private static final List<Integer> WORK_STATUS_LIST = Lists.newArrayList(3, 4);

    /**
     * 查询轨迹链接
     *
     * @param trackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryTrackResponse> getTransferTrack(TrackParam trackParam) {
        AssertUtils.nonNull(trackParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(trackParam.getPromiseId(), "履约单id不能为空");
        AssertUtils.hasText(trackParam.getAngelId(), "护士id不能为空");
        if(Objects.nonNull(trackParam) && !WORK_STATUS_LIST.contains(trackParam.getWorkStatus())) {
            return ExtResponse.buildFail(null);
        }

        //检查护士是否上报了实时位置
        AngelRealTrackParam angelRealTrackParam = new AngelRealTrackParam();
        angelRealTrackParam.setAngelId(Long.valueOf(trackParam.getAngelId()));
        AngelRealTrackBo angelRealTrackBo = angelRealTrackRpc.queryAngelRealTrack(angelRealTrackParam);
        //护士业务类型下如果查不到护士的实时位置不返回链接，直接返回null
        if(Objects.isNull(angelRealTrackBo) || Objects.isNull(angelRealTrackBo.getLat())){
            log.error("[AngelCareWorkPromiseLinkExtImpl -> getTransferTrack],服务者无实时位置!angelRealTrackBo={}", JSON.toJSONString(angelRealTrackBo));
            return ExtResponse.buildSuccess(null);
        }
        String prefix = angelTrackPrefix;
        if(StringUtils.hasText(trackParam.getBusinessMode()) && BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(trackParam.getBusinessMode())) {
            prefix = angelNoLabTrackPrefix;
        }
        String angelTrackUrl = String.format(prefix, trackParam.getPromiseId());
        log.info("[AngelInspectWorkPromiseLinkExtImpl.getTransferTrack],angelTrackUrl={}", angelTrackUrl);
        DeliveryTrackResponse response = new DeliveryTrackResponse();
        response.setTrackUrl(angelTrackUrl);
        log.info("[AngelInspectWorkPromiseLinkExtImpl.getTransferTrack],response={}", JSON.toJSONString(response));
        return ExtResponse.buildSuccess(response);
    }

    /**
     * 查询服务者实时轨迹
     *
     * @param realTrackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryRealTrackResponse> getTransferRealTrack(RealTrackParam realTrackParam) {
        AngelRealTrackParam angelRealTrackParam = new AngelRealTrackParam();
        BeanUtils.copyProperties(realTrackParam, angelRealTrackParam);
        angelRealTrackParam.setAngelId(Long.valueOf(realTrackParam.getAngelId()));
        AngelRealTrackBo angelRealTrackBo = angelRealTrackRpc.queryAngelRealTrack(angelRealTrackParam);
        DeliveryRealTrackResponse realTrackResponse = new DeliveryRealTrackResponse();
        BeanUtils.copyProperties(angelRealTrackBo, realTrackResponse);
        return ExtResponse.buildSuccess(realTrackResponse);
    }
}
