package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.address4.core.domain.address.export.dto.model.decorator.coordinate.CoordinateDecoratorDTO;
import com.jd.address4.horizontal.biz.chinamain.domain.address.export.dto.coordinate.ChinaMainCoordinateDecoratorDTO;
import com.jd.addresstranslation.api.address.GBAddressToJDAddressService;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.BaseResponse;
import com.jd.addresstranslation.api.base.JDAddressRequestInfo;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.core.domain.trade.bo.XfylOrderUserAddressBO;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderTrackRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.jd.address4.core.description.AddressCodeDesc;
import com.jd.address4.core.description.AddressModelDesc;
import com.jd.address4.core.domain.address.export.dto.AddressCollectionDTO;
import com.jd.address4.core.domain.address.export.dto.model.decorator.enc.EncDecoratorDTO;
import com.jd.address4.core.domain.address.export.dto.model.item.AbstractAddressItemDTO;
import com.jd.address4.core.domain.address.export.dto.model.item.DefaultAddressItemDTO;
import com.jd.address4.core.domain.address.export.param.AbstractAddressItemParam;
import com.jd.address4.core.domain.address.export.param.AddressCollectionParam;
import com.jd.address4.core.domain.address.export.param.DefaultAddressItemParam;
import com.jd.address4.core.export.param.AddressQueryParam;
import com.jd.address4.core.export.result.AddressQueryResult;
import com.jd.address4.core.export.service.AddressReadExportService;
import com.jd.trade2.base.export.biztag.HorizontalTag;
import com.jd.trade2.base.export.biztag.VerticalTag;
import com.jd.trade2.base.export.identity.IdentityInfoParam;
import com.jd.trade2.base.export.identity.systeminfo.SystemInfo;
import com.jd.trade2.base.export.identity.tag.RequestTagParam;
import com.jd.trade2.base.export.model.adapter.AdapterManagerImpl;
import com.jd.trade2.base.export.model.domain.DomainCollectionDTO;
import com.jd.trade2.base.export.model.domain.DomainCollectionParam;
import com.jd.trade2.base.export.result.Result;
import com.jd.trade2.base.export.sign.SignParam;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class OrderTrackRpcServiceImpl implements OrderTrackRpcService {

    /**
     * 调用方终端来源
     */
    public static final Integer APP = 2;

    /**
     * 调用方应用所在的机房名称
     */
    public static final String CENTER_LF = "lf";

    /**
     * 常用地址export版本号
     */
    public static final String SDK_VERSION = "1.1.2-SNAPSHOT";
    /**
     * SUCCESS
     */
    private static final int SUCCESS = 200;
    /**
     * 语言
     */
    public static final String LANGUAGE_CODE = "zh_CN";
    
    /**
     * 查询用户收货地址接口
     */
    @Resource
    private AddressReadExportService addressReadExportService;
    /**
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=138897353
     * iAddressMapService
     */
    @Resource
    private GBAddressToJDAddressService gbAddressToJDAddressService;


    /**
     * 查询用户默认收货地址信息
     *
     * @param userPin
     */
    @Override
    public XfylOrderUserAddressBO queryOrderUserAddressInfo(String userPin,Long addressId) {
        try {
            AddressCollectionDTO addressCollectionDTO = getAddressCollectionDTO(userPin);
            //转换结果
            XfylOrderUserAddressBO bo = new XfylOrderUserAddressBO();
            bo.setUserPin(userPin);
            bo.setAddressId(addressId);
            bo.setOnlyQueryLatAndLng(false);
            convertAddressCollectionDTO(addressCollectionDTO,bo);
            return bo;
        } catch (BusinessException e) {
            throw e;
        } catch (Throwable e) {
            log.error("OrderTrackRpcServiceImpl->queryOrderUserAddressInfo error:", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 查询用户默认收货地址信息对应的经纬度
     *
     * @param userPin
     */
    @Override
    public XfylOrderUserAddressBO queryUserDefaultAddressLngAndLat(String userPin) {
        try {
            AddressCollectionDTO addressCollectionDTO = getAddressCollectionDTO(userPin);
            //转换结果
            XfylOrderUserAddressBO bo = new XfylOrderUserAddressBO();
            bo.setUserPin(userPin);
            bo.setOnlyQueryLatAndLng(true);
            convertAddressCollectionDTO(addressCollectionDTO,bo);
            log.info("OrderTrackRpcServiceImpl->queryUserDefaultAddressLngAndLat end,bo={}", bo);
            return bo;
        } catch (BusinessException e) {
            throw e;
        } catch (Throwable e) {
            log.error("OrderTrackRpcServiceImpl->queryUserDefaultAddressLngAndLat error,msg={}", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }
    /**
     * 根据经纬度获取地址
     * @param jdAddressRequestInfo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.OrderTrackRpcServiceImpl.getAddressByLatAndLon")
    public BaseAddressInfo getAddressByLatAndLon(JDAddressRequestInfo jdAddressRequestInfo) {
        try{
            BaseResponse<BaseAddressInfo> result = gbAddressToJDAddressService.getJDDistrictFromLatlng(CommonConstant.APP_KEY_FOR_JDDISTRICTFROMLATLNG, jdAddressRequestInfo);
            if (result == null || SUCCESS != result.getStatus()){
                return null;
            }
            return result.getResult();
        }catch (Throwable e){
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.OrderTrackRpcServiceImpl.getAddressByLatAndLon", e.getMessage());
            log.info("OrderTrackRpcServiceImpl -> getAddressByLatAndLon end, jdAddressRequestInfo={},errMessage={}", JsonUtil.toJSONString(jdAddressRequestInfo),e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }


    /**
     *
     * @param userPin
     * @return
     */
    private AddressCollectionDTO getAddressCollectionDTO(String userPin){
        //业务身份信息
        IdentityInfoParam identityInfoParam = buildIdentityInfoParam();
        //构建地址信息
        AddressQueryParam addressQueryParam = buildAddressQueryParam(userPin);
        //调用
        log.info("OrderTrackRpcServiceImpl->queryOrderUserAddressInfo 查询用户收货地址 地址入参 ={}", JSON.toJSONString(addressQueryParam));
        Result<AddressQueryResult> result = addressReadExportService.queryAddress(identityInfoParam,  addressQueryParam);
        log.info("OrderTrackRpcServiceImpl->queryOrderUserAddressInfo 查询用户收货地址 出参 ={}", JSON.toJSONString(result));

        // 判断RPC调用是否成功
        if (result == null || StringUtils.isEmpty(result.getResultCode())) {
            // 调添加地址接口失败 异常处理 例如下面
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        if (AddressCodeDesc.ADDRESS_NUM_LIMIT.getFullCode().equals(result.getResultCode())) {
            // 超过最大地址条数限制 异常处理 例如下面
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        if (!AddressCodeDesc.SUCCESS.getFullCode().equals(result.getResultCode())) {
            //  失败 异常处理 例如下面
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        if (result.getResultInfo() == null) {
            // 返回的查询结果为空 异常处理
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        //获取结果中的地址AddressCollectionDTO
        AddressCollectionDTO addressCollectionDTO = getResultAddressCollectionDTO(result.getResultInfo().getDomainCollectionDTOs());
        log.info("OrderTrackRpcServiceImpl->queryOrderUserAddressInfo 地址集合={}", JSON.toJSONString(addressCollectionDTO));
        if (addressCollectionDTO == null || CollectionUtils.isEmpty(addressCollectionDTO.getChildList())) {
            log.warn("OrderTrackRpcServiceImpl->queryOrderUserAddressInfo 查询用户收货地址结果为空userPin={}",userPin);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        return addressCollectionDTO;
    }

    /**
     convertAddressCollectionDTO(addressCollectionDTO,bo);
     * 获取结果中的地址AddressCollectionDTO
     * @param domainCollectionDTOList 地址返回结果
     * @return AddressCollectionDTO
     */
    private AddressCollectionDTO getResultAddressCollectionDTO(List<DomainCollectionDTO> domainCollectionDTOList) {

        if (CollectionUtils.isEmpty(domainCollectionDTOList)) {
            //返回的查询结果为空
            return null;
        }
        AddressCollectionDTO addressCollectionDTO = null;
        for (DomainCollectionDTO domainCollectionDTO : domainCollectionDTOList) {
            if (domainCollectionDTO != null && domainCollectionDTO.getType().isAdapt(AddressModelDesc.ADDRESS_COLLECTION)) {
                Object obj = AdapterManagerImpl.getInstance()
                        .getAdapter(domainCollectionDTO, AddressCollectionDTO.class);
                if (obj instanceof AddressCollectionDTO) {
                    //地址集合
                    addressCollectionDTO = (AddressCollectionDTO) obj;
                }
                break;
            }
        }
        return addressCollectionDTO;
    }

    /**
     * 地址服务返回结果适配
     * @param addressCollectionDTO 地址服务返回的集合
     * @return 地址返回结果
     */
    public XfylOrderUserAddressBO convertAddressCollectionDTO(AddressCollectionDTO addressCollectionDTO,XfylOrderUserAddressBO bo) {
        // 将地址服务中的DomainCollectionDTO进行转换
        if (addressCollectionDTO.getType().isAdapt(AddressModelDesc.ADDRESS_COLLECTION)) {
            DefaultAddressItemDTO resultDTO = new DefaultAddressItemDTO();
            // 转换childList
            List<AbstractAddressItemDTO> childList = addressCollectionDTO.getChildList();

            // 先获取第一条地址， 因为有的用户不设置默认地址  托底
            AbstractAddressItemDTO firstAddress = childList.get(CommonConstant.ZERO);
            if (firstAddress instanceof DefaultAddressItemDTO) {
                resultDTO = (DefaultAddressItemDTO) firstAddress;
                log.info("OrderTrackRpcServiceImpl->convertAddressCollectionDTO 第一条地址={}", JSON.toJSONString(resultDTO));
            }

            //根据收货地址id获取地址信息
            if(Objects.nonNull(bo.getAddressId())){
                // 可以进行每一项获取值转换
                for (AbstractAddressItemDTO addressItemDTO : childList) {
                    if (addressItemDTO instanceof DefaultAddressItemDTO) {
                        DefaultAddressItemDTO addressItem = (DefaultAddressItemDTO) addressItemDTO;
                        // 收货地址id不为空
                        if(bo.getAddressId().equals(addressItem.getAddressId())){
                            log.info("OrderTrackRpcServiceImpl->convertAddressCollectionDTO 收货地址={}", JSON.toJSONString(addressItem));
                            resultDTO = addressItem;
                            break;
                        }
                    }
                }
            } else{
                // 可以进行每一项获取值转换
                for (AbstractAddressItemDTO addressItemDTO : childList) {
                    if (addressItemDTO instanceof DefaultAddressItemDTO) {
                        DefaultAddressItemDTO addressItem = (DefaultAddressItemDTO) addressItemDTO;
                        // 有默认地址的话用默认地址
                        if(addressItem.isDefault()){
                            log.info("OrderTrackRpcServiceImpl->convertAddressCollectionDTO 默认地址={}", JSON.toJSONString(addressItem));
                            resultDTO = addressItem;
                            //打默认标记
                            bo.setAddressDefault("默认");
                            break;
                        }
                    }
                }
            }
            if(bo.getOnlyQueryLatAndLng()){
                getAddressCoord(resultDTO,bo);
                getAddressArea(resultDTO,bo);
            }else{
                log.info("OrderTrackRpcServiceImpl->convertAddressCollectionDTO 开始数据解密");
                //处理加密数据
                getAddressEnc(resultDTO,bo);
                // 获取级联地址列表 返回的是一个有序list 顺序就是按照 省、市、区/县、镇 返回的  级联地址列表=[1,2800,55837,0]
                getAddressArea(resultDTO,bo);
                //标签  目前拿到的是code值 没有对应关系
                //getAddressLabel(resultDTO,bo);
            }
            return bo;
        }

        throw new BusinessException(BusinessErrorCode.USER_ADDRESS_CONVERT_IS_ERROR);
    }

    /**
     * 获取级联地址列表
     * @param addressItemDTO
     */
    private void getAddressArea(AbstractAddressItemDTO addressItemDTO,XfylOrderUserAddressBO bo) {
        try {
            List<Long> list = addressItemDTO.getAreaList();
            bo.setFirstAddressArea(list.get(CommonConstant.ZERO));
            bo.setSecondAddressArea(list.get(CommonConstant.ONE));
            bo.setThirdAddressArea(list.get(CommonConstant.TWO));
            if(list.size()>CommonConstant.THREE){
                bo.setFourthAddressArea(list.get(CommonConstant.THREE));
            }
            if(StringUtils.isNotBlank(bo.getFullAddress())){
                String kent = bo.getFullAddress().replace(bo.getAddressDetail(), "");
                log.info("OrderTrackRpcServiceImpl->convertAddressCollectionDTO 级联地址={}", JSON.toJSONString(kent));
                bo.setAddressAreaInfo(kent);
            }
            log.info("OrderTrackRpcServiceImpl->getAddressArea XfylOrderUserAddressBO={}", bo);
        } catch (Exception e) {
            log.warn("OrderTrackRpcServiceImpl->getAddressLabel 获取级联地址列表失败了，pin={}", bo.getUserPin());
        }
    }


    /**
     * 获取加密信息结果 明文兜底
     * 中台PaaS切量 需要用明文兜底
     * @param addressItemDTO 常用地址服务ItemDTO
     */
    private void getAddressEnc(AbstractAddressItemDTO addressItemDTO,XfylOrderUserAddressBO bo) {

        log.info("OrderTrackRpcServiceImpl->getAddressEnc 明文兜底");
        //明文兜底
        bo.setAddressDetail(addressItemDTO.getAddressDetail());
        bo.setFullAddress(addressItemDTO.getFullAddress());
        bo.setMobile(addressItemDTO.getMobile());
        bo.setUserName(addressItemDTO.getName());
        bo.setAddressId(addressItemDTO.getAddressId());
        Object obj = AdapterManagerImpl.getInstance()
                .getAdapter(addressItemDTO, EncDecoratorDTO.class);
        if (obj instanceof EncDecoratorDTO) {
            // 加密信息
            EncDecoratorDTO encDecoratorDTO = (EncDecoratorDTO) obj;
            log.info("OrderTrackRpcServiceImpl->getAddressEnc 加密数据={}", JSON.toJSONString(encDecoratorDTO));
            try {
                bo.setUserName(getValueByEnc(encDecoratorDTO.getName(), addressItemDTO.getName()));
                bo.setMobile(getValueByEnc(encDecoratorDTO.getMobile(), addressItemDTO.getMobile()));
                bo.setAddressDetail(getValueByEnc(encDecoratorDTO.getAddressDetail(), addressItemDTO.getAddressDetail()));
                bo.setFullAddress(getValueByEnc(encDecoratorDTO.getFullAddress(), addressItemDTO.getFullAddress()));
            } catch (Exception e) {
                log.warn("OrderTrackRpcServiceImpl->getAddressEnc 解密失败了，原明文={}", addressItemDTO.getName());
            }
        }
    }

    /**
     * 通过密文获取值
     * @param valueEnc 密文值
     * @param value 明文值
     * @return 解密后的值
     */
    private String getValueByEnc(String valueEnc, String value) {
        log.info("OrderTrackRpcServiceImpl->getValueByEnc 密文={}，明文={}",valueEnc,value);
        TdeClientUtil tdeClientUtil = SpringUtil.getBean(TdeClientUtil.class);
        // 兜底取明文
        String result = value;
        try {
            result = tdeClientUtil.decrypt(valueEnc);
            log.info("OrderTrackRpcServiceImpl->getValueByEnc 解密后={}", result);
            if (StringUtils.isBlank(result)) {
                result = value;
            }
        } catch (Exception e) {
            log.warn("OrderTrackRpcServiceImpl->getValueByEnc 解密失败了，原明文={}", value);
        }
        return result;
    }





    /**
     * 构建业务身份信息
     * @return
     */
    private IdentityInfoParam buildIdentityInfoParam(){
        //业务身份信息
        IdentityInfoParam identityInfoParam = new IdentityInfoParam();
        //请求标签
        RequestTagParam requestTagParam = new RequestTagParam();
        //主站
        requestTagParam.addHorizontalTag(HorizontalTag.MAIN);
        requestTagParam.setVerticalTag(VerticalTag.CHINA_MAIN);

        //调用方系统信息
        SystemInfo systemInfo = new SystemInfo();
        //调用方终端来源 PC(1), APP(2), WAP(3), QQ(4), WX(5)
        systemInfo.setUserAgentId(APP);
        //调用方应用名称
        systemInfo.setCallerAppName("physicalexamination");
        //调用方应用所在的机房名称
        systemInfo.setCallerDataCenter(CENTER_LF);
        try {
            //调用方应用所在的机器IP
            InetAddress address = InetAddress.getLocalHost();
            systemInfo.setCallerIP(address.getHostAddress());
        } catch (Exception e) {
            log.error("OrderTrackRpcServiceImpl->buildIdentityInfoParam 机器IP失败！");
            throw new BusinessException(BusinessErrorCode.USER_ADDRESS_IP_IS_ERROR);
        }
        //常用地址export版本号
        systemInfo.setSdkVersion(SDK_VERSION);
        //语言
        systemInfo.setLanguageCode(LANGUAGE_CODE);
        identityInfoParam.setRequestTags(requestTagParam);
        identityInfoParam.setSystemInfo(systemInfo);
        return identityInfoParam;
    }

    /**
     * 构建地址信息
     * @param userPin
     * @return
     */
    private AddressQueryParam buildAddressQueryParam(String userPin){
        //查询地址入参
        AddressQueryParam addressQueryParam = new AddressQueryParam();
        //调用方用户登录信息
        SignParam signParam = new SignParam();
        signParam.setPin(userPin);

        //构建查询地址集合信息
        List<DomainCollectionParam> domainCollectionParams = new ArrayList<>();
        AddressCollectionParam addressCollectionParam = new AddressCollectionParam();
        List<AbstractAddressItemParam> addressItemParams = new ArrayList<>();
        // 获取基本的地址ItemParam
        DefaultAddressItemParam defaultAddressItemParam = new DefaultAddressItemParam();
        // 用户pin 必填
        defaultAddressItemParam.setPin(userPin);
        addressItemParams.add(defaultAddressItemParam);
        addressCollectionParam.setChildList(addressItemParams);
        domainCollectionParams.add(addressCollectionParam);

        addressQueryParam.setSignParam(signParam);
        addressQueryParam.setDomainCollectionParams(domainCollectionParams);

        return addressQueryParam;
    }

    /**
     * 获取坐标信息
     *
     * @param addressItemDTO
     */
    private static void getAddressCoord(DefaultAddressItemDTO addressItemDTO,XfylOrderUserAddressBO bo) {
        // 获取坐标信息
        CoordinateDecoratorDTO coordinateDecoratorDTO = (CoordinateDecoratorDTO) AdapterManagerImpl.getInstance()
                .getAdapter(addressItemDTO, CoordinateDecoratorDTO.class);
        if (coordinateDecoratorDTO != null) {
            bo.setLat(coordinateDecoratorDTO.getGcLat());
            bo.setLng(coordinateDecoratorDTO.getGcLng());
        }
    }
}
