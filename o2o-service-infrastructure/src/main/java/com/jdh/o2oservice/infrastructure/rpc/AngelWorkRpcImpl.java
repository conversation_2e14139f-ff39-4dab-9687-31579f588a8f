package com.jdh.o2oservice.infrastructure.rpc;

/**
 * @ClassName AngelWorkRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/10/29 2:52 PM
 * @Version 1.0
 **/

import com.jdh.o2oservice.application.angelpromise.AngelWorkStatusApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.dispatch.context.QueryAngelWorkContext;
import com.jdh.o2oservice.core.domain.dispatch.model.AngelWorkData;
import com.jdh.o2oservice.core.domain.dispatch.rpc.JdhAngelWorkRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.AngelWorkBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.QueryAngelWorkParam;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkTwoQuery;
import com.jdh.o2oservice.infrastructure.rpc.convert.AngelWorkRpcConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName AngelWorkRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/10/29 2:52 PM
 * @Version 1.0
 **/
@Component
public class AngelWorkRpcImpl implements JdhAngelWorkRpc {

    @Autowired
    private AngelWorkStatusApplication angelWorkStatusApplication;


    @Override
    @LogAndAlarm
    public AngelWorkBO queryAngelWork(QueryAngelWorkParam queryAngelWorkParam) {
        AngelWorkTwoQuery angelWorkTwoQuery = AngelWorkRpcConvert.INS.toAngelWorkTwoQuery(queryAngelWorkParam);
        List<AngelWorkDetailDto> angelWorkDetailDtos =  angelWorkStatusApplication.queryAngelWorks(angelWorkTwoQuery);

        AngelWorkBO angelWorkBO = new AngelWorkBO();
        angelWorkBO.setAngelWorkDetailList(AngelWorkRpcConvert.INS.toAngelWorkDetailBO(angelWorkDetailDtos));
        return angelWorkBO;
    }
}
