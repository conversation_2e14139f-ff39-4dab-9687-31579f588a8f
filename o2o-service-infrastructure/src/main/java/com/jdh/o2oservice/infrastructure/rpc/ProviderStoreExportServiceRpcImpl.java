package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.jd.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.dto.common.AddressDTO;
import com.jd.health.xfyl.merchant.export.dto.store.StoreInfoDTO;
import com.jd.health.xfyl.merchant.export.dto.supplier.JdhStoreAccountDTO;
import com.jd.health.xfyl.merchant.export.dto.supplier.SupplierMerchantStoreInfoDTO;
import com.jd.health.xfyl.merchant.export.param.supplier.SupplierMerchantStoreInfoParam;
import com.jd.health.xfyl.merchant.export.param.supplier.SupplierMerchantStoreQueryParam;
import com.jd.health.xfyl.merchant.export.param.supplier.store.JdhStoreAccountBatchCmd;
import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jd.health.xfyl.merchant.export.service.supplier.JdhStoreAccountExport;
import com.jd.health.xfyl.merchant.export.service.supplier.SupplierMerchantStoreExportService;
import com.jd.health.xfyl.open.export.param.InStoreThirdStoreParam;
import com.jd.health.xfyl.open.export.service.inner.InStoreMerchantStoreExportService;
import com.jd.health.xfyl.open.export.service.outer.InStoreThirdDataExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.provider.bo.StationAddressBo;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.bo.SupplierMerchantStoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.converter.JdhProviderStoreConverter;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProviderStoreExportServiceRpcImpl implements ProviderStoreExportServiceRpc {

    /**
     * 门店查询
     */
    @Resource
    SupplierMerchantStoreExportService xfylProviderStoreExportService;

    /**
     * 新增店铺
     */
    @Resource
    private InStoreThirdDataExportService inStoreThirdDataExportService;


    @Resource
    private InStoreMerchantStoreExportService inStoreMerchantStoreExportService;

    /**
     * 账号相关
     */
    @Resource
    private JdhStoreAccountExport jdhStoreAccountExport;

    /**
     * 根据门店编号获取门店信息
     *
     * @param jdStoreId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreExportServiceRpcImpl#queryByStoreId")
    public StoreInfoBo queryByStoreId(String jdStoreId) {
        try {
            StoreInfoQueryParam storeInfoQueryParam = new StoreInfoQueryParam();
            storeInfoQueryParam.setJdStoreId(jdStoreId);
            JsfResult<StoreInfoDTO> jsfResult = xfylProviderStoreExportService.queryMerchantStoreDetailByParam(storeInfoQueryParam);
            if (jsfResult != null && jsfResult.isSuccess()) {
                StoreInfoBo storeInfoBo = JdhProviderStoreConverter.INSTANCE.merchantDtoToBo(jsfResult.getData());
                if (storeInfoBo != null) {
                    if ( CollUtil.isNotEmpty(storeInfoBo.getLicenseFileList())) {
                        storeInfoBo.setLicenseFileList(JdhProviderStoreConverter.INSTANCE.merchantFileDtoToBo(jsfResult.getData().getLicenseFileList()));
                    }
                    storeInfoBo.setStorePinList(jsfResult.getData() != null && CollUtil.isNotEmpty(jsfResult.getData().getStoreAccountList()) ? jsfResult.getData().getStoreAccountList().stream().map(JdhStoreAccountDTO::getPin).collect(Collectors.toList()): null);
                }
                return storeInfoBo;
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> queryByStoreId error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 根据门店编号获取门店信息
     *
     * @param jdStoreIdList
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreExportServiceRpcImpl#listByStoreIds")
    public List<StoreInfoBo> listByStoreIds(Set<String> jdStoreIdList) {
        try {
            if (CollUtil.isEmpty(jdStoreIdList)) {
                return Collections.emptyList();
            }
            jdStoreIdList = jdStoreIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            StoreInfoQueryParam storeInfoQueryParam = new StoreInfoQueryParam();
            storeInfoQueryParam.setJdStoreIds(jdStoreIdList);
            JsfResult<List<StoreInfoDTO>> jsfResult = xfylProviderStoreExportService.queryMerchantStoreListByStoreIds(storeInfoQueryParam);
            if (jsfResult != null && jsfResult.isSuccess()) {
                return JdhProviderStoreConverter.INSTANCE.merchantDtoToBo(jsfResult.getData());
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> listByStoreIds error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 根据门店参数查询门店列表
     *
     * @param storeInfoQueryParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreExportServiceRpcImpl.pageStoreByParams")
    public PageInfo<StoreInfoBo> pageStoreByParams(StoreInfoQueryParam storeInfoQueryParam) {
        try {
            if (storeInfoQueryParam == null) {
                storeInfoQueryParam = new StoreInfoQueryParam();
            }
            if (storeInfoQueryParam.getBusinessType() == null) {
                storeInfoQueryParam.setBusinessType(16);
            }
            JsfResult<PageInfo<StoreInfoDTO>> jsfResult = xfylProviderStoreExportService.queryMerchantStoreListByParam(storeInfoQueryParam);
            if (jsfResult != null && jsfResult.isSuccess()) {
                return JdhProviderStoreConverter.INSTANCE.pageMerchantDtoToPageBo(jsfResult.getData());
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> pageStoreByParams error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 查询门店信息
     *
     * @param param param
     * @return ret
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreExportServiceRpcImpl.queryMerchantStoreInfo")
    public List<SupplierMerchantStoreInfoBo> queryMerchantStoreInfo(SupplierMerchantStoreQueryParam param) {
        try {
            if (param == null) {
                param = new SupplierMerchantStoreQueryParam();
            }
            if (param.getBusinessType() == null) {
                param.setBusinessType(16);
            }
            JsfResult<List<SupplierMerchantStoreInfoDTO>> jsfResult = xfylProviderStoreExportService.queryMerchantStoreInfo(param);
            if (jsfResult.isSuccess()) {
                return JdhProviderStoreConverter.INSTANCE.merchantStoreDtoToBo(jsfResult.getData());
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> queryMerchantStoreInfo error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 查询已开通省市
     *
     * @param param param
     * @return ret
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreExportServiceRpcImpl.queryMerchantStoreAddress")
    public List<StationAddressBo> queryMerchantStoreAddress(StoreInfoQueryParam param) {
        try {
            if (param == null) {
                param = new StoreInfoQueryParam();
            }
            if (param.getBusinessType() == null) {
                param.setBusinessType(16);
            }
            JsfResult<List<AddressDTO>> jsfResult = xfylProviderStoreExportService.queryMerchantStoreAddress(param);
            if (jsfResult.isSuccess()) {
                return JdhProviderStoreConverter.INSTANCE.merchantStoreAddressDtoToBo(jsfResult.getData());
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> queryMerchantStoreInfo error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 常见实验室
     * @param inStoreThirdStoreParam
     */
    @Override
    @LogAndAlarm
    public Boolean pushStoreInfo(InStoreThirdStoreParam inStoreThirdStoreParam){
        try {
            JsfResult jsfResult = inStoreThirdDataExportService.pushStoreInfo(inStoreThirdStoreParam);
            log.info("jsfResult={}",JSON.toJSONString(jsfResult));
            if (!jsfResult.isSuccess()) {
                throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
            }
            return true;
        } catch (BusinessException be) {
            log.error("ProviderStoreExportServiceRpcImpl -> pushStoreInfo BusinessException", be);
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> pushStoreInfo Exception", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 编辑实验室
     * @param inStoreThirdStoreParam
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateMerchantStore(InStoreThirdStoreParam inStoreThirdStoreParam){
        try {
            JsfResult<Boolean> jsfResult = inStoreMerchantStoreExportService.updateMerchantStore(inStoreThirdStoreParam);
            if(!jsfResult.isSuccess()){
                throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
            }
            return jsfResult.getData();
        } catch (BusinessException be) {
            log.error("ProviderStoreExportServiceRpcImpl -> updateMerchantStore BusinessException", be);
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> updateMerchantStore Exception", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 查询实验室列表分页
     *
     * @param storeInfoQueryParam
     * @return
     */
    @Override
    @LogAndAlarm
    public PageInfo<StoreInfoDTO> queryMerchantStoreListByParam(StoreInfoQueryParam storeInfoQueryParam) {
        try {
            JsfResult<PageInfo<StoreInfoDTO>> jsfResult = xfylProviderStoreExportService.queryMerchantStoreListByParam(storeInfoQueryParam);
            log.info("jsfResult={}", JSON.toJSONString(jsfResult));
            if (jsfResult != null && jsfResult.isSuccess()) {
                return jsfResult.getData();
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> queryMerchantStoreListByParam error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 更新实验室迁移配置
     * @param supplierMerchantStoreInfoParam
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateMerchantStoreBySelect(SupplierMerchantStoreInfoParam supplierMerchantStoreInfoParam){
        try {
            JsfResult<Boolean> jsfResult = xfylProviderStoreExportService.updateMerchantStoreBySelect(supplierMerchantStoreInfoParam);
            if (jsfResult != null && jsfResult.isSuccess()) {
                return jsfResult.getData();
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> updateMerchantStoreBySelect error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 运营端批量添加账号
     *
     * @param jdhStoreAccountBatchCmd cmd
     * @return
     */
    @Override
    public Boolean batchSaveStoreAccountForMan(JdhStoreAccountBatchCmd jdhStoreAccountBatchCmd) {
        try {
            JsfResult<Boolean> jsfResult = jdhStoreAccountExport.batchSaveStoreAccountForMan(jdhStoreAccountBatchCmd);
            if (jsfResult.isSuccess() && jsfResult.getData()) {
                return jsfResult.getData();
            }
            throw new BusinessException(new DynamicErrorCode(jsfResult.getCode(), jsfResult.getMsg()));
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("ProviderStoreExportServiceRpcImpl -> updateMerchantStoreBySelect error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }
}
