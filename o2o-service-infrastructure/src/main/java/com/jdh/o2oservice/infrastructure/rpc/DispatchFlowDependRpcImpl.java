package com.jdh.o2oservice.infrastructure.rpc;

import com.jdh.o2oservice.application.angel.AngelExtApplication;
import com.jdh.o2oservice.application.angel.StationExtApplication;
import com.jdh.o2oservice.application.angelpromise.AngelPromiseQueryApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.*;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.*;
import com.jdh.o2oservice.export.angel.dto.AngelScheduleDto;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelSkillRelDto;
import com.jdh.o2oservice.export.angel.query.AngelGeoQuery;
import com.jdh.o2oservice.export.angel.query.AngelScheduleCalendarRequest;
import com.jdh.o2oservice.export.angel.query.AngelSkillDictPageRequest;
import com.jdh.o2oservice.export.angel.query.AngelStationRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkGroupCountDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkCountQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.ztools.query.QueryAngelStationRequest;
import com.jdh.o2oservice.infrastructure.rpc.convert.DispatchFlowDependConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName DispatchStationRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 16:17
 **/
@Component
@Slf4j
public class DispatchFlowDependRpcImpl implements DispatchFlowDependRpc {

    /**
     *
     */
    @Resource
    private StationExtApplication stationExtApplication;

    /**
     *
     */
    @Resource
    private AngelExtApplication angelExtApplication;

    /**
     * angelPromiseQueryApplication
     */
    @Resource
    private AngelPromiseQueryApplication angelPromiseQueryApplication;

    /**
     * promiseExtApplication
     */
    @Resource
    private PromiseExtApplication promiseExtApplication;

    /**
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public List<DispatchAngelStationBO> queryAngelStationList(DispatchQueryAngelStationParam param) {
        QueryAngelStationRequest request = DispatchFlowDependConverter.convertor.convertQueryAngelStationRequest(param);
        List<AngelStationDto> stationDtoList = stationExtApplication.queryAngelStationList(request);
        return DispatchFlowDependConverter.convertor.convertDispatchAngelStationBO(stationDtoList);
    }

    /**
     * 根据服务站id查询服务者列表信息
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public List<DispatchAngelBO> queryAngelByStationId(DispatchQueryAngelParam param) {
        AngelStationRequest request = DispatchFlowDependConverter.convertor.convertAngelStationRequest(param);
        List<JdhAngelDto> angelDtoList = angelExtApplication.queryAngelByStationId(request);
        return DispatchFlowDependConverter.convertor.convertDispatchAngelBO(angelDtoList);
    }

    /**
     * 根据angelId获取护士集合
     * @param param
     * @return
     */
    @Override
    public List<DispatchAngelBO> queryAngelByAngelIds(DispatchQueryAngelParam param) {

        List<JdhAngelDto> angelDtoList = angelExtApplication.listByAngelIds(param.getAuditProcessStatus(), param.getIntendedAngelIds());
        return DispatchFlowDependConverter.convertor.convertDispatchAngelBO(angelDtoList);
    }

    /**
     * 查询护士绑定技能列表
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public List<DispatchAngelSkillRelBO> queryAngelSkillListByAngelInfo(DispatchAngelSkillDictParam param) {
        AngelSkillDictPageRequest request = DispatchFlowDependConverter.convertor.convertAngelSkillDictPageRequest(param);
        List<JdhAngelSkillRelDto> jdhAngelSkillRelDtoList = angelExtApplication.queryAngelSkillListByAngelInfo(request);
        return DispatchFlowDependConverter.convertor.convertDispatchAngelSkillRelBO(jdhAngelSkillRelDtoList);
    }

    /**
     * 查询服务者工单聚合数据
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm
    public List<DispatchAngelWorkGroupCountBO> queryAngelWorkGroupCountDto(DispatchAngelWorkCountQuery query){
        AngelWorkCountQuery angelWorkCountQuery = DispatchFlowDependConverter.convertor.convertAngelWorkCountQuery(query);
        List<AngelWorkGroupCountDto> list = angelPromiseQueryApplication.queryAngelWorkGroupCountDto(angelWorkCountQuery);
        return DispatchFlowDependConverter.convertor.convertDispatchAngelWorkGroupCountBO(list);
    }

    /**
     * 根据lbs定位获取护士集合
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public List<DispatchAngelBO> queryAngelListByGeo(DispatchQueryLbsStationAngelParam param) {
        List<JdhAngelDto> jdhAngelDtos = stationExtApplication.queryAngelListByGeo(AngelGeoQuery.builder().address(param.getAddress()).skuNos(param.getSkuNos()).build());
        if (CollectionUtils.isEmpty(jdhAngelDtos)) {
            return Collections.emptyList();
        }
        return DispatchFlowDependConverter.convertor.convertDispatchAngelBO(jdhAngelDtos);
    }

    /**
     * 查询护士排期
     * @param angelScheduleCalendarRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public List<AngelScheduleDto> queryAngelScheduleCalendar(AngelScheduleCalendarRequest angelScheduleCalendarRequest) {
        return angelExtApplication.queryAngelScheduleCalendar(angelScheduleCalendarRequest);
    }

    /**
     * 查询履约单
     * @param promiseId
     * @return
     */
    @Override
    @LogAndAlarm
    public PromiseDto findVoucherIdByPromiseId(Long promiseId) {
        return promiseExtApplication.findVoucherIdByPromiseId(promiseId);
    }
}