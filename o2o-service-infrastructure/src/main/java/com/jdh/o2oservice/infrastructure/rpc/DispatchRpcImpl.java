package com.jdh.o2oservice.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseFreezeUser;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.rpc.DispatchRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.DispatchFreezeStatusRpcBO;
import com.jdh.o2oservice.core.domain.promise.rpc.param.DispatchFreezeParam;
import com.jdh.o2oservice.core.domain.promise.rpc.param.DispatchInvalidParam;
import com.jdh.o2oservice.export.dispatch.cmd.CancelDispatchCmd;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchAppointmentPatient;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchServiceItem;
import com.jdh.o2oservice.export.dispatch.cmd.FreezeDispatchCmd;
import com.jdh.o2oservice.export.dispatch.dto.DispatchFreezeStatusDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 派单RPC能力调用
 * @author: yangxiyu
 * @date: 2024/4/24 8:41 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class DispatchRpcImpl implements DispatchRpc {
    /** 派单应用调 */
    @Resource
    private DispatchApplication dispatchApplication;

    /**
     * 冻结派单数据
     * @param params
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DispatchRpcImpl.freeze")
    @Override
    public DispatchFreezeStatusRpcBO freeze(DispatchFreezeParam params) {

        FreezeDispatchCmd cmd = new FreezeDispatchCmd();
        cmd.setPromiseId(params.getPromiseId());
        cmd.setDispatchId(params.getPromiseId());
        cmd.setFreezeType(params.getFreezeType());
        cmd.setVerticalCode(params.getVerticalCode());
        cmd.setServiceType(params.getServiceType());


        if (CollectionUtils.isNotEmpty(params.getFreezeUser())){
            List<DispatchAppointmentPatient> cancelPatients = Lists.newArrayList();
            for (PromiseFreezeUser user : params.getFreezeUser()) {
                DispatchAppointmentPatient patient = new DispatchAppointmentPatient();
                patient.setPromisePatientId(user.getPromisePatientId());
                
                if (CollectionUtils.isNotEmpty(user.getServiceDetails())){
                    List<DispatchServiceItem> serviceItems = Lists.newArrayList();
                    for (PromiseService freezeService : user.getServiceDetails()) {
                        DispatchServiceItem serviceItem = new DispatchServiceItem();
                        serviceItem.setServiceId(freezeService.getServiceId());
                        //serviceItem.setItemId(freezeService.getServiceItemId());
                        serviceItems.add(serviceItem);
                    }
                    patient.setServiceItems(serviceItems);
                }
                cancelPatients.add(patient);
            }
            cmd.setCancelPatients(cancelPatients);
        }
        try {
            DispatchFreezeStatusDto statusDto = dispatchApplication.freezeDispatch(cmd);
            DispatchFreezeStatusRpcBO freezeBO = new DispatchFreezeStatusRpcBO();
            freezeBO.setWorkId(statusDto.getWorkId());
            freezeBO.setWorkStatus(statusDto.getWorkStatus());
            freezeBO.setHasAvailableService(statusDto.getHasAvailableService());
            return freezeBO;
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("DispatchRpcImpl->freeze error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 作废
     * @param param
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DispatchRpcImpl.invalid")
    @Override
    public Boolean invalid(DispatchInvalidParam param) {
        CancelDispatchCmd cmd = new CancelDispatchCmd();
        cmd.setPromiseId(param.getPromiseId());
        cmd.setCancelType(3);
        dispatchApplication.invalidDispatch(cmd);

        return Boolean.FALSE;
    }

    @Override
    public void cancel(Long promiseId) {
         CancelDispatchCmd cmd = new CancelDispatchCmd();
        cmd.setPromiseId(promiseId);
        cmd.setCancelType(3);
        dispatchApplication.cancelDispatch(cmd);
    }
}
