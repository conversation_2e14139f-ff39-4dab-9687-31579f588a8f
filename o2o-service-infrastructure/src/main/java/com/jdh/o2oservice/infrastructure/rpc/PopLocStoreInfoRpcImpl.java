package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.jd.boundaryless.lbs.center.spi.common.ResponseMessageTO;
import com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider;
import com.jd.boundaryless.lbs.center.spi.store.to.NearStoreInfoTO;
import com.jd.boundaryless.lbs.center.spi.store.to.NearStoreQuery;
import com.jd.common.util.StringUtils;
import com.jd.health.medical.examination.export.dto.GoodsStoreDTO;
import com.jd.health.medical.examination.export.param.AppointStoreParam;
import com.jd.health.medical.examination.export.service.SkuStoreExportService;
import com.jd.health.xfyl.merchant.export.dto.XfylProviderDTO;
import com.jd.health.xfyl.merchant.export.param.GetProviderParam;
import com.jd.health.xfyl.merchant.export.service.XfylProviderExportService;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.pop.wxo2o.spi.store.StoreService;
import com.jd.pop.wxo2o.spi.store.to.StoreInfoTO;
import com.jd.store.common.StoreRequest;
import com.jd.store.common.StoreResponse;
import com.jd.store.domain.store.StoreParam;
import com.jd.store.domain.store.StoreScoreInfoDto;
import com.jd.store.domain.store.StoreScoreQueryParam;
import com.jd.store.soa.StoreScoreRpcService;
import com.jd.store.soa.StoreTagRpcService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.PopLocStoreInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.StoreRpcBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.StoreTagBO;
import com.jdh.o2oservice.infrastructure.rpc.convert.PopStoreConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: yangxiyu
 * @date: 2023/12/28 11:04 上午
 * @version: 1.0
 */
@Slf4j
@Component
public class PopLocStoreInfoRpcImpl implements PopLocStoreInfoRpc {

    /**
     * storeService
     */
    @Resource
    private StoreService storeService;

    /**
     * storeService
     */
    @Resource
    private StoreLbsProvider storeLbsProvider;
    /**
     * 商家端商家信息
     */
    @Resource
    private XfylProviderExportService xfylProviderExportService;
    /**
     *
     */
    @Resource
    private StoreTagRpcService storeTagRpcService;
    /**
     *
     */
    @Value("jdh-service-market")
    private String appName;
    /**
     * storeScoreRpcService
     */
    @Resource
    private StoreScoreRpcService storeScoreRpcService;
    /**
     * skuStoreExportService
     */
    @Resource
    private SkuStoreExportService skuStoreExportService;

    /**
     * 按ID查询
     *
     * @param storeId storeId
     * @return {@link StoreRpcBO}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.PopLocStoreInfoRpcImpl.queryById")
    public StoreRpcBO queryById(Long storeId) {
        try {
            StoreInfoTO storeInfo = storeService.queryStoreInfoByStoreId(storeId,"");
            log.info("PopLocStoreInfoRpcImpl -> queryById, storeInfo={}", JSON.toJSONString(storeInfo));

            GetProviderParam param = new GetProviderParam();
            param.setVenderId(String.valueOf(storeInfo.getVenderId()));

            XfylProviderDTO provider = JsfRpcInvokeUtil.invoke(()->xfylProviderExportService.getProvider(param));
            return PopStoreConverter.convertor.popStore2RpcBO(storeInfo, provider);
        } catch (Throwable e) {
            log.error("PopLocStoreInfoRpcImpl -> queryById error, error ", e);
        }
        return null;
    }

    /**
     * 根据条件查询最近门店数据
     *
     * @param storeRpcBO
     * @return
     */
    @Override
    public List<NearStoreInfoTO> queryNearStores(StoreRpcBO storeRpcBO) {
        log.info("PopLocStoreInfoRpcImpl -> queryNearStores, storeRpcBO={}", JsonUtil.toJSONString(storeRpcBO));
        try {
            if(StringUtils.isBlank(storeRpcBO.getLat()) || StringUtils.isBlank(storeRpcBO.getLng())){
                return new ArrayList<>();
            }
            NearStoreQuery nearStoreQuery = new NearStoreQuery();
            nearStoreQuery.setLatitude(Double.valueOf(storeRpcBO.getLat()));
            nearStoreQuery.setLongitude(Double.valueOf(storeRpcBO.getLng()));
            nearStoreQuery.setGroupId(storeRpcBO.getGroupId());
            nearStoreQuery.setPage(CommonConstant.ONE);
            nearStoreQuery.setPageSize(CommonConstant.ONE);
            //查询附近公里范围
            nearStoreQuery.setKilometres(CommonConstant.NUMBER_FIVE_THOUSAND);
            log.info("PopLocStoreInfoRpcImpl -> queryNearStores, nearStoreQuery={}", JsonUtil.toJSONString(nearStoreQuery));
            ResponseMessageTO<List<NearStoreInfoTO>> messageTO = storeLbsProvider.queryNearStores(nearStoreQuery);
            log.info("PopLocStoreInfoRpcImpl -> queryNearStores, messageTO={}", JsonUtil.toJSONString(messageTO));
            if (messageTO.getSuccess()) {
                return messageTO.getValue();
            }
            throw new SystemException(new DynamicErrorCode(String.valueOf(messageTO.getCode()), messageTO.getMessage()));
        } catch (SystemException be) {
            log.error("PopLocStoreInfoRpcImpl -> queryNearStores exception, errorCode={},message ={}", be.getErrorCode(), be.getMessage(), be);
            throw be;
        } catch (Throwable e) {
            log.error("PopLocStoreInfoRpcImpl -> queryNearStores error, error msg={}", e.getMessage(), e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 查询门店全量表
     *
     * @param storeTagBO
     * @return
     */
    @Override
    public Map<String, String> queryStoreTagList(StoreTagBO storeTagBO) {
        StoreRequest<StoreParam> storeRequest = new StoreRequest<>();
        storeRequest.setOperator(appName);
        StoreParam storeParam = new StoreParam();
        storeParam.setStoreId(storeTagBO.getStoreId());
        storeRequest.setIn(storeParam);
        StoreResponse<Map<String, String>> mapStoreResponse = storeTagRpcService.queryStoreTagList(storeRequest);
        if(Objects.isNull(mapStoreResponse) || !org.apache.commons.lang3.StringUtils.equals(mapStoreResponse.getCode(),CommonConstant.REDIS_DEFAULT_VALUE )) {
            log.error("[PopLocStoreInfoRpcImpl->queryStoreTagList],调用门店中台查询店标异常!storeResult={}", JSON.toJSONString(mapStoreResponse));
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        return mapStoreResponse.getData();
    }

    /**
     * 查看门店评分
     *
     * @param storeId
     * @return
     */
    @Override
    public String queryStoreScoreInfo(Long storeId) {
        log.info("PopLocStoreInfoRpcImpl queryStoreScoreInfo start,storeId={}",storeId);
        StoreRequest<StoreScoreQueryParam> storeRequest = new StoreRequest<>();
        storeRequest.setOperator("jdh-service-market");
        StoreScoreQueryParam storeScoreQueryParam = new StoreScoreQueryParam();
        storeScoreQueryParam.setScoreBusinessCode(1);
        storeScoreQueryParam.setStoreIdList(Arrays.asList(storeId));
        storeRequest.setIn(storeScoreQueryParam);
        StoreResponse<List<StoreScoreInfoDto>> response = storeScoreRpcService.queryStoreScoreInfo(storeRequest);
        if(Objects.nonNull(response) && response.getCode().equals(CommonConstant.REDIS_DEFAULT_VALUE)){
            List<StoreScoreInfoDto> list = response.getData();
            if(CollectionUtil.isNotEmpty(list)){
                if(Objects.nonNull(list.get(0)) && Objects.nonNull(list.get(0).getStoreScore())){
                    return list.get(0).getStoreScore().toPlainString();
                }
            }
        }
        return "";
    }

    /**
     *  根据三级地址编号查询ES中对应门店信息
     * @param appointStoreParam
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.PopLocStoreInfoRpcImpl.queryAppointStoreList",errorReturnJsfResult = false)
    public PageInfo<GoodsStoreDTO> queryAppointStoreList(AppointStoreParam appointStoreParam) {
        try{
            JsfResult<PageInfo<GoodsStoreDTO>> result = skuStoreExportService.queryAppointStoreList(appointStoreParam);
            if (null == result) {
                return null;
            }
            if (BusinessErrorCode.SUCCESS.getCode().equals(result.getCode())) {
                log.info("PopLocStoreInfoRpcImpl->queryAppointStoreList end, result={}",JsonUtil.toJSONString(result.getData().getList()));
                return result.getData();
            } else {
                log.error("PopLocStoreInfoRpcImpl->queryAppointStoreList exception, result is null or result desc ={}" ,result.getMsg());
                return null;
            }
        }catch(BusinessException be){
            log.error("PopLocStoreInfoRpcImpl->queryAppointStoreList exception, errorCode={},message ={}" ,be.getErrorCode(),be);
            return null;
        }catch (Throwable e){
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.PopLocStoreInfoRpcImpl.queryAppointStoreList", e.getMessage());
            log.error("PopLocStoreInfoRpcImpl->queryAppointStoreList error, paramMap={}, error msg={}",JsonUtil.toJSONString(appointStoreParam), e);
            return null;
        }
    }

}
