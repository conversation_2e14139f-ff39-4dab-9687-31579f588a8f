package com.jdh.o2oservice.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.fastjson.TypeReference;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.trade.JdOrderFullExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseFreezeUser;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseInvalidUser;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.rpc.MedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.MedicalPromiseBO;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderMedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.AppointmentApiMerchantParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseFromEsBo;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseListFromEsParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.UpdateMedicalPromiseStationParam;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseBatchInvalidCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseStatusInfoCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.DirectCallMedicalPromiseSubmitCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.report.cmd.UpdateMedPromiseStationCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.MedicalPromiseRpcConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 跨域调用适配 rpc
 * @author: yangxiyu
 * @date: 2024/5/14 10:01 上午
 * @version: 1.0
 */
@Component
public class MedicalPromiseRpcImpl implements MedicalPromiseRpc, ProviderMedicalPromiseRpc {


    /**
     * 实验室检测单application
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;


    @Resource
    private JdOrderFullExtApplication jdOrderFullExtApplication;

    /**
     * 冻结履约明细
     * @param freezeUser
     * @return
     */
    @Override
    @LogAndAlarm
    public List<MedicalPromiseBO>  freeze(List<PromiseFreezeUser> freezeUser, Long promiseId) {
        MedicalPromiseStatusCmd cmd = new MedicalPromiseStatusCmd();
        cmd.setPromiseId(promiseId);
        cmd.setFreeze(YnStatusEnum.YES.getCode());
        List<MedicalPromiseStatusInfoCmd> infoCmds = Lists.newArrayList();
        for (PromiseFreezeUser user : freezeUser) {
            for (PromiseService serviceDetail : user.getServiceDetails()) {
                MedicalPromiseStatusInfoCmd infoCmd = new MedicalPromiseStatusInfoCmd();
                infoCmd.setPromisePatientId(user.getPromisePatientId());
                infoCmd.setServiceId(serviceDetail.getServiceId());
                infoCmds.add(infoCmd);
            }
        }
        cmd.setMedicalPromiseStatusInfoCmds(infoCmds);
        List<MedicalPromiseDTO>  medicalPromiseDTOS =  medicalPromiseApplication.freezeMedicalPromiseStatusBatch(cmd);
        return MedicalPromiseRpcConverter.INS.convert2MedicalPromiseBO(medicalPromiseDTOS);

    }


    @Override
    @LogAndAlarm
    public List<MedicalPromiseBO>  invalid(List<PromiseInvalidUser> invalidUsers, Long promiseId) {

        MedicalPromiseBatchInvalidCmd cmd = new MedicalPromiseBatchInvalidCmd();
        cmd.setPromiseId(promiseId);
        List<MedicalPromiseStatusInfoCmd> infoCmds = Lists.newArrayList();
        for (PromiseInvalidUser user : invalidUsers) {
            for (PromiseService serviceDetail : user.getServiceDetails()) {
                MedicalPromiseStatusInfoCmd infoCmd = new MedicalPromiseStatusInfoCmd();
                infoCmd.setPromisePatientId(user.getPromisePatientId());
                infoCmd.setServiceId(serviceDetail.getServiceId());
                infoCmds.add(infoCmd);
            }
        }
        cmd.setMedicalPromiseStatusInfoCmds(infoCmds);
        List<MedicalPromiseDTO>  medicalPromiseDTOS =  medicalPromiseApplication.invalidMedicalPromiseBatch(cmd);
        return MedicalPromiseRpcConverter.INS.convert2MedicalPromiseBO(medicalPromiseDTOS);
    }

    /**
     * 根据promiseId 查询所有实验室检测单
     *
     * @param promiseId promiseId
     * @return {@link List}<{@link MedicalPromiseBO}>
     */
    @Override
    public List<MedicalPromiseBO> findListByPromiseId(Long promiseId) {
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(promiseId).build());
        return MedicalPromiseRpcConverter.INS.convert2MedicalPromiseBO(medicalPromiseDTOList);
    }

    /**
     * 查询检测单列表
     * @param medicalPromiseListFromEsParam
     * @return
     */
    @LogAndAlarm
    @Override
    public PageDto<MedicalPromiseFromEsBo> queryMedicalPromiseListFromEs(MedicalPromiseListFromEsParam medicalPromiseListFromEsParam) {
        JdOrderFullPageParam jdOrderFullPageParam = MedicalPromiseRpcConverter.INS.toJdOrderFullPageParam(medicalPromiseListFromEsParam);
        PageDto<JdOrderFullDTO> pageDto = jdOrderFullExtApplication.queryPage(jdOrderFullPageParam);
        return JSON.parseObject(JSON.toJSONString(pageDto),new TypeReference<PageDto<MedicalPromiseFromEsBo>>(){});
    }

    /**
     * 更新检测单实验室信息
     * @param updateMedicalPromiseStationParam
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean updateMedPromiseStation(UpdateMedicalPromiseStationParam updateMedicalPromiseStationParam) {
        UpdateMedPromiseStationCmd updateMedPromiseStationCmd = MedicalPromiseRpcConverter.INS.toUpdateMedPromiseStationCmd(updateMedicalPromiseStationParam);
        return medicalPromiseApplication.updateMedicalPromiseStation(updateMedPromiseStationCmd);
    }

    /**
     * 提交预约信息给三方API商家
     * @param appointmentApiMerchantParam
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean appointmentApiMerchant(AppointmentApiMerchantParam appointmentApiMerchantParam) {
        return medicalPromiseApplication.directCallMedicalPromiseToStation(DirectCallMedicalPromiseSubmitCmd.builder()
                .medicalPromiseId(appointmentApiMerchantParam.getMedicalPromiseId())
                .directCall(YnStatusEnum.YES.getCode())
                .extJson(appointmentApiMerchantParam.getExtJson()).build());
    }

}
