package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jd.health.ares.open.platform.export.dto.home.PartnerSourceOrderDTO;
import com.jd.health.ares.open.platform.export.dto.home.PartnerSourceOrderParam;
import com.jd.health.ares.open.platform.export.service.HomeInspectionExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import com.jd.newnethp.cps.center.export.dto.inspection.InspectProjectChildDetailDTO;
import com.jd.newnethp.cps.center.export.dto.inspection.InspectProjectItemDTO;
import com.jd.newnethp.cps.center.export.dto.inspection.InspectSheetInfoDTO;
import com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.trade.bo.*;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.rpc.InspectionQueryRpc;
import com.jdh.o2oservice.infrastructure.rpc.convert.InspectionQueryRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/1 15:27
 **/
@Slf4j
@Service
public class InspectionQueryRpcImpl implements InspectionQueryRpc {
    /**
     * APP_CODE
     */
    public static final String APP_CODE = "jdh-o2o-service";

    /**
     * APP_TOKEN
     */
    public static final String APP_TOKEN = "1024888";

    /**
     * CLIENT_IP
     */
    public static final String CLIENT_IP = "127.0.0.1";

    @Resource
    private InspectionQueryExport inspectionQueryExport;

    @Resource
    private HomeInspectionExportService homeInspectionExportService;

//    /**
//     * fdImSessionService
//     */
//    @Resource
//    private FdImSessionService fdImSessionService;
    /**
     * 查询检验单详情接口
     *
     * @param userPin
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.InspectionQueryRpcImpl.queryInspectSheetBySheetId")
    public InspectSheetInfoBO queryInspectSheetBySheetId(String userPin, Long sheetId) {
        try {
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            JsfResult<InspectSheetInfoDTO> result = inspectionQueryExport.queryInspectSheetBySheetId(userPin, sheetId, clientInfo);
            log.info("查询互医检验单 pin:{} 检验单 id:{} clientInfo:{} result：{}", userPin, sheetId, JSON.toJSONString(clientInfo), JSON.toJSONString(result));
            if (result.isSuccess() && result.getData() != null && StringUtils.isNotBlank(result.getData().getProjectInfo())) {
                // 从 result 中获取 InspectSheetInfoDTO，并解析 projectInfo JSON 字符串为 InspectProjectItemDTO 列表
                List<InspectProjectItemDTO> inspectProjectItemDTOS = JSONArray.parseArray(result.getData().getProjectInfo(), InspectProjectItemDTO.class);
                InspectSheetInfoBO inspectSheetInfoBO = InspectionQueryRpcConvert.INSTANCE.DTO2BO(result.getData());
                List<InspectProjectChildDetailBO> inspectProjectChildDetailBOS = new ArrayList<>();
                for (InspectProjectItemDTO projectItemDTO : inspectProjectItemDTOS) {
                    for (InspectProjectChildDetailDTO childProject : projectItemDTO.getChildProjects()) {
                        InspectProjectChildDetailBO inspectProjectChildDetailBO = new InspectProjectChildDetailBO();
                        inspectProjectChildDetailBO.setChildId(childProject.getChildId());
                        inspectProjectChildDetailBO.setChildName(childProject.getChildName());
                        inspectProjectChildDetailBO.setServiceTypeName(childProject.getServiceTypeName());
                        inspectProjectChildDetailBO.setRelateDetails(childProject.getRelateDetails());
                        inspectProjectChildDetailBO.setProjectId(childProject.getProjectId());
                        inspectProjectChildDetailBO.setProjectName(childProject.getProjectName());
                        inspectProjectChildDetailBOS.add(inspectProjectChildDetailBO);
                    }
                }
                inspectSheetInfoBO.setChildProjects(inspectProjectChildDetailBOS);
                return inspectSheetInfoBO;
            } else {
                log.info("InspectionQueryRpcImpl->queryInspectSheetBySheetId,BusinessException result={}", JSON.toJSONString(result));
                throw new BusinessException(TradeErrorCode.ORDER_SHEET_ID_ERROR);
            }
        } catch (Throwable e) {
            log.info("InspectionQueryRpcImpl->queryInspectSheetBySheetId,error={}", e.getMessage());
            throw e;
        }
    }

    /**
     * 根据检查表ID获取检查表信息
     *
     * @param inspectionQueryBO 检查查询BO对象，包含检查表ID等信息
     * @return 检查表信息BO对象
     */
    @Override
    @LogAndAlarm
    public InspectSheetInfoBO getInspectSheetBySheetId(InspectionQueryBO inspectionQueryBO) {
        try {
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            JsfResult<InspectSheetInfoDTO> result = inspectionQueryExport.queryInspectSheetBySheetId(inspectionQueryBO.getUserPin(), inspectionQueryBO.getSheetId(), clientInfo);
            log.info("getInspectSheetBySheetId, queryInspectSheetBySheetId, result：{}", JSON.toJSONString(result));
            if (result.isSuccess() && result.getData() != null && StringUtils.isNotBlank(result.getData().getProjectInfo())) {
                // 从 result 中获取 InspectSheetInfoDTO，并解析 projectInfo JSON 字符串为 InspectProjectItemDTO 列表
                List<InspectProjectItemDTO> inspectProjectItemDTOS = JSONArray.parseArray(result.getData().getProjectInfo(), InspectProjectItemDTO.class);
                InspectSheetInfoBO inspectSheetInfoBO = InspectionQueryRpcConvert.INSTANCE.DTO2BO(result.getData());
                List<InspectProjectChildDetailBO> inspectProjectChildDetailBOS = new ArrayList<>();
                for (InspectProjectItemDTO projectItemDTO : inspectProjectItemDTOS) {
                    for (InspectProjectChildDetailDTO childProject : projectItemDTO.getChildProjects()) {
                        InspectProjectChildDetailBO inspectProjectChildDetailBO = new InspectProjectChildDetailBO();
                        inspectProjectChildDetailBO.setChildId(childProject.getChildId());
                        inspectProjectChildDetailBO.setChildName(childProject.getChildName());
                        inspectProjectChildDetailBO.setServiceTypeName(childProject.getServiceTypeName());
                        inspectProjectChildDetailBO.setRelateDetails(childProject.getRelateDetails());
                        inspectProjectChildDetailBO.setProjectId(childProject.getProjectId());
                        inspectProjectChildDetailBO.setProjectName(childProject.getProjectName());
                        inspectProjectChildDetailBOS.add(inspectProjectChildDetailBO);
                    }
                }
                inspectSheetInfoBO.setChildProjects(inspectProjectChildDetailBOS);
                return inspectSheetInfoBO;
            } else {
                log.info("InspectionQueryRpcImpl->getInspectSheetBySheetId,BusinessException result={}", JSON.toJSONString(result));
                throw new BusinessException(TradeErrorCode.ORDER_SHEET_ID_ERROR);
            }
        } catch (Throwable e) {
            log.info("InspectionQueryRpcImpl->getInspectSheetBySheetId,error={}", e.getMessage());
            throw e;
        }
    }

    @Override
    @LogAndAlarm
    public PartnerSourceOrderBO queryPartnerSourceOrderInfo(PartnerSourceOrderQuery query) {
        PartnerSourceOrderParam param = new PartnerSourceOrderParam();
        param.setPartnerSourceOrderId(query.getPartnerSourceOrderId());
        param.setPartnerSource(query.getPartnerSource());
        param.setSaleChannelId(query.getSaleChannelId());
        com.jd.health.ares.open.platform.domain.common.JsfResult<PartnerSourceOrderDTO> jsfResult = homeInspectionExportService.queryPartnerSourceOrderInfo(param);
        log.info("queryPartnerSourceOrderInfo, queryPartnerSourceOrderInfo, result：{}", JSON.toJSONString(jsfResult));
        if(jsfResult.isSuccess()){
            return InspectionQueryRpcConvert.INSTANCE.DTO2BO(jsfResult.getData());
        }
        return null;
    }

}
