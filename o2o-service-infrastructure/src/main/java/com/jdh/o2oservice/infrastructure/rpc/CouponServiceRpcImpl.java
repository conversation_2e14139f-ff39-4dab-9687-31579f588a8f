package com.jdh.o2oservice.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.coupon.business.record.client.domain.ChannelParam;
import com.jd.coupon.business.record.client.domain.CouponGlobalParam;
import com.jd.coupon.client.FreeCouponSoaService;
import com.jd.coupon.client.domain.BatchGetCouponParam;
import com.jd.coupon.client.domain.BatchGetCouponResult;
import com.jd.coupon.client.domain.GetCouponParam;
import com.jd.coupon.client.domain.GetCouponResult;
import com.jd.coupon.soa.business.client.param.component.ComponentSku;
import com.jd.coupon.soa.business.client.param.component.GuideComponentParam;
import com.jd.coupon.soa.business.client.result.component.*;
import com.jd.coupon.soa.business.client.service.CouponGuideQueryService;
import com.jd.coupon.soa.business.client.service.GuideComponentService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.product.converter.JdCouponConvert;
import com.jdh.o2oservice.core.domain.product.rpc.CouponServiceRpc;
import com.jdh.o2oservice.core.domain.product.rpc.bo.CouponInfoBO;
import com.jdh.o2oservice.core.domain.product.rpc.bo.GetCouponResultBO;
import com.jdh.o2oservice.core.domain.product.rpc.param.BatchGetCouponRpcParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.FindCouponsRpcParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.GetCouponRpcParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * @Description 优惠券服务
 * @Date 2024/10/21 上午11:44
 * <AUTHOR>
 **/
@Service
@Slf4j
public class CouponServiceRpcImpl implements CouponServiceRpc {

    /**
     * 券购-可领券查询
     */
    @Resource
    private CouponGuideQueryService couponGuideQueryService;

    /**
     * 券购-可用券查询
     */
    @Resource
    private GuideComponentService guideComponentService;

    /**
     * 免费抢券-优惠券发放
     */
    @Resource
    private FreeCouponSoaService freeCouponSoaService;

    /**
     * ducc配置
     */
    @Resource
    private DuccConfig duccConfig;

    private static final Integer SUCCESS_CODE = 1;

    private static final Integer FAIL_CODE = 500;

    private static final String FAIL_MSG = "活动太火爆，休息一会再来吧!!";


    /**
     * 券购-可领券查询
     * https://joyspace.jd.com/pages/hwln2AHT42x5E2SZ2ZzM
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.CouponServiceRpcImpl.findJoinActives")
    public Map<String, List<CouponInfoBO>> findJoinActives(FindCouponsRpcParam param) {
        log.info("CouponServiceRpcImpl findJoinActives param={}", JSON.toJSONString(param));
        JSONObject objConfig = JSON.parseObject(duccConfig.getProductCouponConfig()).getJSONObject("findJoinActives");
        GuideComponentParam guideComponentParam = this.buildGuideComponentParam(param, objConfig);
        if (Objects.isNull(guideComponentParam)){
            return Collections.emptyMap();
        }
        ComponentResult<ComponentJoinActiveResult> componentResult = couponGuideQueryService.findJoinActives(guideComponentParam);
        log.info("CouponServiceRpcImpl findJoinActives res guideComponentParam={}, componentResult={}", JSON.toJSONString(guideComponentParam), JSON.toJSONString(componentResult));
        if (Objects.isNull(componentResult) || !componentResult.isSuccess() || componentResult.getResultCode() != SUCCESS_CODE) {
            return Collections.emptyMap();
        }
        List<SkuJoinActive> activeList = componentResult.getResultData().getSkuJoinActiveList();
        if (CollectionUtils.isEmpty(activeList)) {
            return Collections.emptyMap();
        }
        // 返回结果
        Map<String, List<CouponInfoBO>> result = new HashMap<>();
        activeList.forEach(o -> {
            List<JoinActive> actives = o.getComponentActiveList();
            if (CollectionUtils.isEmpty(actives)) {
                return;
            }
            result.put(o.getComponentSku().getSku(), JdCouponConvert.INSTANCE.joinActiveConvertToCouponInfoBOList(actives));
        });
        return result;
    }

    /**
     * 券购-可用券查询
     * https://joyspace.jd.com/pages/C07onrKuBLztBDPhqWii
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.CouponServiceRpcImpl.findCanUseCoupons")
    public Map<String, List<CouponInfoBO>> findCanUseCoupons(FindCouponsRpcParam param) {
        log.info("CouponServiceRpcImpl findCanUseCoupons param={}", JSON.toJSONString(param));
        JSONObject objConfig = JSON.parseObject(duccConfig.getProductCouponConfig()).getJSONObject("findCanUseCoupons");
        GuideComponentParam guideComponentParam = this.buildGuideComponentParam(param, objConfig);
        if (Objects.isNull(guideComponentParam)){
            return Collections.emptyMap();
        }
        ComponentResult<ComponentCanUseCouponResult> componentResult = guideComponentService.findCanUseCoupons(guideComponentParam);
        log.info("CouponServiceRpcImpl findCanUseCoupons res guideComponentParam={}, componentResult={}", JSON.toJSONString(guideComponentParam), JSON.toJSONString(componentResult));
        if (Objects.isNull(componentResult) || !componentResult.isSuccess() || componentResult.getResultCode() != SUCCESS_CODE) {
            return Collections.emptyMap();
        }
        List<SkuCanUseCoupon> activeList = componentResult.getResultData().getSkuCanUseCouponList();
        if (CollectionUtils.isEmpty(activeList)) {
            return Collections.emptyMap();
        }
        // 返回结果
        Map<String, List<CouponInfoBO>> result = new HashMap<>();
        activeList.forEach(o -> {
            List<CanUseCoupon> actives = o.getComponentCouponList();
            if (CollectionUtils.isEmpty(actives)) {
                return;
            }
            result.put(o.getComponentSku().getSku(), JdCouponConvert.INSTANCE.canUseCouponConvertToCouponInfoBOList(actives));
        });
        return result;
    }

    private GuideComponentParam buildGuideComponentParam(FindCouponsRpcParam param, JSONObject objConfig) {
        if (Objects.isNull(param)){
            return null;
        }
        if (CollectionUtils.isEmpty(param.getCouponSkuAttributeList()) || StringUtils.isBlank(param.getPin())){
            return null;
        }
        GuideComponentParam guideComponentParam = new GuideComponentParam();
        guideComponentParam.setPin(param.getPin());

        Map<String, String> componentParamMap = new HashMap<>();
        componentParamMap.put("resultCount", objConfig.getString("resultCount"));
        componentParamMap.put("orgType", objConfig.getString("orgType"));
        componentParamMap.put("platform", objConfig.getString("platform"));
        guideComponentParam.setComponentParamMap(componentParamMap);

        CouponGlobalParam couponGlobalParam = new CouponGlobalParam();
        couponGlobalParam.setAppName(objConfig.getString("appName"));
        couponGlobalParam.setFlowId(objConfig.getString("flowId"));
        guideComponentParam.setCouponGlobalParam(couponGlobalParam);

        List<ComponentSku> componentSkuList = new ArrayList<>();
        param.getCouponSkuAttributeList().forEach(sku->{
            ComponentSku componentSku = new ComponentSku();
            componentSku.setSku(sku.getSkuNo());
            Map<String, String> componentSkuMap = new HashMap<>();
            componentSkuMap.put("venderId", sku.getVenderId());
            componentSkuMap.put("isGlobal", sku.getIsGlobal());
            componentSkuMap.put("isCanUseDong", sku.getIsCanUseDong());
            componentSkuMap.put("isCanUseJing", sku.getIsCanUseJing());
            componentSkuMap.put("categoryId", sku.getCategoryId());
            componentSkuMap.put("spuId", sku.getSpuId());
            componentSkuMap.put("msbybt", sku.getMsbybt());
            componentSkuMap.put("mutexPromos", sku.getMutexPromos());
            componentSkuMap.put("vender_bizid", sku.getVender_bizid());
            componentSku.setComponentSkuMap(componentSkuMap);
            componentSkuList.add(componentSku);
        });
        guideComponentParam.setComponentSkuList(componentSkuList);
        log.info("CouponServiceRpcImpl buildGuideComponentParam guideComponentParam={}", JSON.toJSONString(guideComponentParam));
        return guideComponentParam;
    }

    /**
     * 领券接口：领取
     * https://joyspace.jd.com/pages/dRdfSawfYwDbOOmTd3RM
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.CouponServiceRpcImpl.getCoupon")
    public GetCouponResultBO getCoupon(GetCouponRpcParam param) {
        log.info("CouponServiceRpcImpl getCoupon param={}", JSON.toJSONString(param));
        GetCouponParam getCouponParam = JSON.parseObject(JSON.toJSONString(param), GetCouponParam.class);

        JSONObject objConfig = JSON.parseObject(duccConfig.getProductCouponConfig()).getJSONObject("getCoupon");
        ChannelParam channelParam = new ChannelParam();
        channelParam.setAppId(objConfig.getString("appId"));
        channelParam.setAppToken(objConfig.getString("appToken"));
        channelParam.setPlatformId(objConfig.getString("platformId"));
        channelParam.setPlatformToken(objConfig.getString("platformToken"));

        getCouponParam.setChannelParam(channelParam);
        getCouponParam.setType(1);
        getCouponParam.setRequestRetry(false);
        getCouponParam.setRequestId(UUID.randomUUID().toString());
        log.info("CouponServiceRpcImpl getCoupon getCouponParam={}", JSON.toJSONString(getCouponParam));
        GetCouponResult getCouponResult = freeCouponSoaService.getCoupon(getCouponParam);
        log.info("CouponServiceRpcImpl getCoupon res getCouponParam={}, getCouponResult={}", JSON.toJSONString(getCouponParam), JSON.toJSONString(getCouponResult));
        if (getCouponResult == null || getCouponResult.getBaseResult() == null){
            return new GetCouponResultBO(FAIL_CODE, FAIL_MSG);
        }
        return new GetCouponResultBO(getCouponResult.getBaseResult().getResultCode(), getCouponResult.getBaseResult().getResultMsg());
    }

    /**
     * 领券接口：批量领取
     * https://joyspace.jd.com/pages/dRdfSawfYwDbOOmTd3RM
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.CouponServiceRpcImpl.batchGetCoupon")
    public GetCouponResultBO batchGetCoupon(BatchGetCouponRpcParam param) {
        log.info("CouponServiceRpcImpl batchGetCoupon param={}", JSON.toJSONString(param));
        BatchGetCouponParam batchGetCouponParam = JSON.parseObject(JSON.toJSONString(param), BatchGetCouponParam.class);

        JSONObject objConfig = JSON.parseObject(duccConfig.getProductCouponConfig()).getJSONObject("getCoupon");
        ChannelParam channelParam = new ChannelParam();
        channelParam.setAppId(objConfig.getString("appId"));
        channelParam.setAppToken(objConfig.getString("appToken"));
        channelParam.setPlatformId(objConfig.getString("platformId"));
        channelParam.setPlatformToken(objConfig.getString("platformToken"));

        batchGetCouponParam.setChannelParam(channelParam);
        batchGetCouponParam.setRequestRetry(false);
        batchGetCouponParam.setRequestId(UUID.randomUUID().toString());
        log.info("CouponServiceRpcImpl batchGetCoupon batchGetCouponParam={}", JSON.toJSONString(batchGetCouponParam));
        BatchGetCouponResult batchGetCouponResult = freeCouponSoaService.batchGetCoupon(batchGetCouponParam);
        log.info("CouponServiceRpcImpl batchGetCoupon res batchGetCouponParam={}, batchGetCouponResult={}", JSON.toJSONString(batchGetCouponParam), JSON.toJSONString(batchGetCouponResult));
        if (batchGetCouponResult == null || batchGetCouponResult.getBaseResult() == null){
            return new GetCouponResultBO(FAIL_CODE, FAIL_MSG);
        }
        return new GetCouponResultBO(batchGetCouponResult.getBaseResult().getResultCode(), batchGetCouponResult.getBaseResult().getResultMsg());
    }

}
