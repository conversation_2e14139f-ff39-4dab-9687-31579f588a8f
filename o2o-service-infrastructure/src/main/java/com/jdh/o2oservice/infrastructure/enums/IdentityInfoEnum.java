package com.jdh.o2oservice.infrastructure.enums;

import com.jd.trade2.base.export.biztag.VerticalTag;
import com.jd.trade2.base.export.description.VerticalTagDescription;

import java.util.ArrayList;
import java.util.List;

/**
 * IdentityInfoEnum 业务身份
 *
 * <AUTHOR>
 * @version 2024/3/4 18:13
 **/
public enum IdentityInfoEnum {

    /**
     *
     */
    CN_RETAIL_JDH_XFYL("cn_retail_jdh_xfyl", VerticalTag.CN_RETAIL_JDH_XFYL, "消费医疗到店业务身份"),
    CN_RETAIL_JDH_XFYL_HOME("cn_retail_jdh_xfyl_home", VerticalTag.CN_RETAIL_JDH_XFYL_HOME, "消费医疗到家业务身份");

    /**
     * 业务身份
     */
    private String identity;

    /**
     * 业务身份 tag
     */
    private VerticalTagDescription verticalTag;

    /**
     * 业务身份描述
     */
    private String identityDesc;

    private static List<String> onlySubmitOrderList = new ArrayList<>();

    static {
        onlySubmitOrderList.add(CN_RETAIL_JDH_XFYL.getIdentity());
        onlySubmitOrderList.add(CN_RETAIL_JDH_XFYL_HOME.getIdentity());
    }

    public static VerticalTagDescription getVerticalTag(String identity) {
        for (IdentityInfoEnum identityInfoEnum : IdentityInfoEnum.values()) {
            if (identityInfoEnum.identity.equalsIgnoreCase(identity)) {
                return identityInfoEnum.verticalTag;
            }
        }

        return IdentityInfoEnum.valueOf(identity).verticalTag;
    }

    public static boolean onlySubmitOrder(String identity) {
        return onlySubmitOrderList.contains(identity);
    }


    IdentityInfoEnum(String identity, VerticalTagDescription verticalTag, String identityDesc) {
        this.identity = identity;
        this.verticalTag = verticalTag;
        this.identityDesc = identityDesc;
    }

    public String getIdentity() {
        return identity;
    }

    public VerticalTagDescription getVerticalTag() {
        return verticalTag;
    }

    public String getIdentityDesc() {
        return identityDesc;
    }

}
