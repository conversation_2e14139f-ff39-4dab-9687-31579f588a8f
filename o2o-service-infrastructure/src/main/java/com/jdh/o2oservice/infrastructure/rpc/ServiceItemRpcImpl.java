package com.jdh.o2oservice.infrastructure.rpc;

import com.jdh.o2oservice.application.product.ProductServiceItemExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.angelpromise.bo.ServiceItemQueryBo;
import com.jdh.o2oservice.core.domain.angelpromise.repository.rpc.ServiceItemRpc;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ServiceItemVo;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemExtQuery;
import com.jdh.o2oservice.infrastructure.rpc.convert.ProviderServiceConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 短信发送rpc实现
 * @author: yaoqinghai
 * @date: 2024/7/18
 * @version: 1.0
 */
@Component
@Slf4j
public class ServiceItemRpcImpl implements ServiceItemRpc {

    @Resource
    private ProductServiceItemExtApplication productServiceItemExtApplication;

    @Override
    @LogAndAlarm(jKey = "ServiceItemRpcImpl.queryServiceItemList")
    public List<ServiceItemVo> queryServiceItemList(ServiceItemQueryBo serviceItemQueryBo) {
        if(Objects.isNull(serviceItemQueryBo) || CollectionUtils.isEmpty(serviceItemQueryBo.getItemIds())) {
            log.error("[ServiceItemRpcImpl -> queryServiceItemList],查询服务项目列表参数异常!");
            return Lists.newArrayList();
        }
        ServiceItemExtQuery serviceItemExtQuery = new ServiceItemExtQuery();
        serviceItemExtQuery.setItemIds(serviceItemQueryBo.getItemIds());
        serviceItemExtQuery.setStationId(serviceItemQueryBo.getStationId());
        List<ServiceItemDto> serviceItemDtos = productServiceItemExtApplication.queryServiceItemList(serviceItemExtQuery);
        return ProviderServiceConverter.ins.convertToServiceItemVos(serviceItemDtos);
    }
}
