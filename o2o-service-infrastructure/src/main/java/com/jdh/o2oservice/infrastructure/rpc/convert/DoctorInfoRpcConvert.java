package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import com.jd.nethp.base.manage.export.sku.command.param.DoctorServiceOpenParam;
import com.jd.nethp.doctor.side.front.export.doctorworkstatus.param.DoctorWorkStatusPram;
import com.jdh.nethp.doctor.base.client.dto.doctorinfo.DoctorBaseInfoClientDTO;
import com.jdh.nethp.doctor.base.client.dto.doctorinfo.query.DoctorDetailClientParam;
import com.jdh.nethp.doctor.base.client.dto.doctorservice.DoctorOpenServiceClientDTO;
import com.jdh.nethp.doctor.base.client.dto.doctorservice.query.DoctorServiceQueryClientParam;
import com.jdh.nethp.doctor.entry.audit.client.param.doctor.DoctorUpdateInfoParam;
import com.jdh.o2oservice.base.util.IpUtil;
import com.jdh.o2oservice.core.domain.angel.context.JdhAngelContext;
import com.jdh.o2oservice.core.domain.angel.enums.AngelTakeOrderStatusEnum;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NethpBaseDoctorInfoClientParam;
import com.jdh.o2oservice.core.domain.angel.vo.HyAngelSkillVo;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/7 2:57 下午
 * @Description:
 */
@Mapper
public interface DoctorInfoRpcConvert {
    DoctorInfoRpcConvert ins = Mappers.getMapper(DoctorInfoRpcConvert.class);


    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "pin", source = "angelPin")
    @Mapping(target = "platformId", source = "nethpDocId")
    @Mapping(target = "angelStationId", source = "stationId")
    @Mapping(target = "jobType", source = "jobNature", qualifiedByName = "jobTypeConvert")
    @Mapping(target = "operator", source = "updateUser")
    DoctorUpdateInfoParam context2DoctorUpdateInfoParam(JdhAngelContext angelContext);

    @Named("jobTypeConvert")
    default Integer jobTypeConvert(Integer jobNature) {
        if (Objects.nonNull(JobNatureEnum.fromValue(jobNature))) {
            return JobNatureEnum.fromValue(jobNature).getHyType();
        }
        return null;
    }


    @Named("getNhpClientInfo")
    default NhpClientInfo getNhpClientInfo() {
        NhpClientInfo nhpClientInfo = new NhpClientInfo();
        nhpClientInfo.setAppCode("jdh-o2o-service");
        nhpClientInfo.setAppToken("jdh-o2o-service");
        nhpClientInfo.setClientIp(IpUtil.getInet4AddressNoException());
        return nhpClientInfo;
    }

    @Mapping(target = "doctorId", source = "nethpDocId")
    @Mapping(target = "workStatus", source = "takeOrderStatus", qualifiedByName = "convertStatus")
    DoctorWorkStatusPram context2DoctorWorkStatusPram(JdhAngelContext angelContext);


    @Named("convertStatus")
    default Integer convertStatus(Integer takeOrderStatus) {
        if (Objects.isNull(AngelTakeOrderStatusEnum.getAngelTakeOrderStatusEnum(takeOrderStatus))) {
            return null;
        }
        return AngelTakeOrderStatusEnum.getAngelTakeOrderStatusEnum(takeOrderStatus).getHyCode();
    }


    @Named("context2DoctorServiceOpenParamList")
    default List<DoctorServiceOpenParam> context2DoctorServiceOpenParamList(JdhAngelContext angelContext) {
        List<DoctorServiceOpenParam> paramList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(angelContext.getJdhAngelSkillRelList())) {
            angelContext.getJdhAngelSkillRelList().forEach(e -> {
                DoctorServiceOpenParam openParam = context2DoctorServiceOpenParam(angelContext);
                openParam.setServiceGroupId(e);
                openParam.setOnSell(Boolean.TRUE);
                paramList.add(openParam);
            });
        }
        if (CollectionUtils.isNotEmpty(angelContext.getJdhAngelNotOpenSkillList())) {
            angelContext.getJdhAngelNotOpenSkillList().forEach(e -> {
                DoctorServiceOpenParam openParam = context2DoctorServiceOpenParam(angelContext);
                openParam.setServiceGroupId(e);
                openParam.setOnSell(Boolean.FALSE);
                paramList.add(openParam);
            });
        }
        return paramList;
    }


    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "doctorPin", source = "angelPin")
    @Mapping(target = "docId", source = "nethpDocId")
    @Mapping(target = "operator", source = "updateUser")
    DoctorServiceOpenParam context2DoctorServiceOpenParam(JdhAngelContext angelContext);

    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "selectNotOpen", constant = "false")
    @Mapping(target = "doctorPin", source = "angelPin")
    @Mapping(target = "docId", source = "nethpDocId")
    @Mapping(target = "turnAllService", constant = "true")
    DoctorServiceQueryClientParam context2DoctorServiceQueryClientParam(JdhAngelContext angelContext);

    List<HyAngelSkillVo> dto2HyAngelSkillVos(List<DoctorOpenServiceClientDTO> clientDTOS);

    @Mapping(target = "nethpDocId", source = "doctorId")
    @Mapping(target = "serviceGroupId", source = "serviceGroupType")
    HyAngelSkillVo dto2HyAngelSkillVo(DoctorOpenServiceClientDTO clientDTO);

    /**
     *
     * @param param
     * @return
     */
    @Mapping(target = "doctorId", source = "platformId")
    @Mapping(target = "status", source = "platformStatus")
    DoctorDetailClientParam nethpBaseDoctorInfoClientParam2DoctorDetailClientParam(NethpBaseDoctorInfoClientParam param);

    /**
     *
     * @param dto
     * @return
     */
    @Mapping(target = "platformId", source = "doctorId")
    @Mapping(target = "name", source = "docName")
    @Mapping(target = "status", source = "status")
    NethpBaseDoctorInfoClientBo dto2NethpBaseDoctorInfoClientBo(DoctorBaseInfoClientDTO dto);
}
