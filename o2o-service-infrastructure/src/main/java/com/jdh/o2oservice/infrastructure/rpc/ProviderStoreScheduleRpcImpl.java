package com.jdh.o2oservice.infrastructure.rpc;


import com.alibaba.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.dto.XfylAppointDateDTO;
import com.jd.health.xfyl.merchant.export.param.schedule.OccupyScheduleParam;
import com.jd.health.xfyl.merchant.export.param.schedule.ReleaseScheduleParam;
import com.jd.health.xfyl.merchant.export.param.supplier.appointment.ListScheduleParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantScheduleExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.provider.bo.ListStoreScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.OccupyScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.ReleaseScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.XfylAppointDateBO;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreScheduleRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.ScheduleServiceRpc;
import com.jdh.o2oservice.infrastructure.rpc.convert.ProviderStoreConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName ProviderStoreDateScheduleRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/2 14:45
 **/
@Component
@Slf4j
public class ProviderStoreScheduleRpcImpl implements ProviderStoreScheduleRpc, ScheduleServiceRpc {

    /**
     *
     */
    @Resource
    private XfylMerchantScheduleExportService xfylMerchantScheduleExportService;

    /**
     * 查询POP门店预约排期
     * @param storeScheduleBO
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreDateScheduleRpcImpl.queryPopStoreSchedule")
    public List<XfylAppointDateBO> queryPopStoreSchedule(ListStoreScheduleBO storeScheduleBO) {
        try {
            ListScheduleParam param = ProviderStoreConverter.convertor.storeScheduleBO2ListScheduleParam(storeScheduleBO);
            log.info("ProviderStoreDateScheduleRpcImpl -> queryPopStoreSchedule, param={}", JSON.toJSONString(param));
            JsfResult<List<XfylAppointDateDTO>> result = xfylMerchantScheduleExportService.listStoreSchedule(param);
            log.info("ProviderStoreDateScheduleRpcImpl -> queryPopStoreSchedule, result={}", JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return ProviderStoreConverter.convertor.appointDate2BoList(result.getData());
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e){
            throw e;
        } catch (Throwable e) {
            log.error("ProviderStoreDateScheduleRpcImpl -> queryPopStoreSchedule error,", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 预占号源，当前是调用商家端服务，后续需要迁移到当前应用
     * 目前仅支持一个服务的号源预占
     * @param bo
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreDateScheduleRpcImpl.occupy")
    @Override
    public Boolean occupy(OccupyScheduleBO bo) {
        Long serviceId = bo.getServiceIds().get(0);
        OccupyScheduleParam param = new OccupyScheduleParam();
        param.setServiceNo(String.valueOf(serviceId));
        param.setChannelNo(bo.getChannelNo());
        param.setAppointmentStartTime(bo.getAppointmentStartTime());
        param.setStoreId(bo.getStoreId());
        return JsfRpcInvokeUtil.invoke(()->xfylMerchantScheduleExportService.occupy(param));
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderStoreDateScheduleRpcImpl.release")
    @Override
    public Boolean release(ReleaseScheduleBO bo) {
        Long serviceId = bo.getServiceIds().get(0);
        ReleaseScheduleParam param = new ReleaseScheduleParam();
        param.setServiceNo(String.valueOf(serviceId));
        param.setChannelNo(bo.getChannelNo());
        param.setAppointmentStartTime(bo.getAppointmentStartTime());
        param.setStoreId(bo.getStoreId());
        return JsfRpcInvokeUtil.invoke(()->xfylMerchantScheduleExportService.release(param));
    }
}