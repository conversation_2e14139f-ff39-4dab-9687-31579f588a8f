package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.health.xfyl.file.common.response.Response;
import com.jd.health.xfyl.file.export.soundrecording.sound.SoundRecordingExport;
import com.jd.health.xfyl.file.export.soundrecording.sound.dto.SoundRecordingDetailDTO;
import com.jd.health.xfyl.file.export.soundrecording.sound.request.SoundRecordingDetailRequest;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.rpc.SoundRecordingRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.SoundRecordingDetailBO;
import com.jdh.o2oservice.core.domain.support.rpc.param.SoundRecordingParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName SoundRecordingRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/6/17 13:28
 **/
@Service
@Slf4j
public class SoundRecordingRpcImpl implements SoundRecordingRpc {

    /**
     * soundRecordingExport
     */
    @Resource
    private SoundRecordingExport soundRecordingExport;

    /**
     *
     * @param param
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NethpAngelRpcImpl.getBaseDoctorInfo")
    @Override
    public List<SoundRecordingDetailBO> querySoundRecordingDetail(SoundRecordingParam param){
        try {
            SoundRecordingDetailRequest request = new SoundRecordingDetailRequest();
            request.setTenantNo("XFYL000001");
            request.setPromiseId(param.getPromiseId());
            request.setWorkId(param.getWorkId());
            request.setOutBusinessId(param.getOutBusinessId());
            request.setQueryElement(param.getQueryElement());
            request.setScene(param.getScene());
            log.info("SoundRecordingRpcImpl -> querySoundRecordingDetail start, request:{}", JSON.toJSONString(request));
            Response<List<SoundRecordingDetailDTO>> result = soundRecordingExport.querySoundRecordingDetail(request);
            log.info("SoundRecordingRpcImpl -> querySoundRecordingDetail end, result:{}", JSON.toJSONString(result));
            if (Objects.isNull(result) || !Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode())) {
                return new ArrayList<>();
            }
            return convertSoundRecordingDetailBO(result.getData());
        } catch (BusinessException e){
            log.error("SoundRecordingRpcImpl -> querySoundRecordingDetail BusinessException", e);
            return new ArrayList<>();
        } catch (Throwable e){
            log.error("SoundRecordingRpcImpl -> querySoundRecordingDetail exception", e);
            return new ArrayList<>();
        }
    }

    /**
     *
     * @param list
     * @return
     */
    private List<SoundRecordingDetailBO> convertSoundRecordingDetailBO(List<SoundRecordingDetailDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SoundRecordingDetailBO> result = new ArrayList<>();
        for (SoundRecordingDetailDTO soundRecordingDetailDTO : list) {
            SoundRecordingDetailBO soundRecordingDetailBO = new SoundRecordingDetailBO();
            BeanUtils.copyProperties(soundRecordingDetailDTO, soundRecordingDetailBO);
            result.add(soundRecordingDetailBO);
        }
        return result;
    }
}