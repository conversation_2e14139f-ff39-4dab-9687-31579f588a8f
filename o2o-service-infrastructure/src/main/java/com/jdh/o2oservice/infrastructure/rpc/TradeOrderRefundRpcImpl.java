package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.fce.orb.domain.CancelRequest;
import com.jd.fce.orb.domain.CancelResponse;
import com.jd.fce.orb.domain.OrbOrderCancelReasonResponse;
import com.jd.fce.orb.service.OrbOrderCancelReasonService;
import com.jd.fce.orb.service.OrbVirtualOrderCancelService;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.angelpromise.AngelPromiseQueryApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundFreezeMapping;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.trade.bo.*;
import com.jdh.o2oservice.core.domain.trade.context.OrderRefundContext;
import com.jdh.o2oservice.core.domain.trade.enums.KtOperationTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundDetail;
import com.jdh.o2oservice.core.domain.trade.rpc.TradeOrderRefundRpc;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.promise.cmd.FreezeVoucherCmd;
import com.jdh.o2oservice.export.promise.dto.FreezeStateDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.infrastructure.rpc.convert.TradeParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TradeOrderRefundRpcImpl 交易域退款rpc
 *
 * <AUTHOR>
 * @version 2024/5/1 16:14
 **/
@Slf4j
@Service
public class TradeOrderRefundRpcImpl implements TradeOrderRefundRpc {

    /**
     * orbVirtualOrderCancelService
     */
    @Resource
    private OrbVirtualOrderCancelService orbVirtualOrderCancelService;

    /**
     * 取消原因接口
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=113129073
     * orbOrderCancelReasonService
     */
    @Resource
    private OrbOrderCancelReasonService orbOrderCancelReasonService;
    /**
     * CANCEL_REASON_TYPE
     */
    private static final Integer CANCEL_REASON_TYPE = 2;
    /**
     * CANCEL_REASON_CODE
     */
    private static final Integer CANCEL_REASON_CODE = 600;
    /**
     * SOURCE
     */
    private static final String SOURCE = "173";

    /**
     * SUCCESS
     */
    private static final Integer SUCCESS = 1;

    private static final Map<String, Integer[]> REASON_CODE_MAP = Maps.newHashMap();
    static {
        REASON_CODE_MAP.put(ServiceTypeEnum.CARE.getServiceType(), new Integer[]{1, 2300});
        REASON_CODE_MAP.put(ServiceTypeEnum.TEST.getServiceType(), new Integer[]{1, 2200});
    }


    /**
     * voucherExtApplication
     */
    @Resource
    private VoucherApplication voucherApplication;
    /**
     * promiseExtApplication
     */
    @Resource
    private PromiseExtApplication promiseExtApplication;
    /**
     * tradeExtApplication
     */
    @Resource
    private TradeExtApplication tradeExtApplication;
    /** */
    @Resource
    private AngelPromiseQueryApplication angelPromiseQueryApplication;
    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     *
     */
    @Resource
    private TradeParamConverter tradeParamConverter;
    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;



    /**
     * 申请退款--快退平台
     *
     * @param detail
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeOrderRefundRpcImpl.appointmentRefund")
    public Boolean appointmentRefund(JdOrderRefundDetail detail) {
        Long orderId = 0L;
        try{
            orderId = detail.getOrderId();
            CancelRequest cancelRequest = new CancelRequest();
            cancelRequest.setOrderId(orderId);
            cancelRequest.setExtraParam(getRefundRequest(detail));
            log.info("TradeOrderRefundRpcImpl -> appointmentRefund start, cancelRequest={},",  JsonUtil.toJSONString(cancelRequest));
            // 调用虚拟订单退款，三次失败则抛异常， UMP报警
            CancelResponse cancelResponse = orbVirtualOrderCancelService.cancelOrder(cancelRequest);
            log.info("TradeOrderRefundRpcImpl -> appointmentRefund end, cancelRequest={}", JsonUtil.toJSONString(cancelResponse));
            if(null != cancelResponse){
                int code = cancelResponse.getResponseCode();
                if(1 == code && 1000 == cancelResponse.getCancelNotice()){
                    log.info("TradeOrderRefundRpcImpl -> appointmentRefund success,orderId={}", orderId);
                    return true;
                }else if( -1003 == cancelResponse.getCancelNotice()){
                    log.info("TradeOrderRefundRpcImpl -> appointmentRefund end,退款重复申请 orderId={}", orderId);
                    return true;
                }
            }
            return false;
        }catch(Throwable e){
            log.error("TradeOrderRefundRpcImpl -> appointmentRefund exception, orderId={},errMessage={}", orderId, e);
            return false;
        }
    }

    /**
     *
     * @param jdOrderRefundDetail
     * @return
     */
    private String getRefundRequest(JdOrderRefundDetail jdOrderRefundDetail){
        RefundRequestBO refundRequest = new RefundRequestBO();
        refundRequest.setOrderId(jdOrderRefundDetail.getOrderId());
        refundRequest.setCustomerPin(jdOrderRefundDetail.getUserPin());
        refundRequest.setRequestPerson(jdOrderRefundDetail.getUserPin());
        refundRequest.setRequestPersonName(jdOrderRefundDetail.getUserPin());
        refundRequest.setAcceptPerson(jdOrderRefundDetail.getUserPin());
        refundRequest.setAcceptPersonName(jdOrderRefundDetail.getUserPin());
        refundRequest.setRequestReason("其他原因");
        refundRequest.setPhoneNumber(jdOrderRefundDetail.getOrderUserPhone());
        refundRequest.setRefundAmount(jdOrderRefundDetail.getRefundAmount());
        if(Objects.nonNull(jdOrderRefundDetail.getRefundAmount()) && jdOrderRefundDetail.getRefundAmount().compareTo(BigDecimal.ZERO) == 0){
            // 0元单固定退回余额1
            refundRequest.setRefundType(1);
        } else {
            refundRequest.setRefundType(jdOrderRefundDetail.getKtRefundType());
        }
        refundRequest.setTransactionNumber(jdOrderRefundDetail.getTransactionNum());
        refundRequest.setOperationType(Objects.nonNull(jdOrderRefundDetail.getKtOperationType()) ? String.valueOf(jdOrderRefundDetail.getKtOperationType()) : String.valueOf(KtOperationTypeEnum.HALF_REFUND.getType()));
        refundRequest.setSystemId(CommonConstant.SYSTEM_ID);
        refundRequest.setApplyDate(System.currentTimeMillis());
        return JsonUtil.toJSONString(refundRequest);
    }

    /**
     * 获取取消订单原因列表
     */
    @Override
    public String getOrderCancelReasons(String serviceType) {
        try {
            Integer[] config = REASON_CODE_MAP.get(serviceType);
            Integer type;
            Integer code;
            if (Objects.nonNull(config)){
                type = config[0];
                code = config[1];
            }else{
                type = CANCEL_REASON_TYPE;
                code = CANCEL_REASON_CODE;
            }

            log.info("TradeOrderRefundRpcImpl -> getOrderCancelReasons start, cancelReasonType={}, cancelReasonCode={}, source={}", type, code, SOURCE);
            OrbOrderCancelReasonResponse result = orbOrderCancelReasonService.getOrderCancelReasons(type, code, SOURCE);
            log.info("TradeOrderRefundRpcImpl -> getOrderCancelReasons end, result={}", JsonUtil.toJSONString(result));
            if (SUCCESS != result.getResponseCode()) {
                throw new BusinessException(TradeErrorCode.REFUND_REASONS_FAIL);
            }
            return result.getOrderCancelReasons();
        }catch (Throwable e){
            log.error("TradeOrderRefundRpcImpl -> getOrderCancelReasons exception：", e);
            throw new BusinessException(TradeErrorCode.REFUND_REASONS_FAIL);
        }
    }

    /**
     * 服务单冻结/作废
     *
     * @param context
     * @return
     */
    @Override
    public FreezeStateBo freezeVoucher(OrderRefundContext context) {
        log.info("TradeOrderRefundRpcImpl freezeVoucher context={}", JSON.toJSONString(context));
        FreezeVoucherCmd freezeVoucherCmd = bulidFreezeVoucherCmd(context);
        FreezeStateDto freezeStateDto = voucherApplication.freezeVoucher(freezeVoucherCmd);
        log.info("TradeOrderRefundRpcImpl freezeVoucher freezeVoucherCmd={}, freezeStateDto={}", JSON.toJSONString(freezeVoucherCmd), JSON.toJSONString(freezeStateDto));
        AssertUtils.nonNull(freezeStateDto,TradeErrorCode.REFUND_ORDER_FREEZE_NULL);
        FreezeStateBo freezeStateBo = tradeParamConverter.convertFreezeStateBo(freezeStateDto);
        return freezeStateBo;
    }

//    /**
//     * 服务单冻结
//     *
//     * @param context
//     * @return
//     */
//    @Override
//    public FreezeStateBo freezePromise(OrderRefundContext context) {
//        FreezeVoucherCmd freezeVoucherCmd = bulidFreezePromiseCmd(context);
//        FreezeStateDto freezeStateDto = voucherApplication.freezeVoucher(freezeVoucherCmd);
//        AssertUtils.nonNull(freezeStateDto,TradeErrorCode.REFUND_ORDER_FREEZE_NULL);
//        FreezeStateBo freezeStateBo = tradeParamConverter.convertFreezeStateBo(freezeStateDto);
//        return freezeStateBo;
//    }

    /**
     * @param promiseId
     * @return
     */
    @Override
    public Long findVoucherIdByPromiseId(Long promiseId) {
        PromiseDto jdhPromise = promiseExtApplication.findVoucherIdByPromiseId(promiseId);
        if(Objects.nonNull(jdhPromise)){
            return jdhPromise.getVoucherId();
        }
        return null;
    }

    /**
     * @param sourceVoucherId
     * @return
     */
    @Override
    public List<PromiseBo> findVoucherIdBySourceVoucherId(Long sourceVoucherId) {
        List<PromiseDto> result = promiseExtApplication.getPromiseByOrderItemId(String.valueOf(sourceVoucherId));
        if(CollUtil.isNotEmpty(result)){
            return tradeParamConverter.convertToPromiseBoList(result);
        }
        return null;
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public BigDecimal getOrderRefundAmount(Long orderId) {
        return tradeExtApplication.getOrderRefundAmount(orderId);
    }

    /**
     * 查询实验室
     *
     * @param promiseIdList
     */
    @Override
    public List<JdhMedicalPromiseQueryBo> queryMedicalPromiseList(List<Long> promiseIdList) {
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(promiseIdList);
        List<MedicalPromiseDTO> result = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        return tradeParamConverter.convertToJdhMedicalPromiseQueryBoList(result);
    }

    /**
     *
     * @param promiseId
     * @return
     */
    @Override
    public TradeAngelPromiseBo getAngelPromise(Long promiseId) {
        AngelWorkDetailQuery query = new AngelWorkDetailQuery();
        query.setPromiseId(promiseId);
        query.setNotInWorkStatusList(Lists.newArrayList(8,9));
        AngelWorkDto workDto = angelPromiseQueryApplication.querySimpleAngelWork(query);
        return tradeParamConverter.convertTradeAngelPromiseBo(workDto);
    }

    /**
     * 退款冻结服务单或服务人的服务
     * @param context
     * @return
     */
    private FreezeVoucherCmd bulidFreezeVoucherCmd(OrderRefundContext context){
        FreezeVoucherCmd freezeVoucherCmd = FreezeVoucherCmd.builder().build();
        freezeVoucherCmd.setReason(context.getRefundReason());
        freezeVoucherCmd.setVoucherId(context.getVoucherId());
        if(RefundTypeEnum.ORDER_ONE_REFUND.getType().equals(context.getRefundType())
                || RefundTypeEnum.AMOUNT_REFUND.getType().equals(context.getRefundType())){
            freezeVoucherCmd.setFreezeUser(bulidPromisePatientList(context.getRefundSkuList()));
        }else{
            freezeVoucherCmd.setFreezeService(bulidPromiseServiceDetailDtoList(context));
        }
        Map<String, RefundFreezeMapping> refundFreezeConfigMap = duccConfig.getRefundFreezeConfig();
        if(CollUtil.isNotEmpty(refundFreezeConfigMap)) {
            RefundFreezeMapping refundFreezeConfig = JSONUtil.toBean(
                    JSONUtil.toJsonStr(refundFreezeConfigMap.get(context.getRefundSource() + context.getRefundType())), RefundFreezeMapping.class);
            freezeVoucherCmd.setFreezeType(refundFreezeConfig.getFreezeType());
            freezeVoucherCmd.setAllowPromiseStatus(refundFreezeConfig.getAllowStatus());
        }
        return freezeVoucherCmd;
    }

//    private FreezeVoucherCmd bulidFreezePromiseCmd(OrderRefundContext context) {
//        FreezeVoucherCmd freezeVoucherCmd = FreezeVoucherCmd.builder().build();
//        freezeVoucherCmd.setReason(context.getRefundReason());
//        freezeVoucherCmd.setVoucherId(context.getVoucherId());
//        if (RefundTypeEnum.ORDER_ONE_REFUND.getType().equals(context.getRefundType())
//                || RefundTypeEnum.AMOUNT_REFUND.getType().equals(context.getRefundType())) {
//            freezeVoucherCmd.setFreezeUser(bulidPromisePatientList(context.getRefundSkuList()));
//        } else {
//            freezeVoucherCmd.setFreezeService(bulidPromiseServiceDetailDtoList(context));
//        }
//        Map<String, RefundFreezeMapping> refundFreezeConfigMap = duccConfig.getRefundFreezeConfig();
//        if (CollUtil.isNotEmpty(refundFreezeConfigMap)) {
//            RefundFreezeMapping refundFreezeConfig = JSONUtil.toBean(
//                    JSONUtil.toJsonStr(refundFreezeConfigMap.get(context.getRefundSource() + context.getRefundType())), RefundFreezeMapping.class);
//            freezeVoucherCmd.setFreezeType(JdhProcessDataTypeEnum.PROCESS_PROMISE.getType());
//            freezeVoucherCmd.setAllowPromiseStatus(refundFreezeConfig.getAllowStatus());
//        }
//        return freezeVoucherCmd;
//    }

    /**
     * bulidPromisePatientList
     * @param refundOrderSkuList
     * @return
     */
    private List<PromisePatientDto> bulidPromisePatientList(List<RefundSku> refundOrderSkuList){
        List<PromisePatientDto> freezeUser = new ArrayList<>();
        if(CollUtil.isNotEmpty(refundOrderSkuList)){
            Map<Long,List<RefundSku>> patientServiceMap = refundOrderSkuList.stream().collect(Collectors.groupingBy(RefundSku::getPromisePatientId));
            for(Map.Entry<Long,List<RefundSku>> entry : patientServiceMap.entrySet()){
                PromisePatientDto promisePatientDto = new PromisePatientDto();
                promisePatientDto.setPromisePatientId(entry.getKey());
                List<RefundSku> list = entry.getValue();
                List<PromiseServiceDetailDto> serviceDetails = new ArrayList<>();
                list.forEach(refundSku -> {
                    PromiseServiceDetailDto promiseServiceDetailDto = new PromiseServiceDetailDto();
                    promiseServiceDetailDto.setServiceId(Long.parseLong(refundSku.getServiceId()));
                    serviceDetails.add(promiseServiceDetailDto);
                });
                promisePatientDto.setServiceDetails(serviceDetails);
                freezeUser.add(promisePatientDto);
            }
        }
        return freezeUser;
    }

    /**
     *
     * @param context
     * @return
     */
    private List<PromiseServiceDetailDto> bulidPromiseServiceDetailDtoList(OrderRefundContext context){
        List<PromiseServiceDetailDto> freezeServiceList = new ArrayList<>();
        PromiseServiceDetailDto promiseServiceDetailDto = new PromiseServiceDetailDto();
        promiseServiceDetailDto.setServiceId(Long.parseLong(context.getServiceId()));
        promiseServiceDetailDto.setPromiseId(context.getPromiseId());
        freezeServiceList.add(promiseServiceDetailDto);
        return freezeServiceList;
    }

}