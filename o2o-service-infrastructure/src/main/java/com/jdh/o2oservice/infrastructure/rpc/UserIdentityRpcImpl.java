package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.Result;
import com.jd.medicine.b2c.base.export.domain.RxClientDTO;
import com.jd.newnethp.diag.review.center.export.dto.UserIdentityDTO;
import com.jd.newnethp.diag.review.center.export.service.UserIdentityExport;
import com.jdh.o2oservice.core.domain.report.bo.UserIdentityBO;
import com.jdh.o2oservice.core.domain.report.rpc.UserIdentityRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserIdentityRpcImpl implements UserIdentityRpc {

    /**
     * APP_CODE
     */
    public static final String APP_CODE = "jdh-o2o-service";

    /**
     * CLIENT_IP
     */
    public static final String CLIENT_IP = "127.0.0.1";

    @Autowired
    private UserIdentityExport userIdentityExport;

    @Override
    public UserIdentityBO queryUserIdentity(String userPin) {
        log.info("UserIdentityRpc#queryUserIdentity userPin={}", userPin);
        if (Strings.isBlank(userPin)) {
            return null;
        }
        RxClientDTO rxClientDTO = new RxClientDTO();
        rxClientDTO.setClient(APP_CODE);
        rxClientDTO.setServerIp(CLIENT_IP);
        try {
            Result<UserIdentityDTO> userIdentityDTOResult = userIdentityExport.queryUserIdentity(rxClientDTO, userPin);
            log.info("UserIdentityRpc#queryUserIdentity result={}", JSON.toJSONString(userIdentityDTOResult));
            if (userIdentityDTOResult != null && userIdentityDTOResult.getData() != null) {
                UserIdentityDTO data = userIdentityDTOResult.getData();
                UserIdentityBO bo = new UserIdentityBO();
                bo.setType(data.getType());
                bo.setDesc(data.getDesc());
                bo.setPlusRightRemainingNum(data.getPlusRightRemainingNum());
                return bo;
            }
        } catch (Exception e) {
            log.error("UserIdentityRpc#queryUserIdentity", e);
        }
        return null;
    }
}
