package com.jdh.o2oservice.infrastructure.rpc.autobots;

import cn.hutool.json.JSONUtil;
import com.jd.llm.client.domain.autobots.AutoBotsRequest;
import com.jd.llm.client.domain.autobots.AutoBotsResult;
import com.jd.llm.client.domain.autobots.AutoBotsWfRequest;
import com.jd.llm.client.domain.autobots.BotsResponse;
import com.jd.llm.client.service.AutoBotsService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsWfRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.rpc.AutoBotsRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 智能体RPCImpl
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */
@Component
@Slf4j
public class AutoBotsRpcImpl implements AutoBotsRpc {

    /**
     * 智能体API
     */
    @Resource
    private AutoBotsService autoBotsService;

    /**
     * 发送智能问答请求，同一次会话traceId一致，reqId每次需要是新的
     *
     * @param agentId           代表要搜索的agent的唯一标识符。
     * @param token             用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    @Override
    @LogAndAlarm
    public AutoBotsResultBO searchAiRequest(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO) {
        AutoBotsRequest autoBotsRequest = AutoBotsConvert.INSTANCE.convert(autoBotsRequestBO);
        BotsResponse<AutoBotsResult> response = autoBotsService.searchAiRequest(agentId,token,autoBotsRequest);
        log.info("AutoBotsRpcImpl#searchAiRequest response:{}",JSONUtil.toJsonStr(response));


        //200 成功
        if (Objects.equals(CommonConstant.TOW_HUNDRED,response.getCode())){
            AutoBotsResult data = response.getData();
            return AutoBotsConvert.INSTANCE.convert(data);
        }

        return null;
    }

    /**
     * 获取智能问答结果，入参需要与searchAiRequest一致
     *
     * @param agentId           代表要搜索的agent的唯一标识符。
     * @param token             用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    @Override
    @LogAndAlarm
    public AutoBotsResultBO searchAiResult(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO) {
        AutoBotsRequest autoBotsRequest = AutoBotsConvert.INSTANCE.convert(autoBotsRequestBO);
        BotsResponse<AutoBotsResult> response = autoBotsService.searchAiResult(agentId,token,autoBotsRequest);
        log.info("AutoBotsRpcImpl#searchAiResult response:{}",JSONUtil.toJsonStr(response));
        if (Objects.equals(CommonConstant.TOW_HUNDRED,response.getCode())){
            AutoBotsResult data = response.getData();
            return AutoBotsConvert.INSTANCE.convert(data);
        }
        return null;
    }

    /**
     * 执行工作流程。
     *
     * @param agentId 代理人ID。
     * @param token   访问令牌。
     * @param request 工作流请求对象。
     * @return 工作流执行结果。
     */
    @Override
    @LogAndAlarm
    public AutoBotsResultBO runWorkflow(String agentId, String token, AutoBotsWfRequestBO request) {
        AutoBotsWfRequest autoBotsWfRequest = AutoBotsConvert.INSTANCE.convert(request);
        BotsResponse<AutoBotsResult> response = autoBotsService.runWorkflow(agentId,token,autoBotsWfRequest);
        log.info("AutoBotsRpcImpl#runWorkflow response:{}",JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && Objects.equals(CommonConstant.TOW_HUNDRED,response.getCode())){
            AutoBotsResult data = response.getData();
            return AutoBotsConvert.INSTANCE.convert(data);
        }

        return null;
    }

    /**
     * 获取工作流结果
     *
     * @param agentId 代理人ID
     * @param token   认证令牌
     * @param request 自动机请求对象
     * @return 自动机结果对象
     */
    @Override
    @LogAndAlarm
    public AutoBotsResultBO getWorkflowResult(String agentId, String token, AutoBotsWfRequestBO request) {
        AutoBotsWfRequest autoBotsWfRequest = AutoBotsConvert.INSTANCE.convert(request);
        BotsResponse<AutoBotsResult> response = autoBotsService.getWorkflowResult(agentId, token, autoBotsWfRequest);
        log.info("AutoBotsRpcImpl#getWorkflowResult response:{}",JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && Objects.equals(CommonConstant.TOW_HUNDRED,response.getCode())){
            AutoBotsResult data = response.getData();
            return AutoBotsConvert.INSTANCE.convert(data);
        }
        return null;
    }


}
