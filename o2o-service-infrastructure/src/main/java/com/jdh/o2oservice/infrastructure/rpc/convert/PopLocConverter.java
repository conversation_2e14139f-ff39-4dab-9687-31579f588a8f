package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.pop.order.loc.assembledflow.soa.service.domain.LocOrderCode;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.LocCodeBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * JdhPromiseInfrastructureConverter
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Mapper
public interface PopLocConverter {

    /**
     * JdhPromiseInfrastructureConverter
     */
    PopLocConverter convertor = Mappers.getMapper(PopLocConverter.class);

    List<LocCodeBO> rpcCode2CodeBO(List<LocOrderCode> codes);


}
