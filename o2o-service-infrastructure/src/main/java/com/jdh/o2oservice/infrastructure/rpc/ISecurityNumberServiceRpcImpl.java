package com.jdh.o2oservice.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.pop.order.sensitive.common.model.*;
import com.jd.pop.order.sensitive.service.ISecurityNumberLifeCycleService;
import com.jd.pop.order.sensitive.service.ISecurityNumberSearchService;
import com.jd.pop.order.sensitive.service.ISecurityNumberService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.ISecurityNumberServiceRpc;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberBindAxbResultBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberBindRecordBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberCallRecordBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberBindAxbParam;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberReleaseRpcParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description 虚拟号服务
 * @Date 2024/12/19 上午10:49
 * <AUTHOR>
 **/
@Service("iSecurityNumberServiceRpc")
@Slf4j
public class ISecurityNumberServiceRpcImpl implements ISecurityNumberServiceRpc {

    @Resource
    private ISecurityNumberService iSecurityNumberService;

    @Resource
    private ISecurityNumberLifeCycleService iSecurityNumberLifeCycleService;

    @Resource
    private ISecurityNumberSearchService iSecurityNumberSearchService;

    @Resource
    private DuccConfig duccConfig;


    /**
     * 虚拟号绑定
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ISecurityNumberServiceRpcImpl.bindAxb")
    public SecurityNumberBindAxbResultBO bindAxb(SecurityNumberBindAxbParam param) {
        try {
            BindAxbReq bindAxbReq = new BindAxbReq();
            BeanUtils.copyProperties(param,bindAxbReq);
            JSONObject obj = JSON.parseObject(duccConfig.getSecurityNumberConfig());
            bindAxbReq.setSceneType(obj.getString("sceneType"));
            bindAxbReq.setBusinessType(obj.getString("businessType"));
            bindAxbReq.setPlatformCode(obj.getString("platformCode"));
            bindAxbReq.setPoolSN(obj.getString("poolSN"));
            ResponseData<BindAxbResultDTO> result = iSecurityNumberService.bindAxb(bindAxbReq);
            log.info("ISecurityNumberServiceRpcImpl bindAxb bindAxbReq={}, result={}", JSON.toJSONString(bindAxbReq), JSON.toJSONString(result));
            if (Objects.nonNull(result) && NumConstant.NUM_200.equals(result.getCode())){
                SecurityNumberBindAxbResultBO data = new SecurityNumberBindAxbResultBO();
                BeanUtils.copyProperties(result.getData(), data);
                return data;
            }
            log.error("ISecurityNumberServiceRpcImpl bindAxb rpc error");
            throw new BusinessException(BusinessErrorCode.VIRTUAL_NUMBER_FULL_ERROR);
        } catch (Exception e) {
            log.error("ISecurityNumberServiceRpcImpl bindAxb error e", e);
            throw new BusinessException(BusinessErrorCode.VIRTUAL_NUMBER_FULL_ERROR);
        }
    }

    /**
     * 虚拟号解绑
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ISecurityNumberServiceRpcImpl.release")
    public Boolean release(SecurityNumberReleaseRpcParam param) {
        try {
            ReleaseReq releaseReq = new ReleaseReq();
            BeanUtils.copyProperties(param, releaseReq);
            log.info("ISecurityNumberServiceRpcImpl release start releaseReq={}", JSON.toJSONString(releaseReq));
            ResponseData<ReleaseResultDTO> result = iSecurityNumberLifeCycleService.release(releaseReq);
            log.info("ISecurityNumberServiceRpcImpl release releaseReq={}, result={}", JSON.toJSONString(releaseReq), JSON.toJSONString(result));
            if(Objects.nonNull(result) && NumConstant.NUM_200.equals(result.getCode())){
                return result.getData().getResult();
            }
            log.error("ISecurityNumberServiceRpcImpl release rpc error");
        } catch (Exception e) {
            log.error("ISecurityNumberServiceRpcImpl release error e", e);
            return false;
        }
        return false;
    }

    /**
     * 录音调取
     * @param buId
     * @param orderId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ISecurityNumberServiceRpcImpl.queryCallRecord")
    public SecurityNumberCallRecordBO queryCallRecord(String buId, Long orderId) {
        try {
            ResponseData<QueryCallRecordingResVO> result = iSecurityNumberService.queryCallRecord(buId, orderId);
            log.info("ISecurityNumberServiceRpcImpl queryCallRecord buId={}, orderId={}, result={}", buId, orderId, JSON.toJSONString(result));
            if(Objects.nonNull(result) && NumConstant.NUM_200.equals(result.getCode())){
                return JSON.parseObject(JSON.toJSONString(result.getData()), SecurityNumberCallRecordBO.class);
            }
            log.error("ISecurityNumberServiceRpcImpl queryCallRecord rpc error");
        } catch (Exception e) {
            log.error("ISecurityNumberServiceRpcImpl queryCallRecord error e", e);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        return null;
    }

    /**
     * 根据订单号查询当前在绑的虚拟号
     * @param orderId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ISecurityNumberServiceRpcImpl.queryOnBindingRecordByOrderId")
    public List<SecurityNumberBindRecordBO> queryOnBindingRecordByOrderId(Long orderId) {
        try {
            ResponseData<List<BindRecordDTO>> result = iSecurityNumberSearchService.queryOnBindingRecordByOrderId(orderId);
            log.info("ISecurityNumberServiceRpcImpl queryOnBindingRecordByOrderId orderId={}, result={}", orderId, JSON.toJSONString(result));
            if(Objects.nonNull(result) && NumConstant.NUM_200.equals(result.getCode())){
                return JSON.parseArray(JSON.toJSONString(result.getData()), SecurityNumberBindRecordBO.class);
            }
            log.error("ISecurityNumberServiceRpcImpl queryOnBindingRecordByOrderId rpc error");
        } catch (Exception e) {
            log.error("ISecurityNumberServiceRpcImpl queryOnBindingRecordByOrderId error e", e);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        return Lists.newArrayList();
    }
}
