package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import com.jd.medicine.b2c.base.export.domain.Result;
import com.jd.medicine.b2c.base.export.domain.RxClientDTO;
import com.jd.newnethp.diag.export.triage.dto.ClientInfoDTO;
import com.jd.newnethp.diag.export.triage.dto.assignresult.DiagAssignResultQueryParam;
import com.jd.newnethp.diag.export.triage.dto.assignresult.DiagAssignSuspendParam;
import com.jd.newnethp.diag.export.triage.dto.assignresult.DiagLatestAssignResultDTO;
import com.jd.newnethp.diag.export.triage.param.ChangeAssignSwitchStatusParam;
import com.jd.newnethp.diag.export.triage.param.OrderTransferByDoctorParam;
import com.jd.newnethp.diag.export.triage.param.OrderTransferParam;
import com.jd.newnethp.diag.export.triage.service.OrderTransferExport;
import com.jd.newnethp.diag.export.triage.service.TriageAssignExport;
import com.jd.newnethp.diag.export.triage.service.TriageInfoExport;
import com.jd.newnethp.trade.center.export.core.dto.submit.DiagOrderSubmitDTO;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.DoctorReplyParam;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.TradeCancelDiagParam;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.TradeDiagEndParam;
import com.jd.newnethp.trade.center.export.core.param.submit.param.DiagOrderParam;
import com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport;
import com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagLatestAssignResultBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderSubmitBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.NewNethpDiagConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName NewNethpDispatchRpc
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 11:06
 **/
@Service
@Slf4j
public class NewNethpDispatchRpcImpl implements NewNethpDispatchRpc {

    /**
     * APP_CODE
     */
    public static final String APP_CODE = "jdh-o2o-service";

    /**
     * APP_TOKEN
     */
    public static final String APP_TOKEN = "1024888";

    /**
     * CLIENT_IP
     */
    public static final String CLIENT_IP = "127.0.0.1";

    /**
     * 下单接口（发起派单）
     */
    @Resource
    private SubmitDiagExport submitDiagExport;

    /**
     * 派单接口（查询派单结果、推送派单失败）
     */
    @Resource
    private TriageAssignExport triageAssignExport;

    /**
     * 接单接口
     */
    @Resource
    private TradeDiagStatusChangeExport tradeDiagStatusChangeExport;

    /**
     * 医生派单接口（开关诊）
     */
    @Resource
    private TriageInfoExport triageInfoExport;

    /**
     * 转诊（重派）
     */
    @Resource
    private OrderTransferExport orderTransferExport;

    /**
     * 派单下单
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.submitOrder")
    public NewNethpDiagOrderSubmitBO submitOrder(NewNethpDiagOrderParam param) {
        DiagOrderParam orderParam = NewNethpDiagConverter.convertor.param2DiagOrderParam(param);
        try{
            //调用客户端信息
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            orderParam.setNhpClientInfo(clientInfo);
            log.info("NewNethpDispatchRpcImpl -> submitOrder start, orderParam:{}", JSON.toJSONString(orderParam));
            Result<DiagOrderSubmitDTO> submitDTOResult = submitDiagExport.submitOrder(orderParam);
            log.info("NewNethpDispatchRpcImpl -> submitOrder end,result:{}", JSON.toJSONString(submitDTOResult));
            if (!Objects.equals(submitDTOResult.getCode(), BusinessErrorCode.SUCCESS.getCode())){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_SUBMIT_ERROR);
            }
            return NewNethpDiagConverter.convertor.submit2NewNethpDiagOrderResult(submitDTOResult.getData());
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> submitOrder BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> submitOrder exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
    }

    /**
     * 查询派单结果的接口
     * @param diagId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.getDiagAssignResult")
    public NewNethpDiagLatestAssignResultBO getDiagAssignResult(Long diagId, Integer assignType) {
        try{
            DiagAssignResultQueryParam assignParam = new DiagAssignResultQueryParam();
            assignParam.setDiagId(diagId);
            assignParam.setAssignType(assignType);
            RxClientDTO rxClientDTO = new RxClientDTO();
            rxClientDTO.setClient(APP_CODE);
            rxClientDTO.setServerIp(CLIENT_IP);
            log.info("NewNethpDispatchRpcImpl -> getDiagAssignResult start, rxClientDTO={}, assignParam:{}", JSON.toJSONString(rxClientDTO), JSON.toJSONString(assignParam));
            Result<DiagLatestAssignResultDTO> assignResult = triageAssignExport.getDiagAssignResult(rxClientDTO, assignParam);
            log.info("NewNethpDispatchRpcImpl -> getDiagAssignResult end,result:{}", JSON.toJSONString(assignResult));
            if (!Objects.equals(assignResult.getCode(), BusinessErrorCode.SUCCESS.getCode())){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_SEARCH_ERROR);
            }
            return NewNethpDiagConverter.convertor.assign2DiagLatestAssignResult(assignResult.getData());
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> getDiagAssignResult BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> getDiagAssignResult exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_SEARCH_ERROR);
        }
    }

    /**
     * 接单
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.doctorReply")
    public Boolean doctorReply(NewNethpDiagDoctorReplyParam param) {
        DoctorReplyParam doctorReplyParam = NewNethpDiagConverter.convertor.param2DoctorReplyParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> doctorReply start, doctorReplyParam:{}", JSON.toJSONString(doctorReplyParam));
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            Result<Boolean> result = tradeDiagStatusChangeExport.doctorReply(clientInfo, doctorReplyParam);
            log.info("NewNethpDispatchRpcImpl -> doctorReply end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode())  || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> doctorReply BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> doctorReply exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 医生诊中转诊接口（重派接口）
     * 订单必须处于问诊中状态，订单没有被取消、完成
     * @param param
     * @return
     * https://joyspace.jd.com/pages/JoVybk77vcPmUyNJxhMl
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.diagingTransferDiagFromInitialRound")
    public Boolean diagingTransferDiagFromInitialRound(NewNethpOrderTransferByDoctorParam param) {
        OrderTransferParam doctorReplyParam = NewNethpDiagConverter.convertor.param2OrderTransferParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> reappointOrderRepliedFromInitialForO2O start, doctorReplyParam:{}", JSON.toJSONString(doctorReplyParam));
            Result<Boolean> result = orderTransferExport.diagingTransferDiagFromInitialRound(new ClientInfoDTO(APP_CODE, CLIENT_IP), doctorReplyParam);
            log.info("NewNethpDispatchRpcImpl -> reappointOrderRepliedFromInitialForO2O end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode()) || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> reappointOrderRepliedFromInitialForO2O BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> reappointOrderRepliedFromInitialForO2O exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 医生接单前转诊接口（重派接口）
     * 订单必须处于待接诊状态
     * 派单系统会从第一轮重新派单
     * @param param
     * @return
     * https://joyspace.jd.com/pages/JoVybk77vcPmUyNJxhMl
     */
    @Override
    @LogAndAlarm
    public Boolean reappointDiagBeforeReplyDefaultFromInitial(NewNethpOrderTransferByDoctorParam param) {
        OrderTransferParam doctorReplyParam = NewNethpDiagConverter.convertor.param2OrderTransferParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> reappointDiagBeforeReplyDefaultFromInitial start, doctorReplyParam:{}", JSON.toJSONString(doctorReplyParam));
            Result<Boolean> result = orderTransferExport.reappointDiagBeforeReplyDefaultFromInitial(new ClientInfoDTO(APP_CODE, CLIENT_IP), doctorReplyParam);
            log.info("NewNethpDispatchRpcImpl -> reappointDiagBeforeReplyDefaultFromInitial end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode()) || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> reappointDiagBeforeReplyDefaultFromInitial BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> reappointDiagBeforeReplyDefaultFromInitial exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 医生接单前转诊接口（重派、指定派单接口）
     * 订单必须处于待接诊状态，订单没有被接单或者取消、完成
     * @param param
     * @return
     * https://joyspace.jd.com/pages/JoVybk77vcPmUyNJxhMl
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.reappointDiagBeforeReplyToDoctor")
    public Boolean reappointDiagBeforeReplyToDoctor(NewNethpOrderTransferByDoctorParam param) {
        OrderTransferParam doctorReplyParam = NewNethpDiagConverter.convertor.param2OrderTransferParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> reappointOrderBeforeReplyFromInitialForO2O start, doctorReplyParam:{}", JSON.toJSONString(doctorReplyParam));
            Result<Boolean> result = orderTransferExport.reappointDiagBeforeReplyToDoctor(new ClientInfoDTO(APP_CODE, CLIENT_IP), doctorReplyParam);
            log.info("NewNethpDispatchRpcImpl -> reappointOrderBeforeReplyFromInitialForO2O end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode()) || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> reappointOrderBeforeReplyFromInitialForO2O BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> reappointOrderBeforeReplyFromInitialForO2O exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 开关诊接口
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.changeAssignSwitchStatus")
    public Boolean changeAssignSwitchStatus(NewNethpChangeAssignSwitchStatusParam param) {
        ChangeAssignSwitchStatusParam switchStatusParam = NewNethpDiagConverter.convertor.param2ChangeAssignSwitchStatusParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> changeAssignSwitchStatus start, switchStatusParam:{}", JSON.toJSONString(switchStatusParam));
            Result<Boolean> result = triageInfoExport.changeAssignSwitchStatus(switchStatusParam);
            log.info("NewNethpDispatchRpcImpl -> changeAssignSwitchStatus end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode())  || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> changeAssignSwitchStatus BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> changeAssignSwitchStatus exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 派单失败接口（停止派单）
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.suspendDiagAssign")
    public Boolean suspendDiagAssign(NewNethpDiagAssignSuspendParam param) {
        DiagAssignSuspendParam diagAssignSuspendParam = NewNethpDiagConverter.convertor.param2DiagAssignSuspendParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> suspendDiagAssign start, diagAssignSuspendParam:{}", JSON.toJSONString(diagAssignSuspendParam));
            RxClientDTO rxClientDTO = new RxClientDTO();
            rxClientDTO.setClient(APP_CODE);
            rxClientDTO.setServerIp(CLIENT_IP);
            Result<Boolean> result = triageAssignExport.suspendDiagAssign(rxClientDTO, diagAssignSuspendParam);
            log.info("NewNethpDispatchRpcImpl -> suspendDiagAssign end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode()) || Objects.equals(result.getData(), Boolean.FALSE)){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> suspendDiagAssign BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> suspendDiagAssign exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 取消派单（问诊单）
     * @param param
     * https://cf.jd.com/pages/viewpage.action?pageId=942686942
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.cancelDiag")
    public Boolean cancelDiag(NewNethpTradeCancelDiagParam param) {
        TradeCancelDiagParam cancelDiagParam = NewNethpDiagConverter.convertor.param2TradeCancelDiagParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> cancelDiag start, cancelDiagParam:{}", JSON.toJSONString(cancelDiagParam));
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            Result<Boolean> result = tradeDiagStatusChangeExport.cancelDiag(clientInfo, cancelDiagParam);
            log.info("NewNethpDispatchRpcImpl -> cancelDiag end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode())){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> cancelDiag BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> cancelDiag exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR);
        }
    }

    /**
     * 结束、完成派单（问诊单）
     * @param param
     * https://cf.jd.com/pages/viewpage.action?pageId=942686942
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NewNethpDispatchRpcImpl.diagEnd")
    public Boolean diagEnd(NewNethpTradeEndDiagParam param) {
        TradeDiagEndParam endDiagParam = NewNethpDiagConverter.convertor.param2TradeDiagEndParam(param);
        try{
            log.info("NewNethpDispatchRpcImpl -> diagEnd start, endDiagParam:{}", JSON.toJSONString(endDiagParam));
            NhpClientInfo clientInfo = new NhpClientInfo();
            clientInfo.setAppCode(APP_CODE);
            clientInfo.setAppToken(APP_TOKEN);
            clientInfo.setClientIp(CLIENT_IP);
            Result<Boolean> result = tradeDiagStatusChangeExport.diagEnd(clientInfo, endDiagParam);
            log.info("NewNethpDispatchRpcImpl -> diagEnd end,result:{}", JSON.toJSONString(result));
            if (!Objects.equals(result.getCode(), BusinessErrorCode.SUCCESS.getCode())){
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_END_ERROR);
            }
            return result.getData();
        } catch (BusinessException e){
            log.error("NewNethpDispatchRpcImpl -> diagEnd BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NewNethpDispatchRpcImpl -> diagEnd exception", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_END_ERROR);
        }
    }
}