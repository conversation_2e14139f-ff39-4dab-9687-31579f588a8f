package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.core.domain.promise.rpc.bo.AngelPromiseBO;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 服务者工单 RPC转换
 * @author: yang<PERSON><PERSON>
 * @date: 2024/5/14 5:10 下午
 * @version: 1.0
 */
@Mapper
public interface AngelPromiseRpcConvert {

    /**
     *
     */
    AngelPromiseRpcConvert INS = Mappers.getMapper(AngelPromiseRpcConvert.class);

    /**
     *
     * @param workDto
     * @return
     */
    AngelPromiseBO convert2Bo(AngelWorkDto workDto);
}
