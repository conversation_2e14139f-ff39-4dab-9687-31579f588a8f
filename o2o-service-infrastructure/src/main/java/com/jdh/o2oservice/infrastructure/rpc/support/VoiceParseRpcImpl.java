package com.jdh.o2oservice.infrastructure.rpc.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.HttpSimpleClient;
import com.jdh.o2oservice.core.domain.support.file.rpc.VoiceParseRpc;
import com.jdh.o2oservice.core.domain.support.file.rpc.dto.UploadVoiceDto;
import com.jdh.o2oservice.core.domain.support.file.rpc.dto.VoiceContentBO;
import com.jdh.o2oservice.core.domain.support.file.rpc.dto.VoiceParseBO;
import com.jdh.o2oservice.core.domain.support.file.rpc.dto.VoiceResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 22:24
 */
@Component
@Slf4j
public class VoiceParseRpcImpl implements VoiceParseRpc {

    @Override
    public UploadVoiceDto uploadVoice(String voicePath, String type, int samplerate) {

        String url = "http://extpre-yanxi-api-internal.jd.com/api/voice_algorithm_platform/fasr_create";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("charset", "UTF-8");
        headers.put("Chunked", "false");

        // 业务参数
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("domain", "general");
        params.put("protocol", 2);
        params.put("format", type);
        params.put("samplerate", samplerate);
        params.put("role", true);
        params.put("speech_url", voicePath);

        // 计算签名
        String sign = calcSign(params);

        Map<String, Object> body = Maps.newHashMap();
        body.put("caller", "jingdongjiankangsf");
        body.put("tenantId", "23353");
        body.put("sign", sign);
        body.put("params", params);


        // {"code":"000000","data":{"request_id":"855419be-c335-11ef-99ec-fa163e034e6d","taskid":"c9fa5b54-c937-50e7-af9a-f260680723df"},"message":"OK","state":1}
        String result = HttpSimpleClient.postJson(url, headers, JSON.toJSONString(body));

        VoiceResult<UploadVoiceDto> dto = JSON.parseObject(result, new TypeReference<VoiceResult<UploadVoiceDto>>(){});
        return  dto.getData();
    }




    /**
     * 查询解析的结果
     */
    public VoiceContentBO findText(String taskId) {
        String url = "http://extpre-yanxi-api-internal.jd.com/api/voice_algorithm_platform/fasr_query";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("charset", "UTF-8");
        headers.put("Chunked", "false");

        // 业务参数
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("taskid", taskId);
        params.put("protocol", 2);

        // 计算签名
        String sign = calcSign(params);

        Map<String, Object> body = Maps.newHashMap();
        body.put("caller", "jingdongjiankangsf");
        body.put("tenantId", "23353");
        body.put("sign", sign);
        body.put("params", params);

        String result= HttpSimpleClient.postJson(url, headers, JSON.toJSONString(body));
        log.info("VoiceParseRpcImpl->findText result={}", result);

        VoiceResult<VoiceParseBO> result1 = JSON.parseObject(result, new TypeReference<VoiceResult<VoiceParseBO>>(){});
        if (StringUtils.equals(result1.getCode(), "000000")){
            VoiceParseBO dto = result1.getData();

            return dto.getContent();
        }

        throw new SystemException(SystemErrorCode.UNKNOWN_ERROR, result1.getMessage());
    }

    private static String calcSign(TreeMap<String, Object> map) {


        StringBuilder builder = new StringBuilder();
        map.forEach((k, v) -> {
            builder.append(k).append("=").append(v).append("&");
        });

        String params = builder.substring(0, builder.length() - 1);

        // 创建一个密钥规范
        SecretKeySpec signingKey = new SecretKeySpec("450063856081a412".getBytes(), "HmacMD5");

        try {
            // 指定算法，获取mac
            Mac mac = Mac.getInstance("HmacMD5");

            // 初始化 Mac 实例
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(params.getBytes());

            // 转16进制
            String hexString = Hex.encodeHexString(rawHmac);
            return hexString;

        } catch (Exception e) {
            log.error("calcSign", e);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }

    }
}
