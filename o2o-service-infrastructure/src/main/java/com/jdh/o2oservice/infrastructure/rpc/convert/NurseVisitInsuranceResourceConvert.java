package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdd.baoxian.b.insurance.core.trade.export.dto.NurseVisitPolicyDTO;
import com.jdd.baoxian.b.insurance.core.trade.export.req.NurseVisitIssuePolicyReq;
import com.jdd.baoxian.b.insurance.core.trade.export.req.NurseVisitQueryReq;
import com.jdd.baoxian.b.insurance.core.trade.export.res.NurseVisitPolicyRes;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NurseVisitPolicyBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NurseVisitIssuePolicyParam;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NurseVisitQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName NurseVisitInsuranceResourceConvert
 * @Description
 * <AUTHOR>
 * @Date 2024/8/5 1:46 PM
 * @Version 1.0
 **/
@Mapper
public interface NurseVisitInsuranceResourceConvert {

    NurseVisitInsuranceResourceConvert convertor = Mappers.getMapper(NurseVisitInsuranceResourceConvert.class);

    NurseVisitIssuePolicyReq toNurseVisitIssuePolicyReq(NurseVisitIssuePolicyParam nurseVisitIssuePolicyParam);

    NurseVisitPolicyBo toNurseVisitPolicyBo(NurseVisitPolicyDTO nurseVisitPolicyDTO);

    NurseVisitQueryReq toNurseVisitQueryReq(NurseVisitQueryParam nurseVisitQueryParam);
}
