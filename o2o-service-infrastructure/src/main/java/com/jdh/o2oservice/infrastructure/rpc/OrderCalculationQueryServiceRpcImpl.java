package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.ofc.ocs.model.Header;
import com.jd.ofc.ocs.webservice.jsf.OrderCalculationQueryServiceJsf;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName:OrderCalculationQueryServiceRpcImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/1/9 01:46
 * @Vserion: 1.0
 **/
@Slf4j
@Component
public class OrderCalculationQueryServiceRpcImpl implements OrderCalculationQueryServiceRpc {

    /**
     * SUCCESS
     */
    private static final String SUCCESS = "SUCCESS";

    /**
     * orderCalculationQueryServiceJsf
     */
    @Resource
    private OrderCalculationQueryServiceJsf orderCalculationQueryServiceJsf;

    private static final String ORDER_CALCULATION_SYSTEMKEY = "b86b4ebf22625ae51c879a0f84f5273c";
    private static final String APP_NAME = "physicalexamination";

    /**
     * 获取订单金额明细接口
     *
     * @param orderId
     */
    @Override
    public String queryOrderSplitAmountAndExpand(Long orderId) {
        log.info("OrderCalculationQueryServiceRpcImpl -> queryOrderSplitAmountAndExpand start, orderId={}", orderId);
        try {
            String result = orderCalculationQueryServiceJsf.queryOrderSplitAmountAndExpand(orderId, getHeader());
            log.info("OrderCalculationQueryServiceRpcImpl -> queryOrderSplitAmountAndExpand end, orderId={}, result={}", orderId, result);
            if (StringUtils.isBlank(result)){
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
            JSONObject object = JSON.parseObject(result);
            if (!SUCCESS.equals(getResultCode(object))){
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION, getResultReason(object));
            }
            return getResultData(object);
        }catch (Throwable e){
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.impl.OrderCalculationQueryServiceRpcImpl.queryOrderSplitAmountAndExpand", e.getMessage());
            log.error("OrderCalculationQueryServiceRpcImpl -> queryOrderSplitAmountAndExpand exception, orderId={},errMessage={}", orderId, e.getMessage(),e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     *
     * @param object
     * @return
     */
    private String getResultData(JSONObject object) {
        return object.getString("data");
    }

    /**
     *
     * @param object
     * @return
     */
    private String getResultReason(JSONObject object) {
        return object.getString("reason");
    }

    /**
     *
     * @param object
     * @return
     */
    private String getResultCode(JSONObject object) {
        return object.getString("code");
    }

    /**
     *
     * @return
     */
    private Header getHeader() {
        Header header = new Header();
        header.setSystemKey(ORDER_CALCULATION_SYSTEMKEY);
        header.setSystemName(APP_NAME);
        return header;
    }
}
