package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jdh.laputa.base.client.jsf.JssUploadJsfService;
import com.jdh.laputa.base.client.res.JsfResult;
import com.jdh.o2oservice.core.domain.support.feeConfig.rpc.JssUploadRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class JssUploadRpcServiceImpl implements JssUploadRpcService {


    @Autowired
    private JssUploadJsfService jssUploadJsfService;

    @Override
    public String jssUploadFile(byte[] bytes, String fileName) {
        try {
             log.info("JssUploadRpcServiceImpl#jssUploadFile.bytes={},fileName={}", JSON.toJSONString(bytes),JSON.toJSONString(fileName));
            JsfResult<String> result = jssUploadJsfService.jssUploadFile(bytes, fileName);
            log.info("JssUploadRpcServiceImpl#jssUploadFile.result={}", JSON.toJSONString(result));
            if (result == null || result.getCode() != 0) {
                  log.error("JssUploadRpcServiceImpl#jssUploadFile#上传失败fileName : {}", fileName);
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("JssUploadRpcServiceImpl#jssUploadFile#error#fileName={}", fileName, e);
        }
        return null;
    }
}
