package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.pop.vender.center.service.shop.dto.BasicShop;
import com.jdh.o2oservice.core.domain.support.store.request.CreateStoreCmd;
import com.jdh.o2oservice.core.domain.trade.vo.VenderInfoValueObject;
import com.jdh.o2oservice.export.angel.cmd.CreateShopCmd;
import com.jdh.o2oservice.export.angel.cmd.GetShopInfoCmd;
import com.jdh.o2oservice.export.angel.cmd.UpdateShopCmd;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * ThirdStoreInfoConverter 三方门店信息转换器
 *
 * <AUTHOR>
 * @version 2024/3/4 14:16
 **/
@Mapper(componentModel = "spring")
public interface ThirdStoreInfoConverter {

    /**
     * ins
     */
    ThirdStoreInfoConverter ins = Mappers.getMapper(ThirdStoreInfoConverter.class);

    /**
     *
     * @param createStoreCmd
     * @return
     */
    CreateShopCmd convertCreateShopCmdInfo(CreateStoreCmd createStoreCmd);
    /**
     *
     * @param createStoreCmd
     * @return
     */
    @Mapping(target = "shopId", source = "outShopId")
    UpdateShopCmd convertUpdateShopCmdInfo(CreateStoreCmd createStoreCmd);
    /**
     *
     * @param createStoreCmd
     * @return
     */
    @Mapping(target = "shopId", source = "outShopId")
    GetShopInfoCmd convertGetShopInfoInfo(CreateStoreCmd createStoreCmd);
}
