package com.jdh.o2oservice.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.jd.health.xfyl.open.export.dto.QuickPushInfoResultDTO;
import com.jd.health.xfyl.open.export.dto.TransCodeDto;
import com.jd.health.xfyl.open.export.param.check.*;
import com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.QuickCheckRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description 快检服务
 * @Date 2025/2/7 下午08:33
 * <AUTHOR>
 **/
@Component
@Slf4j
public class QuickCheckThirdExportServiceRpcImpl implements QuickCheckThirdExportServiceRpc {

    @Resource
    private QuickCheckThirdExportService quickCheckThirdExportService;

    /**
     * 快检推送信息(新逻辑，不包含绑码节点)
     * @param quickCheckPushInfoBO
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.quickCheckPushInfo")
    public QuickPushInfoResultBO quickCheckPushInfo(QuickCheckPushInfoBO quickCheckPushInfoBO) {
        try {
            QuickCheckPushInfoParam param = new QuickCheckPushInfoParam();
            BeanUtils.copyProperties(quickCheckPushInfoBO, param);
            if (Objects.isNull(param.getCoreStatus())){
                param.setCoreStatus(quickCheckPushInfoBO.getStatus());
            }
            JsfResult<QuickPushInfoResultDTO> result = quickCheckThirdExportService.quickCheckPushInfo(param);
            log.info("QuickCheckThirdExportServiceRpcImpl quickCheckPushInfo param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return JSON.parseObject(JSON.toJSONString(result.getData()), QuickPushInfoResultBO.class);
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckPushInfo biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckPushInfo error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 快检推送信息(仅绑码节点调用，兼容老版本)
     * @param quickCheckPushInfoBO
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.quickCheckAppoint")
    public QuickPushInfoResultBO quickCheckAppoint(QuickCheckPushInfoBO quickCheckPushInfoBO) {
        try {
            QuickCheckPushInfoParam param = new QuickCheckPushInfoParam();
            BeanUtils.copyProperties(quickCheckPushInfoBO, param);
            if (Objects.isNull(param.getCoreStatus())){
                param.setCoreStatus(quickCheckPushInfoBO.getStatus());
            }
            JsfResult<QuickPushInfoResultDTO> result = quickCheckThirdExportService.quickCheckAppoint(param);
            log.info("QuickCheckThirdExportServiceRpcImpl quickCheckAppoint param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return JSON.parseObject(JSON.toJSONString(result.getData()), QuickPushInfoResultBO.class);
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckAppoint biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckAppoint error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 同步实验检测单作废
     *
     * @param quickCheckInvalidBO
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean invalidQuickCheckAppoint(QuickCheckInvalidBO quickCheckInvalidBO) {
        try {
            QuickCheckInvalidParam param = new QuickCheckInvalidParam();
            BeanUtils.copyProperties(quickCheckInvalidBO, param);
            JsfResult<Boolean> result = quickCheckThirdExportService.invalidQuickCheckAppoint(param);
            log.info("QuickCheckThirdExportServiceRpcImpl quickCheckAppoint param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return result.getData();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> invalidQuickCheckAppoint error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> invalidQuickCheckAppoint error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 快检门店账号
     * @param stationId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.quickCheckStoreUser")
    public List<String> quickCheckStoreUser(String stationId) {
        try {
            QuickCheckStoreUserParam param = new QuickCheckStoreUserParam();
            param.setStationId(stationId);
            JsfResult<List<String>> result = quickCheckThirdExportService.quickCheckStoreUser(param);
            log.info("QuickCheckThirdExportServiceRpcImpl quickCheckStoreUser param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return result.getData();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckStoreUser biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckStoreUser error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 检测单修改同步
     *
     * @param quickCheckModifyBO
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.quickCheckModify")
    public Boolean quickCheckModify(QuickCheckModifyBO quickCheckModifyBO) {
        try {
            JsfResult<Boolean> result = quickCheckThirdExportService.quickCheckModify(QuickCheckRpcConverter.INSTANCE.convert(quickCheckModifyBO));
            log.info("QuickCheckThirdExportServiceRpcImpl quickCheckModify param={}, result={}", JSON.toJSONString(quickCheckModifyBO), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return result.getData();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckModify biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> quickCheckModify error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 运力变更通知
     *
     * @param quickCheckPushSampleDeliverBO
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.pushSampleDeliver")
    public Boolean pushSampleDeliver(QuickCheckPushSampleDeliverBO quickCheckPushSampleDeliverBO) {
        try {
            JsfResult<Boolean> result = quickCheckThirdExportService.pushSampleDeliver(QuickCheckRpcConverter.INSTANCE.convert(quickCheckPushSampleDeliverBO));
            log.info("QuickCheckThirdExportServiceRpcImpl pushSampleDeliver param={}, result={}", JSON.toJSONString(quickCheckPushSampleDeliverBO), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return result.getData();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> pushSampleDeliver biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> pushSampleDeliver error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 报告已重置通知
     *
     * @param quickCheckReportRetractBO
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.QuickCheckThirdExportServiceRpcImpl.reportRetract")
    public Boolean reportRetract(QuickCheckReportRetractBO quickCheckReportRetractBO) {
        try {
            JsfResult<Boolean> result = quickCheckThirdExportService.reportRetract(QuickCheckRpcConverter.INSTANCE.convert(quickCheckReportRetractBO));
            log.info("QuickCheckThirdExportServiceRpcImpl reportRetract param={}, result={}", JSON.toJSONString(quickCheckReportRetractBO), JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return result.getData();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> reportRetract biz error e", e);
            throw e;
        } catch (Throwable e) {
            log.error("QuickCheckThirdExportServiceRpcImpl -> reportRetract error e", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 修改检查结果的CT值。
     *
     * @param quickCheckResultModifyBO 包含要修改的CT值的业务对象。
     * @return 操作是否成功。
     */
    @Override
    @LogAndAlarm
    public Boolean modifyCtValue(QuickCheckResultModifyBO quickCheckResultModifyBO) {

        try {
            QuickCheckResultModifyParam modifyParam = QuickCheckRpcConverter.INSTANCE.convert(quickCheckResultModifyBO);
            JsfResult<Boolean> result = quickCheckThirdExportService.modifyCheckResult(modifyParam);
            if (result != null && result.isSuccess()) {
                return result.getData();
            }
        }catch (Exception e){
            log.error("QuickCheckThirdExportServiceRpcImpl -> modifyCtValue biz error", e);
        }

        return Boolean.FALSE;
    }

    /**
     * 根据查询条件获取流程编码信息。
     *
     * @param flowCodeQueryBO 查询条件对象，包含流程编码相关的查询条件。
     * @return FlowCodeBO 对象，包含流程编码的详细信息。
     */
    @Override
    @LogAndAlarm
    public FlowCodeBO queryFlowCode(FlowCodeQueryBO flowCodeQueryBO) {

        try {
            QuickCheckTransCodeQueryParam param = QuickCheckRpcConverter.INSTANCE.convert(flowCodeQueryBO);
            JsfResult<TransCodeDto> result = quickCheckThirdExportService.queryTransCode(param);
            if (result != null && result.isSuccess()) {
                return QuickCheckRpcConverter.INSTANCE.convert(result.getData());
            }

        }catch (Exception e){
            log.error("QuickCheckThirdExportServiceRpcImpl -> queryFlowCode biz error", e);
        }

        return null;
    }

    /**
     * 根据查询条件获取可用流码。
     *
     * @param flowCodeQueryBO 查询条件对象，包含流码的各种查询条件。
     * @return 可用的流码信息。
     */
    @Override
    @LogAndAlarm
    public FlowCodeBO freeFlowCode(FreeFlowCodeBO flowCodeQueryBO) {

        try {
            QuickCheckTransCodeFreeParam param = QuickCheckRpcConverter.INSTANCE.convertParam(flowCodeQueryBO);
            JsfResult<TransCodeDto> result = quickCheckThirdExportService.freeTransCode(param);
            if (result != null && result.isSuccess()) {
                return QuickCheckRpcConverter.INSTANCE.convert(result.getData());
            }

        }catch (Exception e){
            log.error("QuickCheckThirdExportServiceRpcImpl -> freeFlowCode biz error", e);
        }
        return null;

    }

    /**
     * 检测结果审核通知
     *
     * @param quickCheckResultReviewBO 快速检查结果审查业务对象，包含审查所需的信息。
     * @return true 如果结果审查通过，false 否则。
     */
    @Override
    @LogAndAlarm
    public Boolean checkResultReview(QuickCheckResultReviewBO quickCheckResultReviewBO) {
        try {
            QuickCheckResultReviewParam param = QuickCheckRpcConverter.INSTANCE.convert(quickCheckResultReviewBO);
            JsfResult<Boolean> result = quickCheckThirdExportService.checkResultReview(param);
            if (result != null && result.isSuccess()) {
                return result.getData();
            }

        }catch (Exception e){
            log.error("QuickCheckThirdExportServiceRpcImpl -> checkResultReview  error", e);
        }
        return null;
    }
}
