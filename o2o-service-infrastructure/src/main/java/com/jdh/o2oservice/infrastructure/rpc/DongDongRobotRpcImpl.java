package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.O2oHttpClient;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName DongDongRobotRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/11/21 19:04
 * https://me.jd.com/openplatform/console/appConfig?appId=M4Iq13t6Ch3gJqcOgrhpF&page=member
 **/
@Service
@Slf4j
public class DongDongRobotRpcImpl implements DongDongRobotRpc {

    /**
     * 发送咚咚机器人消息
     * @param message
     */
    @Override
    @LogAndAlarm(jKey = "com.jd.health.xfyl.merchant.rpc.impl.DongDongRobotRpcImpl.sendDongDongRobotMessage")
    public void sendDongDongRobotMessage(String message, String groupId, JSONArray atUsers) {
        String requestId = SpringUtil.getBean(GenerateIdFactory.class).getId() + UUID.randomUUID().toString();
        HashMap<String, String> headerMap = getHeaderMap(requestId);
        if (headerMap == null) {
            return;
        }
        //组装请求体
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("appId","M4Iq13t6Ch3gJqcOgrhpF");
        paramMap.put("tenantId","CN.JD.GROUP");
        //paramMap.put("erp","lvyifan3");
        //群聊id写死
        paramMap.put("groupId",groupId);
        //组装发送内容
        JSONObject params = new JSONObject();
        params.put("robotId","00_9a8d4906ed194292");
        JSONObject bodyParam = new JSONObject();
        bodyParam.put("atUsers", atUsers);
        bodyParam.put("type","text");
        String interval = new String(new byte[]{(byte) 0xe2, (byte) 0x80, (byte) 0x85}, StandardCharsets.UTF_8);
        List<String> nicknameList = atUsers.stream().map(o -> {
            JSONObject jsonObject = Convert.convert(JSONObject.class, o);
            return "@"+jsonObject.getString("nickname");
        }).collect(Collectors.toList());
        String join = Joiner.on(interval).join(nicknameList);
        bodyParam.put("content",join + interval + message);
        params.put("body",bodyParam);
        paramMap.put("params",params);

        paramMap.put("requestId", requestId);
        paramMap.put("dateTime",System.currentTimeMillis());
        log.info("DongDongRobotRpcImpl -> sendDongDongRobotMessage, headerMap={}, paramMap={}", JsonUtil.toJSONString(headerMap), JsonUtil.toJSONString(paramMap));
        //发送请求
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/suite/v1/timline/sendRobotMsg", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotRpcImpl -> sendDongDongRobotMessage, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (!Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            log.error("DongDongRobotRpcImpl -> sendDongDongRobotMessage, error");
        }
    }

    /**
     * 发送咚咚机器人消息（单聊）
     * @param message
     */
    @Override
    public void sendDongDongRobotMessage(String message, String erp) {
        String requestId = SpringUtil.getBean(GenerateIdFactory.class).getId() + UUID.randomUUID().toString();
        HashMap<String, String> headerMap = getHeaderMap(requestId);
        if (headerMap == null) {
            return;
        }
        //组装请求体
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("appId","M4Iq13t6Ch3gJqcOgrhpF");
        paramMap.put("tenantId","CN.JD.GROUP");
        //paramMap.put("erp","lvyifan3");
        //群聊id写死
        paramMap.put("erp",erp);
        //组装发送内容
        JSONObject params = new JSONObject();
        params.put("robotId","00_9a8d4906ed194292");
        JSONObject bodyParam = new JSONObject();
        bodyParam.put("type","text");
        bodyParam.put("content", message);
        params.put("body",bodyParam);
        paramMap.put("params",params);

        paramMap.put("requestId", requestId);
        paramMap.put("dateTime",System.currentTimeMillis());
        log.info("DongDongRobotRpcImpl -> sendDongDongRobotMessage, headerMap={}, paramMap={}", JsonUtil.toJSONString(headerMap), JsonUtil.toJSONString(paramMap));
        //发送请求
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/suite/v1/timline/sendRobotMsg", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotRpcImpl -> sendDongDongRobotMessage, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (!Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            log.error("DongDongRobotRpcImpl -> sendDongDongRobotMessage, error");
        }
    }

    /**
     *
     * @return
     */
    private HashMap<String, String> getHeaderMap(String requestId) {
        JSONObject appAccessToken = getAppAccessToken();
        if (Objects.isNull(appAccessToken)) {
            return null;
        }
        JSONObject teamAccessToken = getTeamAccessToken(appAccessToken.getString("appAccessToken"));
        if (Objects.isNull(teamAccessToken)) {
            return null;
        }
        //组装header
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("authorization",String.format("Bearer %s", teamAccessToken.getString("teamAccessToken")));
        headerMap.put("x-stage", "PROD");
        headerMap.put("x-requested-id", requestId);
        return headerMap;
    }

    /**
     * 获取getAppAccessToken
     * appAccessToken
     * @return
     */
    public JSONObject getAppAccessToken(){
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("x-stage","PROD");

        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appKey","M4Iq13t6Ch3gJqcOgrhpF");
        paramMap.put("appSecret","3uWBYH6LYF9XQWpV3cfz");
        log.info("DongDongRobotRpcImpl -> getAppAccessToken, headerMap={}, paramMap={}", headerMap, paramMap);
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/auth/v1/app_access_token", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotRpcImpl -> getAppAccessToken, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            return result.getJSONObject("data");
        }
        return null;
    }

    /**
     * 获取getTeamAccessToken
     * teamAccessToken
     * @return
     */
    public JSONObject getTeamAccessToken(String appAccessToken){
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("x-stage","PROD");

        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appAccessToken",appAccessToken);
        paramMap.put("openTeamId","a912c315c52a28249bede462f3024ccd");
        log.info("DongDongRobotRpcImpl -> getTeamAccessToken, headerMap={}, paramMap={}", headerMap, paramMap);
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/auth/v1/team_access_token", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotRpcImpl -> getTeamAccessToken, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            return result.getJSONObject("data");
        }
        return null;
    }
}