package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.dto.B2bEnterpriseBillDetailDto;
import com.jdh.o2oservice.b2b.export.enterprisebill.query.B2bEnterpriseBillRequest;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.export.support.command.B2bOperationLogCmd;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseBillDetailDto;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseInfoDto;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseBillRequest;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhEnterpriseBillRpcConverter {

    JdhEnterpriseBillRpcConverter instance = Mappers.getMapper(JdhEnterpriseBillRpcConverter.class);

    /**
     *
     * @param enterpriseBillRequest
     * @return
     */
    B2bEnterpriseBillRequest convertToB2bEnterpriseBillRequest(JdhEnterpriseBillRequest enterpriseBillRequest);
    /**
     *
     * @param list
     * @return
     */
    List<JdhEnterpriseBillDetailDto> convertToEnterpriseBillDetailDtoList(List<B2bEnterpriseBillDetailDto> list);

    /**
     *
     * @param jdhEnterpriseRequest
     * @return
     */
    B2bEnterpriseRequest convertToB2bEnterpriseRequest(JdhEnterpriseRequest jdhEnterpriseRequest);

    /**
     *
     * @param b2bOperationLogCmd
     * @return
     */
    OperationLogCmd convertToOperationLogCmd(B2bOperationLogCmd b2bOperationLogCmd);

    /**
     * 
     * @param b2bEnterpriseAcountContractDto
     * @return
     */
    JdhEnterpriseAccountContractDto convertToEnterpriseAccountContractDto(B2bEnterpriseAcountContractDto b2bEnterpriseAcountContractDto);

    /**
     *
     * @param b2bEnterpriseDto
     * @return
     */
    JdhEnterpriseInfoDto convertToEnterpriseInfoDto(B2bEnterpriseDto b2bEnterpriseDto);
}
