package com.jdh.o2oservice.infrastructure.rpc.autobots;

import com.jd.llm.client.domain.autobots.AutoBotsResult;
import com.jd.llm.client.domain.autobots.AutoBotsWfRequest;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsCallBackContext;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsWfRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.jd.llm.client.domain.autobots.AutoBotsRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */
@Mapper
public interface AutoBotsConvert {



    /**
     * 获取AutoBotsConvert的实例对象。
     */
    AutoBotsConvert INSTANCE = Mappers.getMapper(AutoBotsConvert.class);


    /**
     * 将AutoBotsRequest对象转换为AutoBotsRequestBO对象
     * @param autoBotsRequest AutoBotsRequest对象，需要被转换的请求对象
     * @return 转换后的AutoBotsRequestBO对象
     */
    AutoBotsRequest convert(AutoBotsRequestBO autoBotsRequest);

    /**
     * 将AutoBotsResult对象转换为AutoBotsResultBO对象。
     * @param autoBotsResult 需要转换的AutoBotsResult对象。
     * @return 转换后的AutoBotsResultBO对象。
     */
    AutoBotsResultBO convert(AutoBotsResult autoBotsResult);

    /**
     * 将AutoBotsWfRequestBO对象转换为AutoBotsWfRequest对象。
     * @param autoBotsWfRequestBO 要转换的AutoBotsWfRequestBO对象。
     * @return 转换后的AutoBotsWfRequest对象。
     */
    AutoBotsWfRequest convert(AutoBotsWfRequestBO autoBotsWfRequestBO);

    /**
     * convert
     * @param autoBotsResultBO
     * @param record
     * @return
     */
    default AutoBotsCallBackContext convert(AutoBotsResultBO autoBotsResultBO, AutoBotsRecord record){
        AutoBotsCallBackContext callBackContext = new AutoBotsCallBackContext();

        callBackContext.setAutoBotsRecord(record);
        callBackContext.setStatus(autoBotsResultBO.getStatus());
        callBackContext.setResponse(autoBotsResultBO.getResponse());
        callBackContext.setFinished(autoBotsResultBO.getFinished());
        callBackContext.setTraceId(autoBotsResultBO.getTraceId());
        callBackContext.setRelDoc(autoBotsResultBO.getRelDoc());
        callBackContext.setResponseAll(autoBotsResultBO.getResponseAll());
        callBackContext.setResponseType(autoBotsResultBO.getResponseType());
        callBackContext.setScene(record.getScene());

        return callBackContext;
    };



}
