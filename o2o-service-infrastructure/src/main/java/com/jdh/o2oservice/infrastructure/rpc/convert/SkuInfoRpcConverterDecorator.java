package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public abstract class SkuInfoRpcConverterDecorator implements SkuInfoRpcConverter{


    /**
     * 转换为sku信息
     *
     * @param skuInfo SKU
     * @return {@link RpcSkuBO}
     */
    @Override
    public RpcSkuBO convert2SkuInfoBo(Map<String, String> skuInfo) {
        RpcSkuBO skuInfoBo = new RpcSkuBO();
        skuInfoBo.setSkuName(skuInfo.get("sku_name"));
        if (StringUtils.isNotBlank(skuInfo.get("sku_status"))){
            skuInfoBo.setSkuStatus(Integer.parseInt(skuInfo.get("sku_status")));
        }
        skuInfoBo.setDdsplx(skuInfo.get("ddsplx"));
        skuInfoBo.setIsddlm(skuInfo.get("isddlm"));
        skuInfoBo.setIsLoc(skuInfo.get("isLOC"));
        skuInfoBo.setLocGroupId(skuInfo.get("locGroupId"));
        skuInfoBo.setImgDfsUrl(skuInfo.get("img_dfs_url"));
        skuInfoBo.setShopName(skuInfo.get("shop_name"));
        skuInfoBo.setYymb(skuInfo.get("yymb"));
        skuInfoBo.setOuterId(skuInfo.get("outer_id"));
        if (StringUtils.isNotBlank(skuInfo.get("vender_id"))){
            skuInfoBo.setVenderId(Long.valueOf(skuInfo.get("vender_id")));
        }
        skuInfoBo.setXfyl(skuInfo.get("xfyl"));
        skuInfoBo.setSpuId(skuInfo.get("spu_id"));
        skuInfoBo.setVenderName(skuInfo.get("vender_name"));
        skuInfoBo.setProductId(skuInfo.get("product_id"));
        skuInfoBo.setIsTest(skuInfo.get("isTest"));
        skuInfoBo.setIsCanUseJQ(skuInfo.get("isCanUseJQ"));
        skuInfoBo.setIsCanUseDQ(skuInfo.get("isCanUseDQ"));
        skuInfoBo.setIsGlobalPurchase(skuInfo.get("isOverseaPurchase"));
        skuInfoBo.setMsbybt(skuInfo.get("msbybt"));
        if (StringUtils.isNotBlank(skuInfo.get("category_id1"))){
            skuInfoBo.setFirstCategoryId(Integer.valueOf(skuInfo.get("category_id1")));
        }
        if (StringUtils.isNotBlank(skuInfo.get("category_id2"))){
            skuInfoBo.setSecondCategoryId(Integer.valueOf(skuInfo.get("category_id2")));
        }
        if (StringUtils.isNotBlank(skuInfo.get("category_id"))){
            skuInfoBo.setThirdCategoryId(Integer.valueOf(skuInfo.get("category_id")));
        }
        skuInfoBo.setVenderBizId(skuInfo.get("vender_bizid"));
        skuInfoBo.setShortName(skuInfo.get("shortName"));
        skuInfoBo.setShortTitle(skuInfo.get("shortTitle"));
        skuInfoBo.setJdhdjfw(skuInfo.get("jdhdjfw"));
        skuInfoBo.setSaleAttributes(skuInfo.get("sale_attributes"));
        return skuInfoBo;
    }
}
