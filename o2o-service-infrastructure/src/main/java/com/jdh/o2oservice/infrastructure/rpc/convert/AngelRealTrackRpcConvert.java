package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.StationDistanceBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.UserDistanceBo;
import com.jdh.o2oservice.export.support.dto.AngelLocationRealDto;
import com.jdh.o2oservice.export.support.dto.StationDistanceDto;
import com.jdh.o2oservice.export.support.dto.UserDistanceDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName AngelRealTrackRpcConvert
 * @Description
 * <AUTHOR>
 * @Date 2025/8/25 20:52
 */
@Mapper
public interface AngelRealTrackRpcConvert {

    AngelRealTrackRpcConvert INS = Mappers.getMapper(AngelRealTrackRpcConvert.class);

    /**
     * convertToAngelLocationBo
     *
     * @param userDistanceDto
     * @return
     */
    UserDistanceBo convertToAngelLocationBo(UserDistanceDto userDistanceDto);

    /**
     * convertToStationDistanceBo
     *
     * @param userDistanceDto
     * @return
     */
    StationDistanceBo convertToStationDistanceBo(StationDistanceDto userDistanceDto);

    /**
     * convertToAngelLocationBos
     *
     * @param userDistanceDto
     * @return
     */
    List<StationDistanceBo> convertToAngelLocationBos(List<StationDistanceDto> userDistanceDto);

    /**
     * convertToAngelLocationBo
     *
     * @param angelLocationAndPoint
     * @return
     */
    @Mapping(target = "userDistanceBo", source = "userDistanceDto")
    @Mapping(target = "stationDistanceBoList", source = "stationDistanceDtoList")
    AngelLocationBo convertToAngelLocationBo(AngelLocationRealDto angelLocationAndPoint);

}
