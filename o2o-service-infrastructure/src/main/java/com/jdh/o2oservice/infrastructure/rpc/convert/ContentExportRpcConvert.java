package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.cmp.soa.ugc.dto.ContentDTO;
import com.jd.cmp.soa.ugc.param.ContentParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.JdhContentContext;
import com.jdh.o2oservice.core.domain.product.bo.JdhContentBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/21 3:23 下午
 * @Description:
 */
@Mapper
public interface ContentExportRpcConvert {
    ContentExportRpcConvert ins = Mappers.getMapper(ContentExportRpcConvert.class);

    default ContentParam context2ContentParam(JdhContentContext context) {
        ContentParam param = new ContentParam();
        param.setContentId(context.getContentId());
        param.setTenantId(287L);
        return param;
    }

    JdhContentBO dto2JdhContentDTO(ContentDTO contentDTO);
}
