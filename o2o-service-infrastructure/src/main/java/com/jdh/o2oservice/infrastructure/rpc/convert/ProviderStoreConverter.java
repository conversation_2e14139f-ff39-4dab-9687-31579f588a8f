package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.xfyl.merchant.export.dto.XfylAppointDateDTO;
import com.jd.health.xfyl.merchant.export.param.supplier.appointment.ListScheduleParam;
import com.jdh.o2oservice.core.domain.provider.bo.ListStoreScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.XfylAppointDateBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName ProviderStoreScheduleConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/1/2 17:08
 **/
@Mapper
public interface ProviderStoreConverter {

    ProviderStoreConverter convertor = Mappers.getMapper(ProviderStoreConverter.class);

    @Mapping(source = "dateTimeDTOList", target = "dateTimeList")
    @Mapping(source = "scheduleType", target = "dateType")
    XfylAppointDateBO appointDate2Bo(XfylAppointDateDTO dto);

    List<XfylAppointDateBO> appointDate2BoList(List<XfylAppointDateDTO> list);

    @Mapping(source = "outServiceId", target = "goodsId")
    ListScheduleParam storeScheduleBO2ListScheduleParam(ListStoreScheduleBO bo);
}