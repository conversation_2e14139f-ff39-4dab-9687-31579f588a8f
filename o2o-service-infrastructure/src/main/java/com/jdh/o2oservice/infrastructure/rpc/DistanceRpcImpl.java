package com.jdh.o2oservice.infrastructure.rpc;

import com.jdh.o2oservice.core.domain.support.basic.rpc.DistanceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DistanceCalcBO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 距离计算RPC服务实现
 * @author: yang<PERSON><PERSON>
 * @date: 2024/5/8 11:22 上午
 * @version: 1.0
 */
@Component
public class DistanceRpcImpl implements DistanceRpc {
    @Override
    public List<Double[]> distanceMatrix(DistanceCalcBO calcBO) {
        return null;
    }
}
