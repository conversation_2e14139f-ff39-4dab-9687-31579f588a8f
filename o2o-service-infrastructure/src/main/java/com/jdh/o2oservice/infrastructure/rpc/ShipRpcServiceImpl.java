package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.support.ship.ShipRpcService;
import com.jdh.o2oservice.core.domain.support.ship.dto.CancelShipResponse;
import com.jdh.o2oservice.core.domain.support.ship.dto.CreateShipResponse;
import com.jdh.o2oservice.core.domain.support.ship.param.CancelDadaShipBo;
import com.jdh.o2oservice.core.domain.support.ship.param.CreateDadaShipBo;
import com.jdh.o2oservice.infrastructure.rpc.bo.DaDaResponseBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName:ShipRpcServiceImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 15:24
 * @Vserion: 1.0
 **/
@Service
@Slf4j
public class ShipRpcServiceImpl implements ShipRpcService {

    /**
     * 基础地址
     */
    @Value("${dada.base.url}")
    private String baseUrl;

    /**
     * 商户id
     */
    @Value("${dada.sourceId}")
    private String sourceId;

    /**
     * app_key
     */
    @Value("${dada.appKey}")
    private String dadaAppKey;

    /**
     * app_secret
     */
    @Value("${dada.appSecret}")
    private String dadaAppSecret;

    /**
     * 创建达达运单
     *
     * @param createDadaShipBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ShipRpcServiceImpl.createDadaShipOrder")
    public CreateShipResponse createDadaShipOrder(CreateDadaShipBo createDadaShipBo) {
        String methodUrl = "/api/order/addOrder";
        CreateShipResponse response = processInvoke(methodUrl, createDadaShipBo, CreateShipResponse.class);
        if (Objects.isNull(response)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return response;
    }

    /**
     * 创建达达运单
     *
     * @param cancelDadaShipBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ShipRpcServiceImpl.createDadaShipOrder")
    public Boolean cancelDadaShipOrder(CancelDadaShipBo cancelDadaShipBo) {
        String methodUrl = "/api/order/formalCancel";
        log.info("开始执行请求, url: {}, 参数: {}", methodUrl, JSON.toJSONString(cancelDadaShipBo));
        CancelShipResponse deductFee = processInvoke(methodUrl, cancelDadaShipBo, CancelShipResponse.class);
        if (Objects.isNull(deductFee)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return true;
    }


    /**
     * 调用商家信息
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = baseUrl.concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        Map<String, Object> paramMap = generateParams(requestObj);
        log.info("paramMap={}", JSON.toJSONString(paramMap));

        String httpResponse = SimpleHttpClient.simplePost(requestUrl, headMap, JSON.toJSONString(paramMap));
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        DaDaResponseBo response = JSON.parseObject(httpResponse, new TypeReference<DaDaResponseBo>() {
        });
        if (Objects.nonNull(response) && Objects.equals(response.getStatus(), "success")) {
            Object result = response.getResult();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        } else {
            log.error("[XfylOrderShipRpcServiceImpl->processResponse],达达接口访问返回异常!");
            throw new BusinessException(AngelPromiseBizErrorCode.CREATE_DADA_SHIP_ERROR);
        }
    }

    /**
     * 组装请求参数
     *
     * @param body
     * @return
     */
    private Map<String, Object> generateParams(Object body) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("source_id", sourceId);
        data.put("app_key", dadaAppKey);
        data.put("timestamp", LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
        data.put("format", "json");
        data.put("v", "1.0");
        data.put("body", JSON.toJSONString(body));
        data.put("signature", signature(data, dadaAppSecret));
        return data;
    }

    /**
     * 生成签名(具体参考文档: http://newopen.imdada.cn/#/quickStart/develop/safety?_k=kklqac)
     */
    private String signature(Map<String, Object> data, String appSecret) {
        // 请求参数按照【属性名】字典升序排序后，按照属性名+属性值拼接
        String signStr = data.keySet().stream()
                .sorted()
                .map(it -> String.format("%s%s", it, data.get(it)))
                .collect(Collectors.joining(""));

        // 拼接后的结果首尾加上appSecret
        String finalSignStr = appSecret + signStr + appSecret;

        // MD5加密并转为大写
        return DigestUtils.md5Hex(finalSignStr).toUpperCase();
    }
}
