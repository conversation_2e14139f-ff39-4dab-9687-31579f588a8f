package com.jdh.o2oservice.infrastructure.rpc.convert;

import cn.jdl.jecap.request.order.create.CommonCheckPreCreateOrderRequest;
import cn.jdl.jecap.response.order.create.CommonCheckPreCreateOrderResponse;
import cn.jdl.jecap.response.order.create.CommonCreateOrderResponse;
import cn.jdl.jecap.response.order.fulfillment.CommonModifyCancelOrderResponse;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCheckPreCreateOrderBo;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCheckPreCreateOrderParam;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCreateOrderBo;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonModifyCancelOrderBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/3/7
 * @description 京东物流rpc转换器
 */
@Mapper
public interface JdLogisticsRpcConverter {

    JdLogisticsRpcConverter instance = Mappers.getMapper(JdLogisticsRpcConverter.class);

    CommonCheckPreCreateOrderRequest toCommonCheckPreCreateOrderRequest(CommonCheckPreCreateOrderParam commonCheckPreCreateOrderParam);

    CommonCheckPreCreateOrderBo toCommonCheckPreCreateOrderBo(CommonCheckPreCreateOrderResponse data);

    CommonCreateOrderBo toCommonCreateOrderBo(CommonCreateOrderResponse data);

    CommonModifyCancelOrderBo toCommonModifyCancelOrderBo(CommonModifyCancelOrderResponse data);
}
