package com.jdh.o2oservice.infrastructure.rpc.support;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.b2b.common.response.Response;
import com.jdh.o2oservice.b2b.export.enterprise.B2bEnterpriseGwExport;
import com.jdh.o2oservice.b2b.export.enterprise.dto.B2bEnterpriseDto;
import com.jdh.o2oservice.b2b.export.enterprise.query.B2bEnterpriseRequest;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.B2bEnterpriseAccountGwExport;
import com.jdh.o2oservice.b2b.export.enterpriseaccount.dto.B2bEnterpriseAcountContractDto;
import com.jdh.o2oservice.b2b.export.enterprisevoucher.B2bEnterpriseVoucherGwExport;
import com.jdh.o2oservice.b2b.export.operationLog.OperationLogGwExport;
import com.jdh.o2oservice.b2b.export.operationLog.cmd.OperationLogCmd;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.rpc.B2bEnterpriseServiceRpc;
import com.jdh.o2oservice.export.support.command.B2bOperationLogCmd;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseInfoDto;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseRequest;
import com.jdh.o2oservice.infrastructure.rpc.convert.JdhEnterpriseBillRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName EnterpriseBillServiceRpcImpl
 * @Description 企业账单
 * <AUTHOR>
 * @Date 2025/03/07 11:49
 */
@Slf4j
@Service("b2bEnterpriseServiceRpc")
public class B2bEnterpriseServiceRpcImpl implements B2bEnterpriseServiceRpc {

    private final static String SUCCESS_CODE= "0000";

    /**
     * b2bEnterpriseAccountGwExport
     */
    @Resource
    private B2bEnterpriseAccountGwExport b2bEnterpriseAccountGwExport;
    /**
     * b2bEnterpriseGwExport
     */
    @Resource
    private B2bEnterpriseGwExport b2bEnterpriseGwExport;
    /**
     * operationLogGwExport
     */
    @Resource
    private OperationLogGwExport operationLogGwExport;

    @Resource
    private B2bEnterpriseVoucherGwExport b2bEnterpriseVoucherGwExport;


    /**
     * 查询企业账户+合同
     *
     * @param jdhEnterpriseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.support.B2bEnterpriseServiceRpcImpl.queryEnterpriseAccountAndContract")
    public JdhEnterpriseAccountContractDto queryEnterpriseAccountAndContract(JdhEnterpriseRequest jdhEnterpriseRequest) {
        try {
            B2bEnterpriseRequest b2bEnterpriseRequest = JdhEnterpriseBillRpcConverter.instance.convertToB2bEnterpriseRequest(jdhEnterpriseRequest);
            Response<B2bEnterpriseAcountContractDto> response = b2bEnterpriseAccountGwExport.queryEnterpriseAccountAndContract(b2bEnterpriseRequest);
            log.info("B2bEnterpriseServiceRpcImpl queryEnterpriseAccountAndContract result={}", JSON.toJSONString(response));
            if(Objects.nonNull(response) && SUCCESS_CODE.equals(response.getCode())){
                return JdhEnterpriseBillRpcConverter.instance.convertToEnterpriseAccountContractDto(response.getData());
            }
        } catch (Exception e) {
            log.error("B2bEnterpriseServiceRpcImpl queryEnterpriseAccountAndContract error e", e);
        }
        return null;
    }

    /**
     * 查询企业信息
     *
     * @param jdhEnterpriseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.support.B2bEnterpriseServiceRpcImpl.JdhEnterpriseRequest")
    public JdhEnterpriseInfoDto queryEnterpriseInfo(JdhEnterpriseRequest jdhEnterpriseRequest) {
        try {
            B2bEnterpriseRequest b2bEnterpriseRequest = JdhEnterpriseBillRpcConverter.instance.convertToB2bEnterpriseRequest(jdhEnterpriseRequest);
            Response<B2bEnterpriseDto> response = b2bEnterpriseGwExport.queryEnterpriseByPromiseId(b2bEnterpriseRequest);
            log.info("B2bEnterpriseServiceRpcImpl JdhEnterpriseRequest result={}", JSON.toJSONString(response));
            if(Objects.nonNull(response) && SUCCESS_CODE.equals(response.getCode())){
                return JdhEnterpriseBillRpcConverter.instance.convertToEnterpriseInfoDto(response.getData());
            }
        } catch (Exception e) {
            log.error("B2bEnterpriseServiceRpcImpl JdhEnterpriseRequest error e", e);
        }
        return null;
    }

    /**
     * 保存操作日志
     *
     * @param b2bOperationLogCmd
     * @return
     */
    @Override
    public Boolean saveOperationLog(B2bOperationLogCmd b2bOperationLogCmd) {
        try {
            OperationLogCmd operationLogCmd = JdhEnterpriseBillRpcConverter.instance.convertToOperationLogCmd(b2bOperationLogCmd);
            Response<Boolean> response = operationLogGwExport.saveOperationLog(operationLogCmd);
            log.info("B2bEnterpriseServiceRpcImpl saveOperationLog result={}", JSON.toJSONString(response));
            if(Objects.nonNull(response) && SUCCESS_CODE.equals(response.getCode())){
                return response.getData();
            }
        } catch (Exception e) {
            log.error("B2bEnterpriseServiceRpcImpl saveOperationLog error e", e);
        }
        return false;
    }

    @Override
    public Long queryEnterpriseId(Long enterpriseVoucherId) {
        try {
            Response<Long> response = b2bEnterpriseVoucherGwExport.queryEnterpriseId(enterpriseVoucherId);
            log.info("B2bEnterpriseServiceRpcImpl queryEnterpriseId result={}", JSON.toJSONString(response));
            if(Objects.nonNull(response) && SUCCESS_CODE.equals(response.getCode())){
                return response.getData();
            }
        } catch (Exception e) {
            log.error("B2bEnterpriseServiceRpcImpl queryEnterpriseId error e", e);
        }
        return null;
    }
}
