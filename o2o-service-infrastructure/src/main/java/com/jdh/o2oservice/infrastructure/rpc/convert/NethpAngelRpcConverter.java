package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.google.common.collect.Lists;
import com.jdh.nethp.doctor.entry.audit.client.dto.doctor.BaseDoctorImgDTO;
import com.jdh.nethp.doctor.entry.audit.client.dto.doctor.BaseDoctorInfoClientDTO;
import com.jdh.nethp.doctor.entry.audit.client.param.doctor.BaseDoctorInfoClientParam;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NethpBaseDoctorInfoClientParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: NethpAngelConverter
 * @Author: zhangxiaojie17
 * @Date: 2024/5/9
**/
@Mapper
public interface NethpAngelRpcConverter {

    NethpAngelRpcConverter ins = Mappers.getMapper(NethpAngelRpcConverter.class);

    @Mapping(target = "tenantId", constant = "JD8888")
    BaseDoctorInfoClientParam convert2BaseDoctorInfoClientParam(NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam);

    NethpBaseDoctorInfoClientBo convert2NethpBaseDoctorInfoClientBo(BaseDoctorInfoClientDTO baseDoctorInfoClientDTO);

    default NethpBaseDoctorInfoClientBo convert2NethpBaseBo(BaseDoctorInfoClientDTO baseDoctorInfoClientDTO) {
        NethpBaseDoctorInfoClientBo nethpBaseDoctorInfoClientBo = ins.convert2NethpBaseDoctorInfoClientBo(baseDoctorInfoClientDTO);
        BaseDoctorImgDTO baseDoctorImgDTO = baseDoctorInfoClientDTO.getBaseDoctorImgDTO();
        nethpBaseDoctorInfoClientBo.setIdCardImgList(baseDoctorImgDTO.getIdCardImgList());
        List<String> practiceImgList = baseDoctorImgDTO.getPracticeImgList();
        List<String> qualificationsImgList = baseDoctorImgDTO.getQualificationsImgList();
        List<String> titleImgList = baseDoctorImgDTO.getTitleImgList();
        List<String> imgList;
        try{
            imgList = Stream.of(practiceImgList, qualificationsImgList, titleImgList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }catch (Exception e){
            imgList = baseDoctorImgDTO.getPracticeImgList();
        }
        nethpBaseDoctorInfoClientBo.setPracticeImgList(imgList);
        return nethpBaseDoctorInfoClientBo;
    }
}
