package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import com.jd.addresstranslation.api.address.GBAddressToJDAddressService;
import com.jd.addresstranslation.api.address.JDAddressDistrictService;

import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.BaseResponse;

import com.jd.addresstranslation.api.base.JDDistrictInfo;


import com.jd.lbs.geocode.api.GeocodingService;
import com.jd.lbs.geocode.api.dto.GeoCompoundResultDto;
import com.jd.lbs.geocode.api.dto.GisPointDto;
import com.jd.lbs.jdlbsapi.c2c.TextParsingService;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseReqDto;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseResult;
import com.jd.medicine.base.common.exception.BusinessErrorCode;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.AddressTextParseRpcParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.JDAddressRpcParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.infrastructure.rpc.convert.JDDistrictConverter;
import com.jdh.o2oservice.infrastructure.rpc.convert.JdhAddressRpcConverter;
import com.jdh.o2oservice.infrastructure.rpc.convert.dto.GisPointDtoConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 地址转换
 *
 * <AUTHOR>
 * @date 2019-12-09 15:26
 */
@Service("addressRpc")
public class AddressRpcImpl implements AddressRpc {
    
    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(AddressRpcImpl.class);
    
    /**
     * SUCCESS
     */
    private static final int SUCCESS = 200;

    /**
     * SUCCESS_CONFLICT
     * 成功解析地址，但解析的地址与用户输入的地址有出入
     */
    private static final int SUCCESS_CONFLICT = 504;

    /**
     * 没有下级
     */
    private static final int NONE_CHILDREN = 503;
    

    
    /**
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=148161039
     * jdAddressDistrictService
     */
    @Resource
    private JDAddressDistrictService jdAddressDistrictService;

    /**
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=104674037
     */
    @Resource
    private GeocodingService geocodingService;

    @Resource
    private GBAddressToJDAddressService gbAddressToJDAddressService;

    /**
     * 智能文本解析
     */
    @Resource
    private TextParsingService textParsingService;


    @Override
    public BaseAddressBo getAddressByLatAndLon(JDAddressRpcParam jdAddressRequestInfo) {
        return null;
    }

    /**
     * 获取京标的省
     *
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getProvinces")
    public List<JDDistrictBo> getProvinces() {
        try{
            BaseResponse<List<JDDistrictInfo>> result = jdAddressDistrictService.getProvinces("health_examination_jdaddressdistrict");
            log.info("AddressRpcImpl -> getProvinces end, result={}", JSON.toJSONString(result));
            if (result == null || SUCCESS != result.getStatus()) {
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }

            return JDDistrictConverter.INSTANCE.dtoListToBoList(result.getResult());
        }catch (Throwable e){
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.impl.AddressRpcImpl.getProvinces", e.getMessage());
            log.error("AddressRpcImpl -> getProvinces exception", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 获取京标子级
     *
     * @param jdParentCode
     * @return
     */
    @Override
    //@LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getChildren")
    public List<JDDistrictBo> getChildren(Integer jdParentCode) {
        //log.info("AddressRpcImpl -> getChildren start, jdParentCode={}", jdParentCode);
        try {
            BaseResponse<List<JDDistrictInfo>> result = jdAddressDistrictService.getChildren("health_examination_jdaddressdistrict", jdParentCode);
            //log.info("AddressRpcImpl -> getChildren end, jdParentCode={}, result={}", jdParentCode, JSON.toJSONString(result));
            if (result != null && NONE_CHILDREN == result.getStatus()) {
                return Lists.newArrayList();
            }
            if (result == null || SUCCESS != result.getStatus()) {
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
            return JDDistrictConverter.INSTANCE.dtoListToBoList(result.getResult());
        } catch (Throwable e) {
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.impl.AddressRpcImpl.getChildren", e.getMessage());
            log.error("AddressRpcImpl -> getChildren exception, jdParentCode={}", jdParentCode, e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getJDAddressFromAddress")
    public BaseAddressBo getJDAddressFromAddress(String address) {
        log.info("AddressRpcImpl -> getJDAddressFromAddress start, address={}",address);
        try{
            BaseResponse<BaseAddressInfo> result = gbAddressToJDAddressService.getJDDistrictFromAddress(CommonConstant.APP_KEY_FOR_JDDISTRICTFROMLATLNG,address);
            log.info("AddressRpcImpl -> getJDAddressFromAddress end, result={}", JsonUtil.toJSONString(result));
            if (result == null || SUCCESS != result.getStatus()){
                throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
            }
            BaseAddressInfo baseAddressInfo = result.getResult();
            return JdhAddressRpcConverter.instance.convertToBaseAddressBo(baseAddressInfo);
        }catch (Throwable e){
            log.error("AddressRpcImpl -> getJDAddressFromAddress exception, address={}", e);
            throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
        }
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getLngLatByAddress")
    public GisPointBo getLngLatByAddress(String address) {
        log.info("AddressRpcImpl -> getLngLatByAddress start, address={}", address);
        com.jd.lbs.geocode.api.base.BaseResponse<GisPointDto> result = geocodingService.geo(CommonConstant.APP_KEY_FOR_GEO_CODING, address);
        log.info("AddressRpcImpl -> getLngLatByAddress end, result={}", JsonUtil.toJSONString(result));
        if (result == null || SUCCESS != result.getStatus() || Objects.isNull(result.getResult())){
            if(result.getStatus()==450){
                //450:解析地址出错
                throw new BusinessException(new DynamicErrorCode("450","请填写完整的省市区及详细地址信息"));
            }
            throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
        }
        return GisPointDtoConverter.ins.convertToGisPointBo(result.getResult());
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getGBandLngLatByDetailAddress")
    public GeoCompoundResultBo getGBandLngLatByDetailAddress(String address) {
        try{
            com.jd.lbs.geocode.api.base.BaseResponse<GeoCompoundResultDto> result = geocodingService.geoCompoundResult(CommonConstant.APP_KEY_FOR_GEO_CODING, address, null);
            log.info("AddressRpcImpl -> getGBandLngLatByDetailAddress end, result={}", JsonUtil.toJSONString(result));
            if (result == null || SUCCESS != result.getStatus()){
                throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
            }
            return GisPointDtoConverter.ins.convertToGeoCompoundResultBo(result.getResult());
        }catch (Throwable e){
            log.error("AddressRpcImpl -> getGBandLngLatByDetailAddress exception, address={}", address, e);
            throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
        }
    }

    @Override
    public List<LetterDistrictBo> getAllCitiesByLetter() {
        return null;
    }


    /**
     * 通过cityId查城市
     * @param districtInfo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.getDistrictInfo")
    public JDDistrictBo getDistrictInfo(Integer districtInfo) {

        log.info("AddressRpcImpl -> getDistrictInfo start, id={}", districtInfo);
        try{
            BaseResponse<JDDistrictInfo> result = jdAddressDistrictService.getDistrictInfo("health_examination_jdaddressdistrict", districtInfo);
            log.info("AddressRpcImpl -> getDistrictInfo end, id={}, result={}", districtInfo, JSON.toJSONString(result));
            if (result == null || SUCCESS != result.getStatus()){
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
            return JDDistrictConverter.INSTANCE.dtoToBo(result.getResult());
        }catch (Throwable e){
            log.error("AddressRpcImpl -> getDistrictInfo exception 未知异常, jdParentCode={}", districtInfo, e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 智能文本解析
     * https://lbsapi.jd.com/iframe.html?childURL=docid=2-75&childNav=1-17
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.AddressRpcImpl.parseText")
    public TextParseAddressBO parseText(AddressTextParseRpcParam param) {
        try{
            TextParseReqDto textParseReqDto = JdhAddressRpcConverter.instance.convertToTextParseReqDto(param);
            log.info("AddressRpcImpl -> parseText start, textParseReqDto={}",JSON.toJSONString(textParseReqDto));
            com.jd.lbs.jdlbsapi.dto.BaseResponse<TextParseResult> result = textParsingService.parseText(CommonConstant.APP_KEY_FOR_ADDRESS_TEXT_PARSE,textParseReqDto);
            log.info("AddressRpcImpl -> parseText end, result={}", JsonUtil.toJSONString(result));
            ArrayList<Integer> successStatusList = Lists.newArrayList(SUCCESS_CONFLICT, SUCCESS);
            if (result == null || result.getResult() == null || !successStatusList.contains(result.getStatus())
                    || result.getResult().getAddressInfo() == null){
                throw new com.jdh.o2oservice.base.exception.BusinessException(com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode.USER_ADDRESS_TEXT_PARSE_ERROR);
            }
            return JdhAddressRpcConverter.instance.convertToTextParseAddressBO(result.getResult());
        }catch (com.jdh.o2oservice.base.exception.BusinessException e){
            log.error("AddressRpcImpl -> parseText business exception, param={}", JSON.toJSONString(param), e);
            throw e;
        }catch (Throwable e){
            log.error("AddressRpcImpl -> parseText Throwable, param={}", JSON.toJSONString(param), e);
            throw new com.jdh.o2oservice.base.exception.BusinessException(com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode.USER_ADDRESS_TEXT_PARSE_ERROR);
        }
    }
}
