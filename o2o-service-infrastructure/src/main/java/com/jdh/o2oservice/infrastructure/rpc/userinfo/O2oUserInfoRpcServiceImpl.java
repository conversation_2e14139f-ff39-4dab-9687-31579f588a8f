package com.jdh.o2oservice.infrastructure.rpc.userinfo;

import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.user.sdk.export.UserInfoExportService;
import com.jd.user.sdk.export.domain.UserBasicInfoVo;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.userinfo.O2oUserInfoRpcService;
import com.jdh.o2oservice.core.domain.support.userinfo.bo.UserBaseInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @InterfaceName:O2oUserInfoRpcServiceImpl
 * @Description: 账号接口
 * @Author: yaoqinghai
 * @Date: 2024/8/6
 * @Vserion: 1.0
 **/
@Component("o2oUserInfoRpcService")
@Slf4j
public class O2oUserInfoRpcServiceImpl implements O2oUserInfoRpcService {

    /** 用户rpc服务 */
    @Resource
    private UserInfoExportService userInfoExportService;

    /**  90为Bpin 非90 为Cpin */
    private static final Integer B_PIN_LEVEL = 90;

    /**
     * 90为Bpin 非90 为Cpin
     * https://cf.jd.com/pages/viewpage.action?pageId=100092999
     *
     * @param pin
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "O2oUserInfoRpcServiceImpl.isBpin")
    public Boolean isBpin(String pin) {
        try {
            log.info("O2oUserInfoRpcServiceImpl->isBpin request={}", pin);
            Integer level = userInfoExportService.getUserLevel(pin);
            log.info("O2oUserInfoRpcServiceImpl->isBpin level={}", level);
            return Objects.equals(level, B_PIN_LEVEL);
        }catch (Exception e){
            log.error("[O2oUserInfoRpcServiceImpl -> isBpin],查询账号信息异常!", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }catch (Throwable e){
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 根据pin获取用户基本信息
     *
     * @param pin      用户的pin码
     * @param loadType 加载类型，用于区分不同的加载场景
     * @return 用户基本信息对象
     */
    @Override
    @LogAndAlarm
    public UserBaseInfoBo getUserBaseInfoByPin(String pin, int loadType) {
        try {
            UserBasicInfoVo userBaseInfoByPin = userInfoExportService.getUserBasicInfoByPin(pin, loadType);
            log.info("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoByPin={},", JsonUtil.toJSONString(userBaseInfoByPin));
            if (Objects.nonNull(userBaseInfoByPin)){
                UserBaseInfoBo userBaseInfoBo = new UserBaseInfoBo();
                userBaseInfoBo.setPin(userBaseInfoByPin.getPin());
                userBaseInfoBo.setYunBigImageUrl(userBaseInfoByPin.getYunBigImageUrl());
                userBaseInfoBo.setYunMidImageUrl(userBaseInfoByPin.getYunMidImageUrl());
                userBaseInfoBo.setYunSmaImageUrl(userBaseInfoByPin.getYunSmaImageUrl());
                userBaseInfoBo.setMobile(userBaseInfoByPin.getMobile());
                log.info("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoBo={},", JsonUtil.toJSONString(userBaseInfoBo));
                return userBaseInfoBo;
            }
            log.info("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin,userBaseInfoBo null");
            return null;
        }catch (Exception e){
            log.error("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin error!", e);
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }catch (Throwable e){
            log.error("O2oUserInfoRpcServiceImpl->getUserBaseInfoByPin error!", e);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }
}
