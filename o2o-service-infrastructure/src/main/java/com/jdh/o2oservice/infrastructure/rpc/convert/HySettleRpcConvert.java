package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.medicine.settlement.center.export.dto.BankCardDetailDTO;
import com.jd.medicine.settlement.center.export.param.BankCardDetailParam;
import com.jd.medicine.settlement.center.export.param.withdraw.AddWithdrawAccountParam;
import com.jd.medicine.settlement.center.export.param.withdraw.QueryWithdrawAccountParam;
import com.jd.medicine.settlement.center.export.param.withdraw.WithdrawAccountDTO;
import com.jd.medicine.settlement.center.export.param.withdraw.WithdrawParam;
import com.jd.medicine.settlement.center.export.param.withdraw.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelAddAccountAmountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelCashOutVo;
import com.jdh.o2oservice.core.domain.settlement.vo.BankCardDetailVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/15 3:38 下午
 * @Description:
 */
@Mapper
public interface HySettleRpcConvert {
    HySettleRpcConvert ins = Mappers.getMapper(HySettleRpcConvert.class);

    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "pin", source = "userPin")
    BankCardDetailParam context2BankCardDetailParam(AngelSettlementQueryContext query);

    BankCardDetailVo dto2BankCardDetailVo(BankCardDetailDTO detailDTO);

    @Mapping(target = "pin", source = "userPin")
    @Mapping(target = "tenantId", constant = "JD8888")
    QueryWithdrawAccountParam context2QueryWithdrawAccountParam(AngelSettlementQueryContext query);

    @Mapping(target = "pin", source = "userPin")
    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "pageNo", source = "pageNum")
    @Mapping(target = "pageSize", source = "pageSize")
    QueryWithdrawDetailListParam context2QueryWithdrawDetailListParam(AngelSettlementQueryContext query);

    @Mapping(target = "feeType", constant = "71")
    @Mapping(target = "withdrawAmount", source = "settleAmount")
    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "pin", source = "userPin")
    @Mapping(target = "title", constant = "提现")
    @Mapping(target = "subTitle", constant = "提现")
    WithdrawParam context2WithdrawParam(AngelCashOutVo angelCashOutVo);


    WithdrawAccountVo dto2WithdrawAccountVo(WithdrawAccountDTO accountDTO);

    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "detailId", source = "settlementNo")
    QueryWithdrawDetailParam getQueryWithdrawDetailParam(Long settlementNo);


    @Mapping(target = "tenantId", constant = "JD8888")
    @Mapping(target = "detailId", source = "settlementNo")
    QueryWithdrawRecordDetailParam getQueryWithdrawRecordDetailParam(Long settlementNo);

    WithdrawDetailVo dto2WithdrawDetailVo(WithdrawDetailDTO detailDTO);

    List<WithdrawDetailVo> dto2WithdrawDetailVo(List<WithdrawDetailDTO> detailDTOList);

    //提现实体转
    @Mapping(target = "direction", constant = "0")
    @Mapping(target = "amount", source = "withdrawAmount")
    @Mapping(target = "detailId", source = "withdrawDetailId")
    WithdrawDetailVo withdrawdDto2WithdrawDetailVo(WithdrawRecordDetailDTO detailDTO);


    AddWithdrawAccountParam angelAddAccountAmountVo2WithdrawParam(AngelAddAccountAmountVo angelAddAccountAmountVo);
}
