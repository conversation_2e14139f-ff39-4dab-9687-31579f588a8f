package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.jd.jdorders.component.export.OrderMiddlewareCBDExport;
import com.jd.jdorders.component.vo.request.OrderQueryRequest;
import com.jd.jdorders.component.vo.response.OrderQueryResponse;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.order.export.domain.ComponentBase;
import com.jd.order.ioms.component.export.IomsModifyCBDExport;
import com.jd.order.ioms.component.vo.dict.ExtParamField;
import com.jd.order.ioms.component.vo.dict.ModifyBizTypeDict;
import com.jd.order.ioms.component.vo.dict.UpdateConditionField;
import com.jd.order.ioms.component.vo.param.OrderParam;
import com.jd.order.ioms.component.vo.request.ModifyRequest;
import com.jd.order.ioms.component.vo.response.IomsResponse;
import com.jd.order.sdk.domain.param.ClientInfo;
import com.jd.order.sdk.domain.param.OrderInfoQueryVoParam;
import com.jd.order.sdk.domain.result.OrderInfoQueryResult;
import com.jd.order.sdk.export.QueryOrderInfoService;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.RpcBusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.enums.OrderListServiceStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderListTabEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.infrastructure.rpc.convert.OrderInfoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * TradeInfoRpcImpl 交易域rpc实现
 *
 * <AUTHOR>
 * @version 2024/3/1 16:14
 **/
@Slf4j
@Service
public class OrderInfoRpcImpl implements OrderInfoRpc {

    @Resource
    private QueryOrderInfoService queryOrderInfoService;

    /**
     * omsModifyCBDExport
     */
    @Resource
    private IomsModifyCBDExport omsModifyCBDExport;
    /**
     * productExtApplication
     */
    @Autowired
    private ProductExtApplication productExtApplication;
    /**
     * tradeExtApplication
     */
    @Autowired
    private TradeExtApplication tradeExtApplication;

    /**
     * @VtpOrderInfoRpc
     */
    @Resource
    private VtpOrderInfoRpcImpl vtpOrderInfoRpc;

    /**
     * 订单查询大接口
     */
    @Resource
    OrderMiddlewareCBDExport orderMiddlewareCBDExport;


    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=621343550
     *
     * @param context 入参
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.OrderInfoRpcImpl.queryOrderInfo")
    public JdOrder queryOrderInfo(OrderInfoQueryContext context) {
        try {
            OrderInfoConverter convertor = OrderInfoConverter.convertor;
            OrderInfoQueryVoParam orderInfoQueryVoParam = convertor.orderInfoQueryContext2Param(context);
            if (Objects.isNull(orderInfoQueryVoParam) || StringUtils.isBlank(orderInfoQueryVoParam.getPin())) {
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            orderInfoQueryVoParam.setClientInfo(buildClientInfo());
            OrderInfoQueryResult result = queryOrderInfoService.queryOrderInfo(orderInfoQueryVoParam);
            log.info("OrderInfoRpcImpl queryOrderInfo orderInfoQueryVoParam={}, result={}", JSON.toJSONString(orderInfoQueryVoParam), JSON.toJSONString(result));
            if (result == null) {
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if ("200".equals(result.getCode())) {
                return convertor.orderInfoQueryResult2JdOrder(result, context);
            }
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e) {
            log.error("OrderInfoRpcImpl queryOrderInfo 查询订单详情 BusinessException orderInfoQueryContext:{}", JSONObject.toJSONString(context), e);
            throw e;
        } catch (Throwable e) {
            log.error("OrderInfoRpcImpl queryOrderInfo 查询订单详情 error orderInfoQueryContext:{}", JSONObject.toJSONString(context), e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * 改写机构号
     * @param orderId
     * @param idCompanyBranch
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.OrderInfoRpcImpl.reviseOrderInstitutionNumber")
    public Boolean reviseOrderInstitutionNumber(Long orderId,Integer idCompanyBranch) {
        log.info("OrderInfoRpcImpl -> reviseOrderInstitutionNumber orderId={}", orderId);
        ModifyRequest modifyRequest = getModifyRequest(orderId,idCompanyBranch);
        try {
            log.info("OrderInfoRpcImpl -> reviseOrderInstitutionNumber modifyRequest={}", JsonUtil.toJSONString(modifyRequest));
            IomsResponse response = omsModifyCBDExport.modifyOrder(modifyRequest);
            if(Objects.nonNull(response) && response.isSuccess()){
                return Boolean.TRUE;
            }
            log.info("OrderInfoRpcImpl -> reviseOrderInstitutionNumber response={}", JsonUtil.toJSONString(response));
            return Boolean.FALSE;
        }catch (com.jd.medicine.base.common.exception.BusinessException e){
            log.error("OrderInfoRpcImpl -> reviseOrderInstitutionNumber business fail", e);
            throw e;
        }catch (Throwable e){
            log.error("OrderInfoRpcImpl -> reviseOrderInstitutionNumber error", e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * 修订中台订单的完成状态
     *
     * @param orderId 单号
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.OrderInfoRpcImpl.reviseOrderFinishState")
    public Boolean reviseOrderFinishState(Long orderId) {
        return this.reviseOrderInstitutionNumber(orderId, null);
    }

    /**
     * @param skuId
     * @return
     */
    @Override
    public Integer queryJdhSkuInfoBySkuId(Long skuId) {
        JdhSkuDto jdhSkuDto = productExtApplication.queryJdhSkuInfoBySkuId(skuId);
        AssertUtils.nonNull(jdhSkuDto, TradeErrorCode.ORDER_IS_NULL);
        return jdhSkuDto.getServiceType();
    }

    /**
     * 查询订单
     *
     * @param orderId
     * @return
     */
    @Override
    public List<JdOrder> queryOrderIdList(Long orderId) {
        List<JdOrderDTO> jdOrderDTOList = tradeExtApplication.queryOrderIdList(orderId);
        return OrderInfoConverter.convertor.jdOrderDTOToList(jdOrderDTOList);
    }

    /**
     * 查询订单大数据
     *
     * @param orderId
     * @return map
     */
    @Override
    @LogAndAlarm()
    public Map<String, Object> getOrderData(Long orderId) {
        if (orderId == null) {
            return Collections.emptyMap();
        }
        OrderQueryRequest queryRequest = buildSystemRequest(orderId);
        OrderQueryResponse orderQueryResponse = orderMiddlewareCBDExport.getOrderData(queryRequest);
        if (orderQueryResponse == null || !orderQueryResponse.isSuccess()) {
            return Collections.emptyMap();
        }
        return orderQueryResponse.getDataMap();
    }
    /**
     * 修改sendpay变更订单列表归堆
     * <a href="https://joyspace.jd.com/pages/gfl8dKyQuk3j1tjobSUO">...</a>
     * <a href="https://joyspace.jd.com/pages/1w9PMxUGIwD5xzGHBf7b">...</a>
     *
     * @param orderId
     * @param orderTabEnum
     * @return
     */
    @Override
    public Boolean modifySendPayForOrderTabChange(Long orderId, OrderListTabEnum orderTabEnum) {
        log.info("OrderInfoRpcImpl -> modifySendPayForOrderTabChange orderId={} orderTabEnum={}", orderId, orderTabEnum);
        if (orderId == null || orderTabEnum == null) {
            return Boolean.FALSE;
        }
        Map<String, Integer> sendPayMap = new HashMap<>();
        if (OrderListTabEnum.FINISH.equals(orderTabEnum)) {
            sendPayMap.put(SendpayValueEnum.SEND_PAY_825_4.getSendPayStr(), Integer.valueOf(SendpayValueEnum.SEND_PAY_825_4.getSendPayValue()));
        }
        return modifySendPay(orderId, sendPayMap);
    }

    /**
     * 修改sendpay变更订单列表状态描述 待使用、服务中、已完成
     * <a href="https://joyspace.jd.com/pages/gfl8dKyQuk3j1tjobSUO">...</a>
     * <a href="https://joyspace.jd.com/pages/1w9PMxUGIwD5xzGHBf7b">...</a>
     *
     * @param orderId
     * @param orderListServiceStatusEnum
     * @return
     */
    @Override
    public Boolean modifySendPayForOrderListStatus(Long orderId, OrderListServiceStatusEnum orderListServiceStatusEnum) {
        log.info("OrderInfoRpcImpl -> modifySendPayForOrderListStatus orderId={} orderListServiceStatusEnum={}", orderId, orderListServiceStatusEnum);
        if (orderId == null || orderListServiceStatusEnum == null) {
            return Boolean.FALSE;
        }
        Map<String, Integer> sendPayMap = new HashMap<>();
        if (OrderListServiceStatusEnum.FINISH.equals(orderListServiceStatusEnum)) {
            sendPayMap.put(SendpayValueEnum.SEND_PAY_1255_3.getSendPayStr(), Integer.valueOf(SendpayValueEnum.SEND_PAY_1255_3.getSendPayValue()));
        } else if (OrderListServiceStatusEnum.SERVICING.equals(orderListServiceStatusEnum)) {
            sendPayMap.put(SendpayValueEnum.SEND_PAY_1255_2.getSendPayStr(), Integer.valueOf(SendpayValueEnum.SEND_PAY_1255_2.getSendPayValue()));
        } else if (OrderListServiceStatusEnum.UNUSED.equals(orderListServiceStatusEnum)) {
            sendPayMap.put(SendpayValueEnum.SEND_PAY_1255_1.getSendPayStr(), Integer.valueOf(SendpayValueEnum.SEND_PAY_1255_1.getSendPayValue()));
        }
        return modifySendPay(orderId, sendPayMap);
    }

    /**
     * 修改sendpay
     * @param orderId
     * @param sendPayMap
     * @return
     */
    private Boolean modifySendPay(Long orderId, Map<String, Integer> sendPayMap) {
        log.info("OrderInfoRpcImpl -> modifySendPay orderId={}", orderId);
        try {
            if (orderId == null || CollUtil.isEmpty(sendPayMap)) {
                return false;
            }
            ModifyRequest modifyRequest = new ModifyRequest();
            ComponentBase componentBase = ComponentBase.builder().appName(CommonConstant.APP_NAME).region("301").build();
            OrderParam orderParam = OrderParam.builder().id(orderId).executor(CommonConstant.APP_NAME).build();
            orderParam.putExtParams(ExtParamField.EXT_DOTYPE, 514).putExtParams(ExtParamField.EXT_EXECUTORNAME, CommonConstant.APP_NAME);
            for (Map.Entry<String, Integer> sendPayEntry : sendPayMap.entrySet()) {
                orderParam.addSendpayDict(sendPayEntry.getKey(), sendPayEntry.getValue());
            }
            modifyRequest.setComponentBase(componentBase);
            modifyRequest.setOrderParam(orderParam);
            modifyRequest.setBizType(ModifyBizTypeDict.MODIFY_SENDPAY);
            log.info("OrderInfoRpcImpl -> modifySendPay modifyRequest={}", JsonUtil.toJSONString(modifyRequest));
            IomsResponse response = omsModifyCBDExport.modifyOrder(modifyRequest);
            if(Objects.nonNull(response) && response.isSuccess()){
                return Boolean.TRUE;
            }
            log.info("OrderInfoRpcImpl -> modifySendPay response={}", JsonUtil.toJSONString(response));
            return Boolean.FALSE;
        }catch (com.jd.medicine.base.common.exception.BusinessException e){
            log.error("OrderInfoRpcImpl -> modifySendPay business fail", e);
            throw e;
        }catch (Throwable e){
            log.error("OrderInfoRpcImpl -> modifySendPay error", e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * 改写机构号 + 拉完成
     * @return
     * @param orderId
     */
    private ModifyRequest getModifyRequest(Long orderId, Integer idCompanyBranch) {
        ModifyRequest modifyRequest = new ModifyRequest();
        ComponentBase componentBase = ComponentBase.builder().appName(CommonConstant.APP_NAME).region("301").build();
        OrderParam orderParam = OrderParam.builder().build();
        if(Objects.nonNull(idCompanyBranch)){
            orderParam.setId(orderId);
            orderParam.setOprator(idCompanyBranch);
            orderParam.setExecutor(CommonConstant.APP_NAME);
            modifyRequest.setBizType(ModifyBizTypeDict.MODIFY_BASIC_INFO);
            orderParam.putExtParams(ExtParamField.EXT_DOTYPE, 3006);
            orderParam.putExtParams(ExtParamField.EXT_EXECUTORNAME, CommonConstant.APP_NAME);
        }else {
            orderParam.setId(orderId);
            orderParam.setState(30);
            orderParam.setExecutor(CommonConstant.APP_NAME);
            orderParam.addUpdateCondition(UpdateConditionField.CONDITION_STATE,11)
                    // 主站之前的business.setType()
                    .putExtParams(ExtParamField.EXT_DOTYPE, 11)
                    // 主站之前的business.setOperatorName()
                    .putExtParams(ExtParamField.EXT_EXECUTORNAME, CommonConstant.APP_NAME);
            modifyRequest.setBizType(ModifyBizTypeDict.MODIFY_STATE);
        }
        modifyRequest.setComponentBase(componentBase);
        modifyRequest.setOrderParam(orderParam);

        return modifyRequest;
    }


    private ClientInfo buildClientInfo() throws Exception {
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setSystemName("jdh-o2o-service");
        clientInfo.setToken("889665A2D995C1340F27A71F0A54656E");
        return clientInfo;
    }

    /**
     * @param orderId orderId
     * @return orderId
     */
    private OrderQueryRequest buildSystemRequest(Long orderId) {
        // 请求对象
        OrderQueryRequest request = new OrderQueryRequest();
        // 通用业务参数
        ComponentBase componentBase = new ComponentBase();
        // 见2.2部分入参对象里的对应站点region值
        componentBase.setRegion("301");
        // 必传字段，申请权限成功后会分配
        componentBase.setAppName("J-dos-physicalexamination");

        // 查询字段列表   字段明细见文档  https://cf.jd.com/pages/viewpage.action?pageId=172781817
        List querys  = Arrays.asList("V_ORDERXML","V_CARTXML");
        // 赋值
        request.setComponentBase(componentBase);
        request.setBizType("2");
        request.setOrderId(orderId);
        request.setQueryKeys(querys);
        // 1.获取原始序列化格式数据（按下单写入数据格式返回）必填
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("serializeType","raw");

        request.setExtMap(extMap);
        return request;
    }
}
