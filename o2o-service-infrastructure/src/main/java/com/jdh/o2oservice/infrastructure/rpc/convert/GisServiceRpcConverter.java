package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.lbs.geofencing.api.dto.CustomPresortRequestDto;
import com.jd.lbs.geofencing.api.dto.CustomPresortResponseDto;
import com.jd.pap.priceinfo.sdk.domain.response.PriceInfoResponse;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.CustomPresortResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.PriceInfoResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.CustomPresortRpcParam;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper
public interface GisServiceRpcConverter {

    GisServiceRpcConverter instance = Mappers.getMapper(GisServiceRpcConverter.class);

    List<CustomPresortResponseBO> convertDto2Bo(List<CustomPresortResponseDto> customPresortResponseDtos);

    CustomPresortRequestDto convertParam2Dto(CustomPresortRpcParam customPresortRpcParam);

}
