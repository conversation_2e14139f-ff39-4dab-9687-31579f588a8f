package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.xfyl.merchant.export.dto.XfylProviderDTO;
import com.jd.pop.wxo2o.spi.store.to.StoreInfoTO;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.StoreRpcBO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

/**
 * @author: yangxiyu
 * @date: 2023/12/28 11:14 上午
 * @version: 1.0
 */
@Mapper
public interface PopStoreConverter {

    PopStoreConverter convertor = Mappers.getMapper(PopStoreConverter.class);


    default StoreRpcBO popStore2RpcBO(StoreInfoTO storeInfo, XfylProviderDTO provider){
        if (Objects.isNull(storeInfo)){
            return null;
        }

        StoreRpcBO bo = new StoreRpcBO();
        bo.setStoreId(String.valueOf(storeInfo.getStoreId()));
        bo.setStoreName(storeInfo.getStoreName());
        bo.setPhone(storeInfo.getPhone());
        bo.setStoreAddress(storeInfo.getStoreAddress());
        bo.setVenderId(storeInfo.getVenderId());
        bo.setOutStoreId(storeInfo.getExStoreId());
        bo.setStoreHours(storeInfo.getBusinessTime());
        bo.setProvinceName(storeInfo.getAddOneAddress());
        bo.setCityName(storeInfo.getAddSecAddress());
        bo.setCountyName(storeInfo.getAddThiAddress());
        bo.setProvinceId(storeInfo.getAddOneCode());
        bo.setCityId(storeInfo.getAddSecCode());
        //jdos编译报错，包版本没对应上  loc提供的包找不到logo字段返回，暂时无用，先注释掉
        bo.setStoreLogo(joinImageUrl(storeInfo.getStoreLogo()));
        bo.setStoreImg(joinImageUrl(storeInfo.getStorePic()));
        if(Objects.nonNull(storeInfo.getStoreLongitudeLatitudeTO())){
            bo.setLat(storeInfo.getStoreLongitudeLatitudeTO().getLatitude());
            bo.setLng(storeInfo.getStoreLongitudeLatitudeTO().getLongitude());
        }
        //消医频道页
        bo.setZongheScore(storeInfo.getExtendData().get("zonghe_score") == null? -1 :Double.parseDouble(storeInfo.getExtendData().get("zonghe_score")));
        bo.setStoreLogo(storeInfo.getStoreLogo());
        bo.setCategoryId2(storeInfo.getCategoryId2());
        bo.setAddThiAddress(storeInfo.getAddThiAddress());
        bo.setStoreStatus(storeInfo.getStoreStatus());
        bo.setStatus(storeInfo.getStatus());
        bo.setBusinessBeginTime(storeInfo.getExtendData().get("businessBeginTime"));
        bo.setBusinessEndTime(storeInfo.getExtendData().get("businessEndTime"));
        bo.setChannelNo(provider.getChannelNo());
        return bo;
    }

    /**
     * 门店图片链接拼装
     *
     * @param path path
     * @return {@link String}
     */
    default String joinImageUrl(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        if (path.startsWith("jfs")){
            return CommonConstant.IMG_URL_PREFIX + path;
        } else if (path.startsWith("//img10.")) {
            return CommonConstant.HTTPS + ":" + path;
        } else {
            return path;
        }
    }
}
