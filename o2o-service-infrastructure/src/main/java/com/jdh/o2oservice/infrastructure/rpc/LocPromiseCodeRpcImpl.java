package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.pop.order.loc.assembledflow.soa.service.LocOrderCodeSoaService;
import com.jd.pop.order.loc.assembledflow.soa.service.domain.ConsumeCodeInfo;
import com.jd.pop.order.loc.assembledflow.soa.service.domain.LocOrderCode;
import com.jd.pop.order.loc.assembledflow.soa.service.query.LocOrderCodeQuery;
import com.jd.pop.order.loc.assembledflow.soa.service.result.Result;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.promise.rpc.LocPromiseCodeRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.LocCodeBO;
import com.jdh.o2oservice.core.domain.promise.rpc.param.LocCodeConsumeParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.PopLocConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LocPromiseCodeRpcImpl implements LocPromiseCodeRpc {

    /**
     * locOrderCodeSoaService
     */
    @Resource
    private LocOrderCodeSoaService locOrderCodeSoaService;

    /**
     * listLocCode
     *
     * @param userPin 用户PIN
     * @param orderId 订单ID
     * @return {@link List}<{@link LocCodeBO}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.LocPromiseCodeRpcImpl.listLocCode")
    public List<LocCodeBO> listLocCode(String userPin, Long orderId) {
        LocOrderCodeQuery query = new LocOrderCodeQuery();
        query.setOrderId(orderId);
        query.setPage(NumConstant.NUM_1);
        query.setPageSize(NumConstant.NUM_500);
        log.info("LocPromiseCodeRpcImpl -> getLocCodeListByPin start, orderId:{}", orderId);
        try{
            Result<List<LocOrderCode>> result = locOrderCodeSoaService.getLocCodeListByPin(userPin, query);
            log.info("LocPromiseCodeRpcImpl -> getLocCodeListByPin end,result:{}",JSON.toJSONString(result));
            if (result.isSuccess()){
                return PopLocConverter.convertor.rpcCode2CodeBO(result.getResultList());
            }
        }catch (Throwable e){
            log.error("LocPromiseCodeRpcImpl -> getLocCodeListByPin exception", e);
        }
        return null;
    }

    @Override
    public Boolean consumeCode(LocCodeConsumeParam param) {
        log.info("LocPromiseCodeRpcImpl -> consumeCode start, consumeCodeInfo={}", JSON.toJSONString(param));
        ConsumeCodeInfo codeInfo = new ConsumeCodeInfo();
        codeInfo.setCodeNum(param.getCode());
        codeInfo.setPwdNumber(param.getCodePwd());
        codeInfo.setVenderId(param.getVenderId());
        codeInfo.setShopId(Long.valueOf(param.getStoreId()));
        try{
            codeInfo.setConsumeSource("6");
            Result<Boolean> result = locOrderCodeSoaService.consumeCode(codeInfo);
            log.info("LocPromiseCodeRpcImpl -> consumeCode end,result={}", JSON.toJSONString(result));
            if (result.isSuccess() && result.getResultList()){
                return true;
            }
        }catch (Throwable e){
            log.error("LocPromiseCodeRpcImpl -> consumeCode exception, consumeCodeInfo={}", JSON.toJSONString(param), e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        return false;
    }
}
