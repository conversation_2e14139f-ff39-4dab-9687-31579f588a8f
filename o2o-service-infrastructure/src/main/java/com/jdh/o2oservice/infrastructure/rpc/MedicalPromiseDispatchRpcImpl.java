package com.jdh.o2oservice.infrastructure.rpc;

import com.jdh.o2oservice.application.angel.StationExtApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.AngelStationExtDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.JdhMedicalPromiseQueryDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.AngelStationExtQueryParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseQueryParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.rpc.MedicalPromiseDispatchRpc;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.query.AngelStationDetailRequest;
import com.jdh.o2oservice.export.angel.query.AngelStationRequest;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.infrastructure.rpc.convert.MedicalPromiseRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName:MedicalPromiseDispatchRpcImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/21 17:06
 * @Vserion: 1.0
 **/
@Service("medicalPromiseDispatchRpc")
@Slf4j
public class MedicalPromiseDispatchRpcImpl implements MedicalPromiseDispatchRpc {

    /** */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private StationExtApplication stationExtApplication;

    /**
     * 派实验室
     *
     * @param medicalPromiseParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "MedicalPromiseDispatchRpcImpl.dispatchStation")
    public Boolean dispatchStation(MedicalPromiseParam medicalPromiseParam) {
        MedicalPromiseDispatchCmd medicalPromiseDispatchCmd = MedicalPromiseRpcConverter.INS.convertToMedicalPromiseDispatchCmd(medicalPromiseParam);
        return medicalPromiseApplication.storeDisPatch(medicalPromiseDispatchCmd);
    }

    /**
     * 查询实验室
     *
     * @param medicalPromiseQueryParam
     */
    @Override
    @LogAndAlarm(jKey = "MedicalPromiseDispatchRpcImpl.queryMedicalPromiseList")
    public List<JdhMedicalPromiseQueryDto> queryMedicalPromiseList(MedicalPromiseQueryParam medicalPromiseQueryParam) {
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(medicalPromiseQueryParam.getPromiseId());
        medicalPromiseListRequest.setInvalid(medicalPromiseQueryParam.getInvalid());
        return MedicalPromiseRpcConverter.INS.convertToJdhMedicalPromiseQueryVoList(medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest));
    }

    /**
     * 查询服务站明细
     *
     * @param angelStationExtQueryParam
     * @return
     */
    @Override
    public AngelStationExtDto queryAngelStationDetail(AngelStationExtQueryParam angelStationExtQueryParam) {
        AssertUtils.nonNull(angelStationExtQueryParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.hasText(angelStationExtQueryParam.getAngelStationId(), BusinessErrorCode.ILLEGAL_ARG_ERROR);

        AngelStationDetailRequest angelStationRequest = new AngelStationDetailRequest();
        angelStationRequest.setAngelStationId(Long.valueOf(angelStationExtQueryParam.getAngelStationId()));
        return MedicalPromiseRpcConverter.INS.convertToAngelStationExtDto(stationExtApplication.queryAngelStationDetail(angelStationRequest));
    }
}
