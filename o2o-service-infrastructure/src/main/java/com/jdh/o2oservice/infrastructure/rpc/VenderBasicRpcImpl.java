package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.boundaryless.store.center.spi.store.StoresServiceProvider;
import com.jd.boundaryless.store.center.spi.store.to.StoreInfoTO;
import com.jd.boundaryless.store.center.spi.store.to.StoresQueryTo;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.o2o.arrive.store.api.UserApiService;
import com.jd.o2o.arrive.store.api.request.BaseRequest;
import com.jd.o2o.arrive.store.api.request.UserRequest;
import com.jd.o2o.arrive.store.api.response.Response;
import com.jd.o2o.arrive.store.api.response.UserResponse;
import com.jd.pop.seller.i18n.common.params.I18nParam;
import com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade;
import com.jd.pop.vender.center.i18n.api.business.vo.VenderDynamicSubjectVo;
import com.jd.seller.baccount.cache.api.belong.AccountBelongFacade;
import com.jd.seller.baccount.cache.api.belong.param.AccountBelongParam;
import com.jd.seller.baccount.cache.api.belong.vo.AccountBelongVO;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.provider.bo.JmListAccountStoreIdParam;
import com.jdh.o2oservice.core.domain.provider.bo.PopJmAccountBO;
import com.jdh.o2oservice.core.domain.provider.rpc.VenderBasicRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商家信息 RPC
 * @author: yangxiyu
 * @date: 2024/1/15 5:19 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class VenderBasicRpcImpl implements VenderBasicRpc {



    /**
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=*********
     */
    @Resource
    private AccountBelongFacade accountBelongFacade;

    /**
     * https://joyspace.jd.com/pages/Uknp1Q0SRdhHFIBKdkNd
     */
    @Resource
    private BusinessQueryByVenderIdAndKeysFacade businessQueryByVenderIdAndKeysFacade;
    /**
     * 门店管理接口
     */
    @Resource
    private StoresServiceProvider storesServiceProvider;

    /** */
    @Resource
    private UserApiService userApiService;

    /** 请求来源 */
    private static final String SOURCE = "jd_health";

    /** 成功码 */
    private static final int SUCCESS_CODE = 200;

    @Override
    @LogAndAlarm(jKey = "com.jd.health.xfyl.merchant.rpc.impl.VenderBasicRpcImpl.getVenderIdByPin")
    public String getVenderId(String userPin) {

        AccountBelongParam param = new AccountBelongParam();
        param.setPin(userPin);
        try {
            List<AccountBelongVO> list = accountBelongFacade.findAllBelongByPin(param, null);
            log.info("VenderBasicRpcImpl -> getVenderIdByPin info,list={}", JSON.toJSONString(list));
            for (AccountBelongVO vo : list) {
                // 200表示POP店铺账号
                if (Objects.equals(vo.getBelongType(), 200)){
                    return vo.getBelongBizId();
                }
            }
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }catch (Throwable e){
            log.error("VenderBasicRpcImpl -> getVenderIdByPin exception,查询venderId 失败", e);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }


    /**
     * 获取商家账号信息
     * @param userPin
     * @return
     */
    @Override
    public PopJmAccountBO getVenderAccount(String userPin) {
        AccountBelongParam param = new AccountBelongParam();
        param.setPin(userPin);
        try {
            List<AccountBelongVO> list = accountBelongFacade.findAllBelongByPin(param, null);
            log.info("VenderBasicRpcImpl -> getVenderAccount info,list={}", JSON.toJSONString(list));
            for (AccountBelongVO vo : list) {
                // 200表示POP店铺账号
                if (Objects.equals(vo.getBelongType(), 200)){
                    PopJmAccountBO accountBO = new PopJmAccountBO();
                    accountBO.setVenderId(vo.getBelongBizId());
                    accountBO.setAccountType(vo.getAccountType());
                    accountBO.setPin(userPin);
                    return accountBO;
                }
            }
            return null;
        }catch (Throwable e){
            log.error("VenderBasicRpcImpl -> getVenderAccount exception,查询账号信息 失败", e);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }


    /**
     *
     * @param venderId
     * @param key
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jd.health.xfyl.merchant.rpc.impl.VenderBasicRpcImpl.getVenderDynamicSubjectVo")
    public String getVenderDynamicSubjectVo(Long venderId, String key) {
        I18nParam i18nParam = new I18nParam();
        i18nParam.setBusinessUnit(302);
        Set<String> keySet = new HashSet<>();
        keySet.add(key);
        try{
            Map<String, VenderDynamicSubjectVo> result = businessQueryByVenderIdAndKeysFacade.excute(venderId,keySet,i18nParam);
            log.info("VenderBasicRpcImpl -> getVenderDynamicSubjectVo ,result={}", JSON.toJSONString(result));
            if(CollUtil.isNotEmpty(result)){
                VenderDynamicSubjectVo venderDynamicSubjectVo = result.get(key);
                return venderDynamicSubjectVo.getValue();
            }
        }catch (Exception e){
            log.error("VenderBasicRpcImpl -> getVenderDynamicSubjectVo ,Exception：",e);
        }
        return "0";
    }


    /**
     * 根据入参查询所有有效门店id列表，支持门店名称模糊搜索和子账号关联门店查询
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "VenderBasicRpcImpl.listStoreIdByAccount")
    public List<String> listStoreIdByAccount(JmListAccountStoreIdParam param) {
        try {
            List<String> result = Lists.newArrayList();
            int pageSize = 1;
            boolean hasNext = true;
            while (hasNext) {
                StoresQueryTo storesQueryTo = new StoresQueryTo();
                //POP或自营商家子账号=2002;如果是子账号放入请求参数查子账号下门店数据
                if (Objects.equals(param.getAccountType(), 2002)) {
                    storesQueryTo.setPin(param.getUserPin());
                }
                storesQueryTo.setVenderId(param.getVenderId());

                log.info("VenderBasicRpcImpl -> listStoreIdByAccount storesQueryTo={}", JsonUtil.toJSONString(storesQueryTo));
                com.jd.boundaryless.employee.center.spi.common.Paginate<StoreInfoTO> paginate = storesServiceProvider.findStoreInfoByQueryTo(storesQueryTo,pageSize , 1000);
                log.info("VenderBasicRpcImpl -> listStoreIdByAccount storeInfoByQueryTo={}", JsonUtil.toJSONString(paginate));
                List<StoreInfoTO> list = paginate.getItemList();
                if (CollectionUtil.isEmpty(list) || paginate.isLastPage()) {
                    hasNext = false;
                    continue;
                }
                List<String> storeIds = list.stream().map(e -> Objects.toString(e.getStoreId(), "")).collect(Collectors.toList());
                result.addAll(storeIds);
                pageSize++;
            }
            return result;
        }catch (Throwable e){
            log.error("VenderBasicRpcImpl -> listStoreIdByAccount Throwable", e);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 根据pin和角色类型查询商家id
     *
     * @param pin
     * @param roleType
     */
    @Override
    @LogAndAlarm(jKey = "VenderBasicRpcImpl.findVenderIdByPin")
    public Long findVenderIdByPin(String pin, String roleType) {
        UserRequest request = new UserRequest();
        request.setPin(pin);
        request.setRoleType(roleType);
        Response<UserResponse> response;
        try {
            requestPre(request);
            log.info("VenderBasicRpcImpl->findVenderIdByPin request={}", JsonUtil.toJSONString(request));
            response = userApiService.getUserInfo(request);
            log.info("VenderBasicRpcImpl->findVenderIdByPin response={}", JsonUtil.toJSONString(response));
            if (SUCCESS_CODE != response.getCode()) {
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
        }catch (com.jd.medicine.base.common.exception.BusinessException e){
            log.error("VenderBasicRpcImpl->findVenderIdByPin BusinessException", e);
            throw e;
        }catch (Throwable e){
            log.error("VenderBasicRpcImpl->findVenderIdByPin Throwable", e);
            throw new com.jdh.o2oservice.base.exception.BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        return response.getData().getVenderId();
    }

    /**
     * 填充通用字段
     * @param request
     */
    private void requestPre(BaseRequest request){
        request.setSource(SOURCE);
        String requestId = UUID.randomUUID().toString();
        request.setRequestId(requestId);
    }
}
