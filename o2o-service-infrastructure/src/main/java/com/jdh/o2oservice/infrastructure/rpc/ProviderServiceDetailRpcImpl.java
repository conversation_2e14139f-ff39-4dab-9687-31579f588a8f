package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.dto.ServiceGuaranteelDTO;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jd.health.xfyl.merchant.export.param.ServiceDetailParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ServiceDetailRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ServiceDetailBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.AppointmentRpcParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.ProviderServiceConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * ProviderServiceDetailRpcImpl
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Slf4j
@Service
public class ProviderServiceDetailRpcImpl implements ServiceDetailRpc {

    /**
     * xfylMerchantAppointApiExportService
     */
    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;

    /**
     * 查询服务详细信息
     *
     * @param param param
     * @return {@link ServiceDetailBo}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProviderServiceDetailRpcImpl.queryServiceDetail")
    public ServiceDetailBo queryServiceDetail(ServiceDetailRpcParam param) {
        try {
            ServiceDetailParam detailParam = ProviderServiceConverter.ins.convertParam(param);
            log.info("ProviderServiceDetailRpcImpl -> queryServiceDetail detailParam:{}", JSON.toJSONString(detailParam));
            JsfResult<ServiceGuaranteelDTO> result = xfylMerchantAppointApiExportService.queryServiceDetail(detailParam);
            log.info("ProviderServiceDetailRpcImpl -> queryServiceDetail result:{}", JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode())) {
                return ProviderServiceConverter.ins.dto2Bo(result.getData());
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        }catch (BusinessException be){
            throw be;
        } catch (Exception e){
            log.error("ProviderServiceDetailRpcImpl -> queryServiceDetail error",e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }

    /**
     * 更新商家端预约单核销状态
     *
     * @param appointmentRpcParam
     * @return
     */
    @Override
    @LogAndAlarm(
            jKey = "ProviderServiceDetailRpcImpl.updateAppointmentWriteOffStatus")
    public Boolean updateAppointmentWriteOffStatus(AppointmentRpcParam appointmentRpcParam) {
        AppointmentParam appointmentParam = ProviderServiceConverter.ins.convertToAppointmentParam(appointmentRpcParam);
        return JsfRpcInvokeUtil.invoke(() -> xfylMerchantAppointApiExportService.updateAppointmentWriteOffStatus(appointmentParam));
    }
}
