package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.lbs.jdlbsapi.dto.BicyclingDirectionRouteInfo;
import com.jd.lbs.jdlbsapi.dto.DrivingDirectionServiceRoutes;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.ServiceFeeDetailDTO;
import com.jdh.o2oservice.export.trade.query.AddressUpdateParam;
import com.jdh.o2oservice.export.trade.query.AppointmentTimeParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName DirectionConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 20:22
 **/
@Mapper
public interface DirectionConverter {

    DirectionConverter convertor = Mappers.getMapper(DirectionConverter.class);


    /**
     * 参数转换
     * @param param
     * @return
     */
    DirectionResultBO convertResultBO(DrivingDirectionServiceRoutes param);

    /**
     * 参数转换
     * @param param
     * @return
     */
    DirectionResultBO convertResultBO(BicyclingDirectionRouteInfo param);

    /**
     *
     * @param jdOrderDTO
     * @return
     */
    JdOrderDetailBo convertJdOrderDetailBo(JdOrderDTO jdOrderDTO);

    /**
     *
     * @param jdhSkuDto
     * @return
     */
    JdhSkuBo convertJdhSkuBo(JdhSkuDto jdhSkuDto);

    /**
     *
     * @param serviceItemDtoList
     * @return
     */
    List<ServiceItemBo> convertServiceItemBoList(List<ServiceItemDto> serviceItemDtoList);
    /**
     *
     * @param serviceFeeDetailDtoList
     * @return
     */
    List<ServiceFeeDetailBo> convertServiceFeeDetailBoList(List<ServiceFeeDetailDTO> serviceFeeDetailDtoList);

    /**
     * AddressUpdateParam AppointmentTimeParam
     * @param addressInfo
     * @return
     */
    AddressUpdateParam convertAddressUpdateParam(AddressInfoBo addressInfo);
    /**
     *
     * @param dispatchAppointmentTimeBo
     * @return
     */
    AppointmentTimeParam convertAppointmentTimeParam(DispatchAppointmentTimeBo dispatchAppointmentTimeBo);

}