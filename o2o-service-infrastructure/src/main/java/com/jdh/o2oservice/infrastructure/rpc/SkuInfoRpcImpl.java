package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.GoodsTypeDiscerner;
import com.jd.gms.component.labrador.api.domain.*;
import com.jd.gms.component.labrador.api.request.GetProductByIdParam;
import com.jd.gms.component.labrador.api.request.UpdateProductParam;
import com.jd.gms.component.labrador.api.service.read.ProductReadService;
import com.jd.gms.component.labrador.api.service.write.ProductWriteService;
import com.jd.gms.crs.enums.Language;
import com.jd.gms.crs.enums.MapType;
import com.jd.gms.crs.enums.Site;
import com.jd.gms.crs.enums.Terminal;
import com.jd.gms.crs.model.CrsResult;
import com.jd.gms.crs.model.MaterialResult;
import com.jd.gms.crs.request.JdRequestBuilder;
import com.jd.gms.crs.request.Request;
import com.jd.gms.crs.rpc.AssemblyRpc;
import com.jd.gms.crs.rpc.MapRpc;
import com.jd.gms.crs.rpc.ProductRpc;
import com.jd.gms.reader.dbconfig.DBConfigReader;
import com.jd.health.medical.examination.export.dto.SkuInfoDTO;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.jdbc.common.util.CollectionUtils;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.pap.priceinfo.sdk.domain.request.PriceInfoRequest;
import com.jd.pap.priceinfo.sdk.domain.response.PriceInfoResponse;
import com.jd.pap.priceinfo.sdk.service.PriceInfoService;
import com.jd.sdd.mkt.sdk.component.ZbWareDetailService;
import com.jd.sdd.mkt.sdk.vo.response.Result;
import com.jd.sdd.mkt.sdk.vo.response.ZbWareDetailInfo;
import com.jd.trade.guide.sdk.domain.param.BatchGuideRequest;
import com.jd.trade.guide.sdk.domain.param.ClientInfo;
import com.jd.trade.guide.sdk.domain.param.SkuAttr;
import com.jd.trade.guide.sdk.domain.param.SkuGuideParam;
import com.jd.trade.guide.sdk.domain.result.BatchResult;
import com.jd.trade.guide.sdk.domain.result.GuideMultiResultModule;
import com.jd.trade.guide.sdk.service.GuideService;
import com.jd.ugc.comment.api.CommentService;
import com.jd.ugc.comment.vo.param.CommentPageListParam;
import com.jd.ugc.qa.soa.QAOpenService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.vd.api.bean.VideoMarkResult;
import com.jd.vd.api.service.VODService;
import com.jdh.market.core.promo.coupon.query.SkuStoreSalesAndCouponBatchQuery;
import com.jdh.market.export.promo.CouponClientService;
import com.jdh.market.material.export.dto.TttMaterialStoreDTO;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.PriceFieldsEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.IpUtil;
import com.jdh.o2oservice.core.domain.product.enums.SerivceProductErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.CommentPageParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.SkuInfoRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * SKU信息RPC
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Slf4j
@Service
public class SkuInfoRpcImpl implements SkuInfoRpc {


    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=100297560
     * 有skudesign标记的商品才来取装吧样式
     */
    private static final String SKU_DESIGN11 = "<div skudesign=\"100011\">";

    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=100297560
     * 有skudesign标记的商品才来取装吧样式
     */
    private static final String SKU_DESIGN10 = "<div skudesign=\"100010\">";

    /**
     * productRpc 接口文档：<a href="https://cf.jd.com/pages/viewpage.action?pageId=163187355">...</a>
     */
    @Resource
    private ProductRpc productRpc;
    /**
     * productReadService
     */
    @Resource
    private ProductReadService productReadService;
    /**
     * priceInfoService
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=164569582
     */
    @Resource
    private PriceInfoService priceInfoService;

    /**
     * productReadService
     */
    @Resource
    private SkuInfoExportService skuInfoExportService;
    /**
     * couponClientService
     */
    @Resource
    private CouponClientService couponClientService;
    /**
     * guideService 到手价 <a href="https://cf.jd.com/pages/viewpage.action?pageId=260224910">...</a>
     */
    @Resource
    private GuideService guideService;
    /**
     * 调用方在自动部署中的应用AppId,代码自动生成,请勿手动设置否则后果自负
     */
    public static final String APP_ID = "1024440";
    /**
     * 用户标识，不同的调用者，可能传入的标识含义不一样！若有用户pin则传入用户pin，否则填应用名称
     */
    public static final String USER_AGENT = "examinationman";
    /**
     * 业务标识，业务身份
     */
    public static final String BUSINESS_IDENTITY = "EXAM_MAN";


    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=166088894
     * 商品大字段（商品详情、视频、中台规格参数）查询接口
     */
    @Resource
    private AssemblyRpc assemblyRpc;

    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=418650014
     * 获取移动端样式接口
     */
    @Resource
    private ZbWareDetailService zbWareDetailService;
    /**
     * commentService
     * 评价SDK国际化 https://cf.jd.com/pages/viewpage.action?pageId=677463362
     */
    @Resource
    private CommentService commentService;


    @Resource
    private VODService vODService;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 商品中台商品写服务
     */
    @Resource
    private ProductWriteService productWriteService;

    /**
     * 获取问答列表
     * https://joyspace.jd.com/pages/SBAXuW1vTyrJrt1bm1tq
     */
    @Resource
    private QAOpenService qaOpenService;


    @Resource
    private MapRpc mapRpc;


    /**
     * 获取SKU信息
     *
     * @param skuIds Ids
     * @return {@link Map}<{@link String}, {@link Map}<{@link String}, {@link String}>>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getSkuInfo")
    public Map<String, RpcSkuBO> getSkuInfo(Set<String> skuIds) {
        try {
            Request<String> request = getRequest(skuIds);
            log.info("SkuInfoRpcImpl -> getSkuInfo start, skuIds={}, request={}", JSON.toJSONString(skuIds), JSON.toJSONString(request));
            CrsResult<String, Map<String, String>> result = productRpc.querySku(request);
            log.info("SkuInfoRpcImpl -> getSkuInfo end, skuIds={}, request={}, result={}", JSON.toJSONString(skuIds), JSON.toJSONString(request), JSON.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                log.error("SkuInfoRpcImpl -> getSkuInfo result is null");
                return null;
            }
            Map<String, Map<String, String>> resultMap = result.getResultMap();
            Map<String, RpcSkuBO> skuInfoMap = Maps.newHashMap();
            if (Objects.nonNull(resultMap)) {
                resultMap.forEach((key, value) -> {
                    RpcSkuBO skuInfoRpcBo = SkuInfoRpcConverter.instance.convert2SkuInfoBo(value);
                    skuInfoRpcBo.setSkuId(key);
                    // 转换销售属性
                    convertSaleAttributes(skuInfoRpcBo);
                    skuInfoMap.put(key, skuInfoRpcBo);
                });
            }
            log.info("SkuInfoRpcImpl -> getSkuInfo start, skuInfoMap={}", JSON.toJSONString(skuInfoMap));
            return skuInfoMap;
        } catch (Throwable e) {
            log.error("SkuInfoRpcImpl -> getSkuInfo exception, skuIds={}", JSON.toJSONString(skuIds),e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }


    /**
     * 转换销售属性
     * @param skuInfoRpcBo
     */
    private void convertSaleAttributes(RpcSkuBO skuInfoRpcBo) {
        try {
            // [{\"dim\":1,\"saleName\":\"类型\",\"saleValue\":\"test3\",\"sequenceNo\":1},{\"dim\":2,\"saleName\":\"区域\",\"saleValue\":\"广东\",\"sequenceNo\":1}]
            String saleAttributes = skuInfoRpcBo.getSaleAttributes();
            if (StringUtils.isNotBlank(saleAttributes)){
                JSONArray saleAttributesArr = JSON.parseArray(saleAttributes);
                for (Object o : saleAttributesArr) {
                    JSONObject obj = JSON.parseObject(JSON.toJSONString(o));
                    if (StringUtils.isNotBlank(obj.getString("saleName")) && obj.getString("saleName").equals("类型")) {
                        // sku简称
                        skuInfoRpcBo.setSkuAbbreviation(obj.getString("saleValue"));
                        // sku组套内排序
                        skuInfoRpcBo.setSkuGroupInnerOrder(obj.getInteger("sequenceNo"));
                        break;
                    } else if (Objects.nonNull(obj.getInteger("dim")) && obj.getInteger("dim").equals(NumConstant.NUM_1)) {
                        skuInfoRpcBo.setSkuAbbreviation(obj.getString("saleValue"));
                        skuInfoRpcBo.setSkuGroupInnerOrder(obj.getInteger("sequenceNo"));
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("SkuInfoRpcImpl -> getSkuInfo convertSaleAttributes error skuId={}, e", skuInfoRpcBo.getSkuId(), e);
        }
    }

    /**
     * 通过sku id获取crs sku bo
     *
     * @param skuId SKU ID
     * @return {@link RpcSkuBO}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getCrsSkuBoBySkuId")
    public RpcSkuBO getCrsSkuBoBySkuId(String skuId) {
        try {
            return this.getSkuInfo(Sets.newHashSet(skuId)).get(skuId);
        }catch (Throwable e){
            log.error("SkuInfoRpcImpl -> getCrsSkuBoBySkuId exception, skuId={}", skuId, e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }

    /**
     * 判断sku是否是pop商品
     * https://cf.jd.com/pages/viewpage.action?pageId=244765591
     *
     * @param skuNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.judgeIsPopBySku")
    public Boolean judgeIsPopBySku(String skuNo) {
        log.info("SkuInfoRpcImpl -> judgeIsPopBySku 入参 skuNo={}",skuNo);
        // 不要创建多个该类实例
        DBConfigReader dbconfigReader = new DBConfigReader();
        GoodsTypeDiscerner goodsTypeDiscernerRemote = new GoodsTypeDiscerner(dbconfigReader);

        // 返回结果就是识别出的商品类型
        int goodsType = goodsTypeDiscernerRemote.discern(Long.parseLong(skuNo));
        log.info("SkuInfoRpcImpl -> judgeIsPopBySku skuNo={},goodsType={}",skuNo,goodsType);
        //1-9:POP
        if(goodsType >= CommonConstant.ONE && goodsType<= CommonConstant.NINE){
            //POP商品
            return Boolean.TRUE;
        }
        //20:POP虚拟商品
        if(goodsType==CommonConstant.NUMBER_TWENTY){
            //POP商品
            return Boolean.TRUE;
        }

        //自营商品或其他类型的商品
        return Boolean.FALSE;
    }

    /**
     * 商品读接口
     *
     * @param venderId
     * @param productId
     * @param fields
     * @param customPropsModes
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getProductById")
    public Product getProductById(String venderId, Long productId, Set<String> fields, Set<String> customPropsModes) {
        if (CollectionUtil.isEmpty(fields)) {
            return null;
        }
        com.jd.gms.component.labrador.api.domain.ClientInfo clientInfo = ClientInfoBuilder.newBuilder(APP_ID, USER_AGENT, BUSINESS_IDENTITY).autoData().build();
        VenderInfo venderInfo = new VenderInfo(venderId, VenderSource.JD.getLocalCode());
        //设置要查询的字段
        //Set<String> fields = Sets.newHashSet("customProps");
        //组装查询入参
        GetProductByIdParam param = new GetProductByIdParam(clientInfo, venderInfo, productId, fields, null, null);
        if (CollectionUtil.isNotEmpty(customPropsModes)) {
            param.setCustomPropsModes(customPropsModes);
        }
        com.jd.gms.component.labrador.api.response.Result<Product> result = productReadService.getProductById(param);
        log.info("SkuInfoRpcImpl -> getProductById param={}, result={}", JsonUtil.toJSONString(param), JsonUtil.toJSONString(result));
        if (result == null || !result.isSuccess()) {
            log.error("SkuInfoRpcImpl -> getProductById error end, result={}", JsonUtil.toJSONString(result));
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return result.getObj();
    }

    /**
     * @param skuNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryGoodsInfoBySkuNo")
    public SkuInfoDTO queryGoodsInfoBySkuNo(String skuNo) {
        JsfResult<SkuInfoDTO> result = skuInfoExportService.queryGoodsInfoBySkuNo(skuNo);
        if (result != null && BusinessErrorCode.SUCCESS.getCode().equals(result.getCode())) {
            return result.getData();
        } else {
            log.error("SkuInfoRpcImpl -> queryGoodsInfoBySkuNo exception,desc ={}" ,result.getMsg());
            return null;
        }
    }

    /**
     * 查询到手价
     * @param guidePriceQueryBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.batchFinalPrice")
    public Map<Long, GuideMultiResultModule> batchFinalPrice(GuidePriceQueryBo guidePriceQueryBo) {
        try {
            BatchGuideRequest request = new BatchGuideRequest();
            request.setPin(guidePriceQueryBo.getPin());
            request.setProvinceId(guidePriceQueryBo.getProvinceId());
            request.setCityId(guidePriceQueryBo.getCityId());
            request.setCountryId(guidePriceQueryBo.getCountryId());
            request.setType(2);
            request.setClientInfo(buildClientInfo());
            Map<String, SkuGuideParam> skuGuideParamMap = new HashMap<>(guidePriceQueryBo.getSkuIds().size());
            for (String skuId : guidePriceQueryBo.getSkuIds()) {
                SkuGuideParam skuGuideParam = new SkuGuideParam();
                SkuAttr skuAttr = new SkuAttr();
                skuAttr.setSkuId(Long.parseLong(skuId));
                skuGuideParam.setSkuAttr(skuAttr);

                Map<String, List<Long>> skuCouponBatchIdMap = guidePriceQueryBo.getSkuCouponBatchIdMap();
                if (MapUtils.isNotEmpty(skuCouponBatchIdMap) && CollectionUtils.isNotEmpty(skuCouponBatchIdMap.get(skuId))){
                    Map<String, String> extMap = new HashMap<>();
                    extMap.put("batchIds", JSON.toJSONString(skuCouponBatchIdMap.get(skuId)));
                    skuGuideParam.setExtMap(extMap);
                }
                skuGuideParamMap.put(skuId,skuGuideParam);
            }
            request.setSkuGuideParamMap(skuGuideParamMap);
            log.info("SkuInfoRpcImpl -> batchFinalPrice start, request={}",JsonUtil.toJSONString(request));
            BatchResult batchResult = guideService.batchFinalPrice(request);
            log.info("SkuInfoRpcImpl -> batchFinalPrice end, request={}, batchResult={}", JSON.toJSONString(request), JsonUtil.toJSONString(batchResult));
            if(Objects.isNull(batchResult) || !batchResult.isSuccess()){
                log.error("SkuInfoRpcImpl -> batchFinalPrice result is null");
                return null;
            }
            return batchResult.getGuideMultiResultMap();
        }catch (Throwable e){
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.batchFinalPrice", e.getMessage());
            log.error("SkuInfoRpcImpl -> batchFinalPrice exception, param={}", JsonUtil.toJSONString(guidePriceQueryBo),e);
            return null;
        }
    }

    /**
     * 查询商品销量，优惠券
     *
     * @param skuStoreSalesAndCouponBatchQuery
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getSkuSalesAndCoupon")
    public List<TttMaterialStoreDTO> getSkuSalesAndCoupon(SkuStoreSalesAndCouponBatchQuery skuStoreSalesAndCouponBatchQuery) {
        try {
            JsfResult<List<TttMaterialStoreDTO>> batchResult = couponClientService.getSkuSalesAndCoupon(skuStoreSalesAndCouponBatchQuery);
            log.info("SkuInfoRpcImpl -> getSkuSalesAndCoupon end, batchResult={}",JsonUtil.toJSONString(batchResult));
            if(Objects.isNull(batchResult) || !batchResult.isSuccess()){
                log.error("SkuInfoRpcImpl -> getSkuSalesAndCoupon result is null");
                return null;
            }
            return batchResult.getData();
        }catch (Throwable e){
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getSkuSalesAndCoupon", e.getMessage());
            log.error("SkuInfoRpcImpl -> getSkuSalesAndCoupon exception, param={}", JsonUtil.toJSONString(skuStoreSalesAndCouponBatchQuery),e);
            return null;
        }
    }

    /**
     * 商品京东价
     * @param skuIds
     * @param pin
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getSkuPriceByPin")
    public Map<String, PriceResultBO> getSkuPriceByPin(Set<String> skuIds, String pin) {
        PriceInfoResponseBO priceInfoResponse = this.getSkuPriceAndPromotionByPin(skuIds, pin);
        if(priceInfoResponse == null){
            return Collections.emptyMap();
        }
        return priceInfoResponse.getPriceMap();
    }

    /**
     * <pre>
     * 注意商品中台限制了最大查询数量30
     * </pre>
     * @param skuIds
     * @param pin
     * @return
     */
    @Override
    @LogAndUmp
    public PriceInfoResponseBO getSkuPriceAndPromotionByPin(Set<String> skuIds, String pin) {
        return getSkuPriceAndPromotionByPin(skuIds, pin, null);
    }

    @Override
    @LogAndAlarm(jKey = "SkuInfoRpcImpl.getSkuPriceAndPromotionByPin")
    public PriceInfoResponseBO getSkuPriceAndPromotionByPin(Set<String> skuIds, String pin, AddressDetailBO addressDetail) {
        if (CollUtil.isEmpty(skuIds)) {
            return null;
        }
        if (skuIds.size() <= CommonConstant.NUMBER_THIRTY) {
            return getSkuPrice(skuIds, pin, addressDetail);
        }
        List<List<String>> subs = ListUtils.partition(new ArrayList<>(skuIds), CommonConstant.NUMBER_THIRTY);
        List<CompletableFuture<PriceInfoResponseBO>> futures = new ArrayList<>();
        for (List<String> sub : subs) {
            futures.add(CompletableFuture.supplyAsync(()-> getSkuPrice(new HashSet<>(sub), pin, addressDetail), executorPoolFactory.get(ThreadPoolConfigEnum.SKU_PRICE_C_HAND_POOL)));
        }
        try {
            PriceInfoResponseBO multiRet = new PriceInfoResponseBO();
            multiRet.setPriceMap(new HashMap<>(1));
            multiRet.setFlagResponse(new HashMap<>(1));
            multiRet.setInfoResponse(new HashMap<>(1));
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(3, TimeUnit.SECONDS);
            for (CompletableFuture<PriceInfoResponseBO> future : futures) {
                PriceInfoResponseBO priceInfoResponseBO = future.get();
                if (priceInfoResponseBO == null) {
                    continue;
                }
                if (CollUtil.isNotEmpty(priceInfoResponseBO.getPriceMap())) {
                    multiRet.getPriceMap().putAll(priceInfoResponseBO.getPriceMap());
                }
                if (CollUtil.isNotEmpty(priceInfoResponseBO.getFlagResponse())) {
                    multiRet.getFlagResponse().putAll(priceInfoResponseBO.getFlagResponse());
                }
                if (CollUtil.isNotEmpty(priceInfoResponseBO.getInfoResponse())) {
                    multiRet.getInfoResponse().putAll(priceInfoResponseBO.getInfoResponse());
                }
            }
            log.info("SkuInfoRpcImpl getSkuPriceAndPromotionByPin multiRet={}", JSON.toJSONString(multiRet));
            return multiRet;
        } catch (InterruptedException e) {
            log.error("SkuInfoRpcImpl -> getSkuPriceAndPromotionByPinAddress InterruptedException, skuIds={}", JsonUtil.toJSONString(skuIds), e);
        } catch (ExecutionException e) {
            log.error("SkuInfoRpcImpl -> getSkuPriceAndPromotionByPinAddress ExecutionException, skuIds={}", JsonUtil.toJSONString(skuIds), e);
        } catch (TimeoutException e) {
            log.error("SkuInfoRpcImpl -> getSkuPriceAndPromotionByPinAddress TimeoutException, skuIds={}", JsonUtil.toJSONString(skuIds), e);
        }
        return null;
    }

    /**
     * 查询商品详情
     *
     * @param skuNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryProductBigFieldBySku")
    public SkuInfoAndStyleBO queryProductBigFieldBySku(String skuNo) {
        Set<String> skuIds = Sets.newHashSet(skuNo);
        Request<String> request = getRequest(skuIds);
        SkuInfoAndStyleBO dto = new SkuInfoAndStyleBO();
        try {


            // <!-- 关键点：通过传入 imageRectanglized 来识别是否走新长主图逻辑  -->
            request.setExtension(ImmutableMap.of("tenantId", "1024"));
//            Set<String> requestFields = Sets.newHashSet("pcWdis", "wReadMe","wareQD","mWdis","afterSaleDesc");
            //mWdis 移动端商品介绍
            String mWdis = "mWdis";
            log.info("SkuInfoRpcImpl -> queryProductBigFieldBySku start, skuIds={}, request={}", JsonUtil.toJSONString(skuIds), JsonUtil.toJSONString(request));
            CrsResult<String, Map<String, String>> result = assemblyRpc.queryProductBigFieldBySku(request);
            log.info("SkuInfoRpcImpl -> queryProductBigFieldBySku end, result={}", JsonUtil.toJSONString(result));
            // 从result里取值时，需要先判断 result.isSuccess (如果不判断isSuccess，接口异常时result.getValue(poductId)取出来的null，
            // 和接口正常时result.getValue(productId)取出来的null，两者之间是有歧义)
            if (result != null && result.isSuccess()) {
                Map<String, String> valueMap = result.getValue(skuNo);
                if (!CollectionUtils.isEmpty(valueMap)) {
                    String value = valueMap.get(mWdis);
                    log.info("SkuInfoRpcImpl -> queryProductBigFieldBySku key={}, value={}", mWdis, value);
                    if (StringUtils.isEmpty(value)) {
                        //获取商品大字段信息失败
                        throw new BusinessException(SerivceProductErrorCode.QUERY_SKU_BIG_FIELD_IS_ERROR);
                    }
                    dto.setImageAndText(value);
                    //获取样式
                    if (value.contains(SKU_DESIGN11) || value.contains(SKU_DESIGN10)) {
                        String cssContent = buildSkuStyleBySkuNo(skuNo);
                        if (StringUtils.isNotEmpty(cssContent)) {
                            log.info("SkuInfoRpcImpl -> queryProductBigFieldBySku CssContent={}", cssContent);
                            dto.setCssContent(cssContent);
                        }
                    }
                }
            }
        } catch (Throwable e) {
            Profiler.businessAlarm("com.jd.health.medical.examination.rpc.sku.SkuInfoRpcImpl.queryProductBigFieldBySku", e.getMessage());
            log.error("SkuInfoRpcImpl -> queryProductBigFieldBySku exception, skuIds={},errMessage={}", JsonUtil.toJSONString(skuIds), e.getMessage());
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return dto;
    }

    /**
     * 组装商品样式
     *
     * @param skuNo
     * @return
     */
    private String buildSkuStyleBySkuNo(String skuNo) {
        log.info("SkuInfoRpcImpl -> buildSkuStyleBySkuNo skuNo={},", skuNo);
        //先判断商品是不是 POP
        Boolean flag = judgeIsPopBySku(skuNo);
        log.info("SkuInfoRpcImpl -> buildSkuStyleBySkuNo skuNo={},flag={}", skuNo, flag);
        if (flag) {
            //POP商品 取spu取查样式
            RpcSkuBO crsSkuBO = getCrsSkuBoBySkuId(skuNo);
            if (Objects.isNull(crsSkuBO)) {
                throw new SystemException(BusinessErrorCode.QUERY_SKU_INFO_IS_ERROR);
            }
            log.info("SkuInfoRpcImpl -> buildSkuStyleBySkuNo skuNo={},SpuId={}", skuNo, crsSkuBO.getProductId());
            //pop商品传spu
            skuNo = crsSkuBO.getProductId();
        }
        ProductStyleAndJsBO styleAndJsBO = queryProductStyleAndJsBySku(skuNo);
        log.info("SkuInfoRpcImpl -> queryProductBigFieldBySku CssContent={}", JSON.toJSONString(styleAndJsBO.getCssContent()));
        return styleAndJsBO.getCssContent();
    }

    /**
     * 查询商品装吧返回的商品样式
     *
     * @param skuNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryProductStyleAndJsBySku")
    public ProductStyleAndJsBO queryProductStyleAndJsBySku(String skuNo) {
        try {
            log.info("SkuInfoRpcImpl -> getMobileWareStyleAndJsByWareId start, skuNo={}", skuNo);
            Result<ZbWareDetailInfo> result = zbWareDetailService.getMobileWareStyleAndJsByWareId(Long.valueOf(skuNo));
            log.info("SkuInfoRpcImpl -> getMobileWareStyleAndJsByWareId end, result={}", JsonUtil.toJSONString(result));
            if (result == null || !result.getSuccess() || result.getData() == null) {
                log.error("SkuInfoRpcImpl -> getMobileWareStyleAndJsByWareId exception, skuNo={}, errMessage={}", skuNo, result.getMsg());
                throw new BusinessException(SerivceProductErrorCode.QUERY_SKU_STYLE_IS_ERROR);
            }
            ZbWareDetailInfo zbWareDetailInfo = result.getData();
            ProductStyleAndJsBO bo = new ProductStyleAndJsBO();
            bo.setCssContent(zbWareDetailInfo.getCssContent());
            bo.setSkuNo(String.valueOf(zbWareDetailInfo.getWareId()));
            return bo;
        } catch (Exception e) {
            log.error("SkuInfoRpcImpl->getMobileWareStyleAndJsByWareId error", e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }

    /**
     * 查询商品图片
     *
     * @param skuNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryMaterialForAssembly")
    public SkuMaterialBO queryMaterialForAssembly(String skuNo) {
        HashSet<String> skuIds = Sets.newHashSet(skuNo);
        Request<String> request = getRequest(skuIds);

        try {
            // <!-- 关键点：通过传入 imageRectanglized 来识别是否走新长主图逻辑  -->
            request.setExtension(ImmutableMap.of("imageRectanglized", "true"));
            log.info("SkuInfoRpcImpl -> queryMaterialForAssembly start, skuIds={}, request={}", JsonUtil.toJSONString(skuIds), JsonUtil.toJSONString(request));
            CrsResult<String, MaterialResult> result = assemblyRpc.queryMaterialForAssembly(request);
            log.info("SkuInfoRpcImpl -> queryMaterialForAssembly end, result={}", JsonUtil.toJSONString(result));
            // 从result里取值时，需要先判断 result.isSuccess (如果不判断isSuccess，接口异常时result.getValue(poductId)取出来的null，
            // 和接口正常时result.getValue(productId)取出来的null，两者之间是有歧义)
            if (result != null && result.isSuccess()) {
                for (String skuId : skuIds) {
                    MaterialResult materialResult = result.getValue(skuId);
                    if (materialResult != null) {
                        String jsonString = JsonUtil.toJSONString(materialResult);
                        return JsonUtil.parseObject(jsonString, SkuMaterialBO.class);
                    }
                }
            }
            return null;
        } catch (Throwable e) {
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryMaterialForAssembly", e.getMessage());
            log.error("SkuInfoRpcImpl -> queryMaterialForAssembly exception, skuIds={}", JsonUtil.toJSONString(skuIds), e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }



    /**
     * 按被评价对象查看列表
     * https://joyspace.jd.com/pages/DB1rsMoXU6qIWxSb4oKd
     * @param commentPageParam
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getCommentPageList")
    public String getCommentPageList(CommentPageParam commentPageParam) {
        String skuNo = commentPageParam.getSkuNo();
        Integer score = commentPageParam.getScore();
        Integer sortType = commentPageParam.getSortType();
        Integer page = commentPageParam.getPageNum();
        Integer pageSize = commentPageParam.getPageSize();
        Map ext = new HashMap();
//        ext.put("rid",commentPageBO.getLabelId());
        CommentPageListParam commentPageListParam = new CommentPageListParam();
        commentPageListParam.setSite(getSite());
        commentPageListParam.setObjectId(Long.parseLong(skuNo));
        if (null == score || score == CommonConstant.ZERO) {
            score = CommonConstant.ZERO;
            ext.put("filterLowEval", true);
            ext.put("uuid",UUID.randomUUID().toString());
        }
        commentPageListParam.setAggr(0);
        commentPageListParam.setFuzzy(1);
        commentPageListParam.setScore(score);
        commentPageListParam.setSortType(sortType);
        commentPageListParam.setPage(page);
        commentPageListParam.setPageSize(pageSize);
        commentPageListParam.setExt(ext);

        String result = commentService.getCommentPageList(JSON.toJSONString(commentPageListParam));
        log.info("SkuInfoRpcImpl getCommentPageList commentPageListParam={}, result={}", JSON.toJSONString(commentPageListParam), result);
        if (StringUtil.isNotBlank(result)) {
            CommentUgcResultBO commentUgcResult = JSON.parseObject(result, CommentUgcResultBO.class);
            if (Objects.isNull(commentUgcResult)) {
                return null;
            }
            if (commentUgcResult.getSuccess()) {
                log.info("SkuInfoRpcImpl -> getCommentPageList end,按被评价对象查看列表page={},skuNo={}", page, skuNo);
                return commentUgcResult.getResult();
            } else {
                log.error("SkuInfoRpcImpl->getCommentPageList error code={}, msg={}", commentUgcResult.getCode(), commentUgcResult.getResultCode());
                throw new SystemException(BusinessErrorCode.CUSTOM_ERROR_CODE.formatDescription(commentUgcResult.getCode(), commentUgcResult.getResultCode()));

            }
        }
        return null;
    }
    private com.jd.ugc.comment.vo.Site getSite(){
        com.jd.ugc.comment.vo.Site site = new com.jd.ugc.comment.vo.Site();
        site.setBuid(CommonConstant.BU_ID);
        site.setClientCode("CN");
        site.setLang("zh");
        return site;
    }
    /**
     * 组装商品信息查询入参
     *
     * @param skuIds Ids
     * @return {@link Request}<{@link String}>
     */
    private Request<String> getRequest(Set<String> skuIds) {
        Request<String> request = new Request<>();
        request.setSkuIds(skuIds);
        request.setSite(Site.JD_COM);
        request.setLanguage(Language.ZH);
        request.setMainLanguage(Language.ZH);
        request.setTerminal(Terminal.PC);
        request.setSystemKey("health_physicalexamination");
        return request;
    }

    /**
     * 构建客户端信息
     *
     * @return {@link ClientInfo}
     */
    private ClientInfo buildClientInfo() throws Exception {
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setAppName("jdos_physicalexamination");
        clientInfo.setSource(1);
        clientInfo.setUserIP(IpUtil.getInet4Address());
        clientInfo.setClientIP(IpUtil.getInet4Address());
        clientInfo.setBusinessId(10111);
        clientInfo.setSiteId("cn_retail_jd");
        return clientInfo;
    }


    /**
     * @param skuIds
     * @return
     */
    private PriceInfoRequest getPriceInfoRequest(Set<String> skuIds, String pin) {
        return getPriceInfoRequest(skuIds, pin, null);
    }

    /**
     * @param skuIds
     * @return
     */
    public PriceInfoRequest getPriceInfoRequest(Set<String> skuIds, String pin, AddressDetailBO addressDetail) {
        PriceInfoRequest priceInfoRequest = new PriceInfoRequest();
        Set<Integer> priceInfos = new HashSet<>();
        // 获取实时价格
        priceInfos.add(1);
        priceInfos.add(2);
        priceInfoRequest.setPriceInfos(priceInfos);
        Set<Integer> priceFields = new HashSet<>();
        priceFields.addAll(PriceFieldsEnum.getAllCode());
        priceInfoRequest.setPriceFields(priceFields);
        priceInfoRequest.setSkuIds(skuIds);
        priceInfoRequest.setSite(301);
        priceInfoRequest.setChannel(2);
        priceInfoRequest.setPin(pin);
        priceInfoRequest.setSource("physicalexamination");
        try {
            if (Objects.nonNull(addressDetail)){
                StringBuilder builder = new StringBuilder();
                if (addressDetail.getProvinceId() != null){
                    builder.append(addressDetail.getProvinceId()+"-");
                }
                if (addressDetail.getCityId() != null){
                    builder.append(addressDetail.getCityId()+"-");
                }
                if (addressDetail.getCountyId() != null){
                    builder.append(addressDetail.getCountyId()+"-");
                }
                if (addressDetail.getTownId() != null){
                    builder.append(addressDetail.getTownId()+"-");
                }
                //  1(省id)-2(市id)-3(区id)-4(镇id)
                String area = builder.substring(0, builder.length() - 1);
                priceInfoRequest.setArea(area);
            }
        } catch (Exception e) {
            log.error("SkuInfoRpcImpl getPriceInfoRequest address error e", e);
        }
        log.info("SkuInfoRpcImpl getPriceInfoRequest priceInfoRequest={}", JSON.toJSONString(priceInfoRequest));
        return priceInfoRequest;
    }

    /**
     * <pre>
     * 注意商品中台限制了最大查询数量30
     * </pre>
     * @param skuIds
     * @param pin
     * @return
     */
    public PriceInfoResponseBO getSkuPrice(Set<String> skuIds, String pin) {
        return getSkuPrice(skuIds, pin, null);
    }

    /**
     * <pre>
     * 注意商品中台限制了最大查询数量30
     * </pre>
     * @param skuIds
     * @param pin
     * @return
     */
    public PriceInfoResponseBO getSkuPrice(Set<String> skuIds, String pin, AddressDetailBO addressDetail) {
        try {
            if (CollUtil.isEmpty(skuIds)) {
                return null;
            }
            PriceInfoRequest priceInfoRequest = getPriceInfoRequest(skuIds, pin, addressDetail);
            PriceInfoResponse result = priceInfoService.getRealPriceInfo(priceInfoRequest);
            log.info("SkuInfoRpcImpl -> getSkuPriceAndPromotionByPin end, result={}",JsonUtil.toJSONString(result));
            if (result == null || !result.isSuccess()) {
                return null;
            }
            PriceInfoResponseBO priceInfoResponseBO = SkuInfoRpcConverter.instance.convert2SkuInfoBo(result);
            log.info("SkuInfoRpcImpl getSkuPrice priceInfoResponseBO={}", JSON.toJSONString(priceInfoResponseBO));
            // 处理双价格
            if(priceInfoResponseBO != null && priceInfoResponseBO.getPriceMap() != null){
                priceInfoResponseBO.getPriceMap().forEach((k,v) -> {
                    // 含有双价格的商品
                    if(v != null && v.getFieldMap() != null && v.getFieldMap().size() > 0){
                        log.info("SkuInfoRpcImpl getSkuPriceAndPromotionByPin fieldMap={}", JSON.toJSONString(v.getFieldMap()));
                        v.getFieldMap().forEach((k1,v1) -> {
                            if(PriceFieldsEnum.fromCode(k1) != null){
                                v.setOriginalPrice(v.getJdPrice());
                                v.setJdPrice(v1);
                            }
                        });
                    }
                });
            }
            return priceInfoResponseBO;
        } catch (Throwable e) {
            log.error("SkuInfoRpcImpl -> getSkuPriceAndPromotionByPin exception, skuIds={},errMessage={}", JsonUtil.toJSONString(skuIds), e.getMessage());
            return null;
        }
    }

    /**
     * 获取问答列表-分页
     * @param pageQuestionParam
     * @return
     */
    @Override
    public String getPageQuestionList(String pageQuestionParam) {
        try {
            log.info("SkuInfoRpcImpl.getPageQuestionList.pageQuestionParam={}",pageQuestionParam);
            String result = qaOpenService.getPageQuestionList(pageQuestionParam);
            log.info("SkuInfoRpcImpl.getPageQuestionList.result={}",result);
            return result;
        }catch (Exception e){
            log.error("SkuInfoRpcImpl.getPageQuestionList has error",e);
        }
        return null;
    }

    /**
     * 子集查兄弟集合（子查兄弟）
     * https://joyspace.jd.com/pages/UFh6lhn8zWRiBtzpFJhM
     * @param skuIds
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.queryMapInfo")
    public Map<String, String> queryMapInfo(Set<String> skuIds) {
        try {
            Request<String> request = JdRequestBuilder
                    .newBuilder("health_jdh-o2o-service")
                    .skuIds(skuIds)
                    .site(Site.JD_COM)
                    .language(Language.ZH)
                    .terminal(Terminal.PC)
                    .addExtension("MAPPING_TYPE", MapType.COLOR_SIZE)
                    .build();
            CrsResult<String, String> result = mapRpc.queryMapInfo(request);
            log.info("SkuInfoRpcImpl -> queryMapInfo request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(result));
            if (result != null && result.isSuccess()) {
                return result.getResultMap();
            }
        } catch (Exception e) {
            log.error("SkuInfoRpcImpl -> queryMapInfo error e", e);
        }
        return Collections.emptyMap();
    }
    /**
     * 获取视频播放信息
     */
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.getVideoPlayInfoByTraffic", errorReturnJsfResult = false)
    @Override
    public VideoMarkResultBO getVideoPlayInfoByTraffic(String videoId) {
        try {
            Map<String, Object> paramMap=new HashMap<String, Object>(){{
                put("vid",videoId);
                put("type","1");
                put("sharpness","high");
            }};
            log.info("SkuInfoRpcImpl.getVideoPlayInfoByTraffic.paramMap={}",JsonUtil.toJSONString(paramMap));
            VideoMarkResult videoPlayInfoByTraffic = vODService.getVideoPlayInfoByTraffic(paramMap);
            log.info("SkuInfoRpcImpl.getVideoPlayInfoByTraffic.videoPlayInfoByTraffic={}",JsonUtil.toJSONString(videoPlayInfoByTraffic));
            return JsonUtil.parseObject(JsonUtil.toJSONString(videoPlayInfoByTraffic),VideoMarkResultBO.class);
        }catch (Exception e){
            log.error("SkuInfoRpcImpl.getVideoPlayInfoByTraffic has error",e);
        }
        return null;
    }


    /**
     * 更新商品定制类目属性
     *
     * @param venderId
     * @param productId
     * @param customProps
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.SkuInfoRpcImpl.updateProduct", errorReturnJsfResult = false)
    public Boolean updateProduct(String venderId, Long productId, Set<CustomProp> customProps) {
        if (com.jd.medicine.base.common.util.CollectionUtil.isEmpty(customProps) || Objects.isNull(productId)) {
            return false;
        }
        com.jd.gms.component.labrador.api.domain.ClientInfo clientInfo = ClientInfoBuilder.newBuilder(APP_ID, USER_AGENT, BUSINESS_IDENTITY).autoData().build();
        VenderInfo venderInfo = new VenderInfo(venderId, VenderSource.JD.getLocalCode());
        //组装查询入参
        Product product = new Product();
        product.setProductId(productId);
        product.setCustomProps(customProps);
        UpdateProductParam param = new UpdateProductParam(clientInfo, venderInfo, product);
        com.jd.gms.component.labrador.api.response.Result result = productWriteService.updateProduct(param);
        log.info("SkuInfoRpcImpl -> updateProduct param={}, result={}", JsonUtil.toJSONString(param), JsonUtil.toJSONString(result));
        if (result == null || !result.isSuccess()) {
            throw new com.jd.medicine.base.common.exception.BusinessException(com.jd.medicine.base.common.exception.BusinessErrorCode.RPC_CALL_ERROR);
        }
        return result.isSuccess();
    }
}
