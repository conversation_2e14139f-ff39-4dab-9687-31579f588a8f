package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.pap.priceinfo.sdk.domain.response.PriceInfoResponse;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.PriceInfoResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Map;

@Mapper
@DecoratedWith(SkuInfoRpcConverterDecorator.class)
public interface SkuInfoRpcConverter {

    SkuInfoRpcConverter instance = Mappers.getMapper(SkuInfoRpcConverter.class);

    /**
     * 转换为sku信息
     *
     * @param skuInfo SKU
     * @return {@link SkuInfoRpcBo}
     */
    RpcSkuBO convert2SkuInfoBo(Map<String, String> skuInfo);
    PriceInfoResponseBO convert2SkuInfoBo(PriceInfoResponse priceInfoResponse);


}
