package com.jdh.o2oservice.infrastructure.rpc.provider;

import com.jd.health.xfyl.merchant.export.dto.AppointResultDTO;
import com.jd.health.xfyl.merchant.export.dto.ServiceGuaranteelDTO;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jd.jsf.gd.error.RpcException;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.exception.BusinessErrorCode;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.provider.bo.PromiseProviderCallbackParam;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderAppointRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.CancelAppointRpcParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.ModifyAppointRpcParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.PopSubmitAppointRpcParam;
import com.jdh.o2oservice.export.promise.cmd.PromiseCallbackCmd;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ServiceDetailBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.infrastructure.rpc.JsfRpcInvokeUtil;
import com.jdh.o2oservice.infrastructure.rpc.convert.ProviderServiceConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 调用商家端预约 RPC实现
 * @author: yangxiyu
 * @date: 2024/7/19 14:05
 * @version: 1.0
 */
@Component
@Slf4j
public class ProviderAppointRpcImpl implements ProviderAppointRpc {

    /**
     * xfylMerchantAppointApiExportService
     */
    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseExtApplication promiseApplication;

    /**
     * 预约
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public AppointResultDTO appointment(PopSubmitAppointRpcParam param) throws RpcException {

        AppointmentParam appointmentParam = new AppointmentParam();
        BeanUtils.copyProperties(param, appointmentParam);
        return JsfRpcInvokeUtil.invoke(() -> xfylMerchantAppointApiExportService.appointment(appointmentParam));
    }

    @Override
    @LogAndAlarm
    public AppointResultDTO modifyAppointment(ModifyAppointRpcParam param) {
        AppointmentParam appointmentParam = new AppointmentParam();
        BeanUtils.copyProperties(param, appointmentParam);
        return JsfRpcInvokeUtil.invoke(() -> xfylMerchantAppointApiExportService.modifyAppointment(appointmentParam));
    }

    @Override
    @LogAndAlarm
    public AppointResultDTO cancelAppointment(CancelAppointRpcParam param) {
        AppointmentParam appointmentParam = new AppointmentParam();
        BeanUtils.copyProperties(param, appointmentParam);
        return JsfRpcInvokeUtil.invoke(() -> xfylMerchantAppointApiExportService.cancelAppointment(appointmentParam));
    }

    /**
     * 商家履约单回调
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean providerPromiseCallback(PromiseProviderCallbackParam cmd) {
        PromiseCallbackCmd promiseCallbackCmd = ProviderServiceConverter.ins.convertPromiseCallbackCmd(cmd);
        return promiseApplication.providerPromiseCallback(promiseCallbackCmd);
    }

    @Override
    @LogAndAlarm
    public ServiceDetailBo queryServiceDetail(ServiceDetailRpcParam param) {
        log.info("ProviderAppointRpcImpl->queryServiceDetail start, thirdStoreParam={}", JsonUtil.toJSONString(param));
        com.jd.health.xfyl.merchant.export.param.ServiceDetailParam rpcParam = new com.jd.health.xfyl.merchant.export.param.ServiceDetailParam();
        rpcParam.setSkuNo(param.getSkuNo());
        JsfResult<ServiceGuaranteelDTO> result = xfylMerchantAppointApiExportService.queryServiceDetail(rpcParam);
        log.info("ProviderAppointRpcImpl->queryServiceDetail end, result={}", result);
        if (null == result) {
            throw new BusinessException(BusinessErrorCode.RPC_CALL_ERROR, "返回数据为空");
        }
        ServiceGuaranteelDTO data = result.getData();
        if (Objects.nonNull(data)) {
            ServiceDetailBo dto = new ServiceDetailBo();
            BeanUtils.copyProperties(data, dto);
            return dto;
        } else {
            log.error("ProviderAppointRpcImpl->queryServiceDetail exception, result desc ={}" ,result.getMsg());
            throw new BusinessException(BusinessErrorCode.RPC_CALL_ERROR, result.getMsg());
        }
    }
}
