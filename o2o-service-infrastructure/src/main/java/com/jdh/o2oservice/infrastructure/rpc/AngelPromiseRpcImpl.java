package com.jdh.o2oservice.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angelpromise.AngelPromiseQueryApplication;
import com.jdh.o2oservice.core.domain.promise.rpc.AngelPromiseRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.AngelPromiseBO;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailQuery;
import com.jdh.o2oservice.infrastructure.rpc.convert.AngelPromiseRpcConvert;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 服务者 工单查询
 * @author: yangxiyu
 * @date: 2024/5/14 5:05 下午
 * @version: 1.0
 */
@Component
public class AngelPromiseRpcImpl implements AngelPromiseRpc {

    /** */
    @Resource
    private AngelPromiseQueryApplication angelPromiseQueryApplication;


    /**
     * 获取服务者工单
     * @param promiseId
     * @return
     */
    @Override
    public AngelPromiseBO getAngelPromise(Long promiseId) {
        AngelWorkDetailQuery query = new AngelWorkDetailQuery();
        query.setPromiseId(promiseId);
        query.setNotInWorkStatusList(Lists.newArrayList(8,9));
        AngelWorkDto workDto = angelPromiseQueryApplication.querySimpleAngelWork(query);
        return AngelPromiseRpcConvert.INS.convert2Bo(workDto);
    }
}
