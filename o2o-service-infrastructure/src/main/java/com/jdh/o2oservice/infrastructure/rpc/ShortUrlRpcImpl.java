package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.shorturl.api.jsf.ShortUrlService;
import com.jd.shorturl.model.UrlInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ShortUrlRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;

/**
 * 短链生成服务
 * <AUTHOR>
 * @date 2024-01-04-6:18 下午
 */
@Service
@Slf4j
public class ShortUrlRpcImpl implements ShortUrlRpc {
    /** 短链前后加空格 */
    private static final String SHORT_SUFFIX = " {0} ";
    /**
     * shortUrlService
     */
    @Resource
    private ShortUrlService shortUrlService;
    /**
     * shortUrlServiceDomain
     */
    @Value("${shortUrlService.domain}")
    private String shortUrlServiceDomain;
    /**
     * shortUrlServiceLength
     */
    @Value("${shortUrlService.length}")
    private Integer shortUrlServiceLength;
    /**
     * shortUrlServiceKey
     */
    @Value("${shortUrlService.key}")
    private String shortUrlServiceKey;
    /**
     * GENERATE_URL_SUCCESS_CODE
     */
    private static final String GENERATE_URL_SUCCESS_CODE = "200";

    @Override
    public String generateShortUrl(String url) {
        log.info("ShortUrlRpcImpl -> generateShortUrl start, url={}", url);

        try {
            String result = "";
            UrlInfo shortUrl = shortUrlService.generateURLFastest(shortUrlServiceDomain, shortUrlServiceLength, url, shortUrlServiceKey, 0);
            if (null != shortUrl && GENERATE_URL_SUCCESS_CODE.equals(shortUrl.getCode())) {
                result = shortUrl.getShortUrl();
            }else {
                log.info("ShortUrlRpcImpl->generateShortUrl----errorCode={},errorMessage={}", shortUrl.getCode(), shortUrl.getCodeText());
                throw new BusinessException(new DynamicErrorCode(shortUrl.getCode(), shortUrl.getCodeText()));
            }
            log.info("ShortUrlRpcImpl -> generateShortUrl----end,result={}", shortUrl);
            result = " " + result + " ";
            return result;
        }catch(BusinessException be){
            log.error("ShortUrlRpcImpl -> generateShortUrl exception,errorCode={},message ={}",be.getErrorCode(),be.getMessage());
            throw be;
        } catch (Throwable e) {
            log.error("ShortUrlRpcImpl->generateShortUrl----Exception,errMessage={}", e.getMessage());
            return null;
        }
    }


    /**
     * 唤醒京东APP的短链
     * @param exportUrl
     * @return
     */
    @Override
    @LogAndAlarm
    public String rouseAppShortUrl(String exportUrl) {
        UrlInfo shortUrl;
        try {
            shortUrl = shortUrlService.generateURLFastestWithPath(shortUrlServiceDomain, "v", shortUrlServiceLength, exportUrl, shortUrlServiceKey, 0);
        }catch (Throwable e){
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.rpc.ShortUrlRpcImpl.rouseAppShortUrl", e.getMessage());
            log.error("ShortUrlRpcImpl->rouseAppShortUrl----Exception,errMessage={}", e.getMessage());
            return null;
        }

        if (null != shortUrl && GENERATE_URL_SUCCESS_CODE.equals(shortUrl.getCode())) {
            log.info("ShortUrlRpcImpl -> rouseAppShortUrl----end,result={}", shortUrl.getShortUrl());
            return MessageFormat.format(SHORT_SUFFIX, shortUrl.getShortUrl());
        }else {
            log.info("ShortUrlRpcImpl->rouseAppShortUrl----errorCode={},errorMessage={}", shortUrl.getCode(), shortUrl.getCodeText());
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }
}
