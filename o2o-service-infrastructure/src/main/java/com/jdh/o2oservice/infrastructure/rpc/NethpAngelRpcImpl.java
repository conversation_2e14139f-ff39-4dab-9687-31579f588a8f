package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient;
import com.jdh.nethp.doctor.entry.audit.client.DoctorInfoQueryExportClient;
import com.jdh.nethp.doctor.entry.audit.client.dto.doctor.BaseDoctorInfoClientDTO;
import com.jdh.nethp.doctor.entry.audit.client.dto.doctor.DoctorExtendsInfoDTO;
import com.jdh.nethp.doctor.entry.audit.client.dto.doctor.JdDoctorInfoClientDTO;
import com.jdh.nethp.doctor.entry.audit.client.param.doctor.BaseDoctorInfoClientParam;
import com.jdh.nethp.doctor.entry.audit.client.param.query.DoctorInfoDetailQueryClientParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.angel.enums.AngelErrorCode;
import com.jdh.o2oservice.core.domain.angel.rpc.NethpAngelRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpDoctorPracticeOfficeBo;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpDoctorqualificationsBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NethpBaseDoctorInfoClientParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.DoctorInfoRpcConvert;
import com.jdh.o2oservice.infrastructure.rpc.convert.NethpAngelRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 互医护士信息rpc
 * <AUTHOR>
 * @Date 2024/5/8
 * @Version V1.0
 **/
@Component
@Slf4j
public class NethpAngelRpcImpl implements NethpAngelRpc {

    @Resource
    private DoctorInfoEntryExportClient doctorInfoEntryExportClient;

    @Resource
    private DoctorInfoQueryExportClient doctorInfoQueryExportClient;

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NethpAngelRpcImpl.getBaseDoctorInfo")
    public NethpBaseDoctorInfoClientBo getBaseDoctorInfo(NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam) {
        BaseDoctorInfoClientParam baseDoctorInfoClientParam = NethpAngelRpcConverter.ins.convert2BaseDoctorInfoClientParam(nethpBaseDoctorInfoClientParam);
        //客户端信息
        NhpClientInfo nhpClientInfo = DoctorInfoRpcConvert.ins.getNhpClientInfo();
        try {
            log.info("NethpAngelRpcImpl -> getBaseDoctorInfo start, baseDoctorInfoClientParam:{}", JSON.toJSONString(baseDoctorInfoClientParam));
            JsfResult<BaseDoctorInfoClientDTO> baseDoctorInfo = doctorInfoEntryExportClient.getBaseDoctorInfo(baseDoctorInfoClientParam, nhpClientInfo);
            log.info("NethpAngelRpcImpl -> getBaseDoctorInfo end, baseDoctorInfo:{}", JSON.toJSONString(baseDoctorInfo));
            if (Objects.isNull(baseDoctorInfo) || !Objects.equals(baseDoctorInfo.getCode(), BusinessErrorCode.SUCCESS.getCode())) {
                throw new BusinessException(AngelErrorCode.NETHP_QUERY_ANGEL_INFO_FAILED);
            }

            return NethpAngelRpcConverter.ins.convert2NethpBaseBo(baseDoctorInfo.getData());
        } catch (BusinessException e){
            log.error("NethpAngelRpcImpl -> getBaseDoctorInfo BusinessException", e);
            throw e;
        } catch (Throwable e){
            log.error("NethpAngelRpcImpl -> getBaseDoctorInfo exception", e);
            throw new BusinessException(AngelErrorCode.NETHP_QUERY_ANGEL_INFO_FAILED);
        }
    }

    /**
     * @param nethpBaseDoctorInfoClientParam
     * @Description: 查询互医医生主数据
     * @Return: com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo
     * @Author: zhangxiaojie17
     * @Date: 2024/5/9
     **/
    @Override
    @LogAndAlarm
    public NethpDoctorqualificationsBo queryDoctorBaseInfo(NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam) {
        DoctorInfoDetailQueryClientParam clientParam = new DoctorInfoDetailQueryClientParam();
        clientParam.setPlatformId(nethpBaseDoctorInfoClientParam.getPlatformId());
        clientParam.setDocPin(nethpBaseDoctorInfoClientParam.getPin());
        clientParam.setAppCode("jdh-o2o-service");
        clientParam.setTenantId("JD8888");
        //客户端信息
        NhpClientInfo nhpClientInfo = DoctorInfoRpcConvert.ins.getNhpClientInfo();
        log.info("NethpAngelRpcImpl -> queryDoctorBaseInfo, clientParam={}, nhpClientInfo={}", JSON.toJSONString(clientParam), JSON.toJSONString(nhpClientInfo));
        JsfResult<JdDoctorInfoClientDTO> clientDTOJsfResult = doctorInfoQueryExportClient.queryDoctorBaseInfo(clientParam, nhpClientInfo);
        log.info("NethpAngelRpcImpl -> queryDoctorBaseInfo, clientDTOJsfResult={}", JSON.toJSONString(clientDTOJsfResult));
        if (Objects.isNull(clientDTOJsfResult) || !Objects.equals(clientDTOJsfResult.getCode(), BusinessErrorCode.SUCCESS.getCode())) {
            throw new BusinessException(AngelErrorCode.NETHP_QUERY_ANGEL_INFO_FAILED);
        }
        NethpDoctorqualificationsBo qualificationsBo = new NethpDoctorqualificationsBo();
        JdDoctorInfoClientDTO data = clientDTOJsfResult.getData();
        qualificationsBo.setPlatformId(data.getPlatformId());
        qualificationsBo.setPin(data.getPin());
        qualificationsBo.setTitleId(data.getTitleId());
        qualificationsBo.setTitleName(data.getTitleName());
        qualificationsBo.setQualificationsCode(data.getQualificationsCode());
        qualificationsBo.setPracticeCode(data.getPracticeCode());
        return qualificationsBo;
    }

    /**
     * @param nethpBaseDoctorInfoClientParam
     * @Description: 查询互医医生主数据
     * @Return: com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo
     * @Author: zhangxiaojie17
     * @Date: 2024/5/9
     **/
    @Override
    @LogAndAlarm
    public NethpDoctorPracticeOfficeBo queryDoctorExtendsInfo(NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam) {
        DoctorInfoDetailQueryClientParam clientParam = new DoctorInfoDetailQueryClientParam();
        clientParam.setPlatformId(nethpBaseDoctorInfoClientParam.getPlatformId());
        clientParam.setDocPin(nethpBaseDoctorInfoClientParam.getPin());
        clientParam.setAppCode("jdh-o2o-service");
        clientParam.setTenantId("JD8888");
        //客户端信息
        NhpClientInfo nhpClientInfo = DoctorInfoRpcConvert.ins.getNhpClientInfo();
        log.info("NethpAngelRpcImpl -> queryDoctorExtendsInfo, clientParam={}, nhpClientInfo={}", JSON.toJSONString(clientParam), JSON.toJSONString(nhpClientInfo));
        JsfResult<DoctorExtendsInfoDTO> extendsInfoDTOJsfResult = doctorInfoQueryExportClient.queryDoctorExtendsInfo(clientParam, nhpClientInfo);
        log.info("NethpAngelRpcImpl -> queryDoctorExtendsInfo, jdDoctorInfoClientDTOJsfResult={}", JSON.toJSONString(extendsInfoDTOJsfResult));
        if (Objects.isNull(extendsInfoDTOJsfResult) || !Objects.equals(extendsInfoDTOJsfResult.getCode(), BusinessErrorCode.SUCCESS.getCode())) {
            throw new BusinessException(AngelErrorCode.NETHP_QUERY_ANGEL_INFO_FAILED);
        }
        NethpDoctorPracticeOfficeBo resultBo = new NethpDoctorPracticeOfficeBo();
        DoctorExtendsInfoDTO data = extendsInfoDTOJsfResult.getData();

        resultBo.setPlatformId(data.getOriginDocId());
        resultBo.setMajor(data.getMajor());
        resultBo.setPracticeOffice(data.getPracticeOffice());
        return resultBo;
    }
}
