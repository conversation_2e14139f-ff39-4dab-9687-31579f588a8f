package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jd.risk.riskservice.v2.RiskQueryService;
import com.jd.risk.riskservice.v2.vo.QueryRequest;
import com.jd.risk.riskservice.v2.vo.result.RiskResult;
import com.jd.risk.riskservice.v2.vo.result.StrategyResult;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.support.param.OperationLogKeyEnum;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DeployDuccConfig;
import com.jdh.o2oservice.base.ducc.deploy.DispatchRiskStrategyDeploy20250428;
import com.jdh.o2oservice.core.domain.dispatch.enums.RiskStrategyEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.RiskQueryServiceRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchRiskStrategyParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/27 20:10
 */
@Component
@Slf4j
public class RiskQueryServiceRpcImpl implements RiskQueryServiceRpc {

    private static final String BUSINESS_CODE = "homeincare";
    @Resource
    private RiskQueryService riskQueryService;
    @Resource
    private OperationLogApplication operationLogApplication;

    @Value("${com.jd.risk.riskservice.v2.RiskQueryService.useType}")
    private String useType;
    @Resource
    private DeployDuccConfig deployDuccConfig;


    @Override
    @LogAndAlarm
    public RiskStrategyEnum queryStrategy(DispatchRiskStrategyParam param) {
        DispatchRiskStrategyDeploy20250428 strategy20250428 = deployDuccConfig.getDispatchRiskStrategy20250428();
        // 配置不为空，且开关关闭，则不走风控
        if (Objects.nonNull(strategy20250428) && strategy20250428.isClose()) {
            log.info("RiskQueryServiceRpcImpl->queryStrategy 风控策略关闭，不走风控");
            return RiskStrategyEnum.NONE;
        }

        return operationLogApplication.execute(param1 -> {
            try {
                QueryRequest request = buildRiskRequest(param);
                RiskResult<StrategyResult> riskResult = riskQueryService.queryStrategy(request);
                log.info("RiskQueryServiceRpcImpl->queryStrategy riskResult={}", JSON.toJSONString(riskResult));
                if (riskResult.isSuccess()) {
                    StrategyResult strategyResult = riskResult.getResult();
                    if (StringUtils.equals(strategyResult.getStrategy(), RiskStrategyEnum.RISK.getCode())) {
                        return RiskStrategyEnum.RISK;
                    } else if (StringUtils.equals(strategyResult.getStrategy(), RiskStrategyEnum.NONE.getCode())) {
                        return RiskStrategyEnum.NONE;
                    }
                }
                return RiskStrategyEnum.ERROR;
            } catch (Throwable e) {
                log.error("RiskQueryServiceRpcImpl->queryStrategy error", e);
                return RiskStrategyEnum.ERROR;
            }
        }, param,  param.getOperator(), OperationLogKeyEnum.DISPATCH_RISK_QUERY, Objects.toString(param.getAggregateId()));
    }

    private QueryRequest buildRiskRequest(DispatchRiskStrategyParam param){
        QueryRequest request = new QueryRequest();
        request.setAppName(CommonConstant.APP_NAME);
        request.setSubSys(BUSINESS_CODE);
        request.setPin(param.getUserPin());
        request.setTime(new Date());
        request.setUseType(useType);

        Map<String, Object> extendMap = Maps.newHashMap();
        extendMap.put("businessCode", BUSINESS_CODE);
        extendMap.put("province", param.getProvinceCode());
        extendMap.put("city", param.getCityCode());
        extendMap.put("county", param.getDistrictCode());
        extendMap.put("town", param.getTownCode());
        extendMap.put("_addressDetail", param.getAddressDetail());
        extendMap.put("reserveStartTime", param.getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        extendMap.put("reserveEndTime", param.getAppointmentTime().getAppointmentEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        extendMap.put("serverId", param.getAngelId());
        extendMap.put("_serverMobile", param.getAngelPhone());
        extendMap.put("serverPin", param.getAngelPin());



        Map<String, Object> extProps = Maps.newHashMap();
        Map<String, Object> homeInCareMap = Maps.newHashMap();
        homeInCareMap.put("verticalCode", param.getVerticalCode());
        extProps.put("homeInCare", homeInCareMap);
        extendMap.put("extProps", extProps);

        request.setExtendMap(extendMap);
        log.info("RiskQueryServiceRpcImpl->buildRiskRequest request={}", JSON.toJSONString(request));
        return request;
    }
}
