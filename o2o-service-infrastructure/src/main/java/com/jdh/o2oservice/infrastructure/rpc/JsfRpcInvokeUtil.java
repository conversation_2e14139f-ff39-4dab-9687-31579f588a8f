package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.jsf.gd.error.RpcException;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * Jsf 接口调用，简化 JsfResult 处理
 *
 */
@Slf4j
public class JsfRpcInvokeUtil {

    private JsfRpcInvokeUtil() {
    }


    /**
     * 无参数调用返回
     *
     * @param supplier Supplier
     * @param <R> 返回泛型
     * @return 返回值
     */
    public static <R> R invoke(Supplier<JsfResult<R>> supplier) {

        try {
            JsfResult<R> result = supplier.get();
            log.info("invoke -> result={}", JSON.toJSONString(result));
            if (Objects.isNull(result) || Objects.isNull(result.getCode())) {
                throw new NullPointerException("jsf consumer invoke result is null");
            }
            if (!result.isSuccess()) {
                DynamicErrorCode errorCode = new DynamicErrorCode(result.getCode(), result.getMsg());
                throw new BusinessException(errorCode);
            }
            return result.getData();
            // jsf调用返回业务异常
        } catch (BusinessException e) {
            log.error("Jsf rpc error ", e);
            throw e;
            // jsf调用系统错误
        }catch (RpcException e){
            log.error("Jsf rpc error ", e);
            throw e;
        }catch (Throwable e) {
            log.error("Jsf rpc Throwable ", e);
            throw new RpcException(e.getMessage());
        }
    }

}
