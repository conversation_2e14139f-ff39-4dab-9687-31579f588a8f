package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.fastjson.JSON;
import com.jdd.baoxian.b.insurance.core.trade.export.req.NurseVisitIssuePolicyReq;
import com.jdd.baoxian.b.insurance.core.trade.export.req.NurseVisitQueryReq;
import com.jdd.baoxian.b.insurance.core.trade.export.res.NurseVisitPolicyRes;
import com.jdd.baoxian.b.insurance.core.trade.export.resource.NurseVisitInsuranceResource;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.angel.rpc.NurseVisitInsuranceResourceRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NurseVisitPolicyBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NurseVisitIssuePolicyParam;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NurseVisitQueryParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.NurseVisitInsuranceResourceConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName NurseVisitInsuranceResourceRpcImpl
 * @Description 保险类
 * <AUTHOR>
 * @Date 2024/8/5 10:19 AM
 * @Version 1.0
 **/
@Service
@Slf4j
public class NurseVisitInsuranceResourceRpcImpl implements NurseVisitInsuranceResourceRpc {

    @Autowired
    private NurseVisitInsuranceResource nurseVisitInsuranceResource;

    /**
     * 给护士投保
     * @param nurseVisitIssuePolicyParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NurseVisitInsuranceResourceRpcImpl.issuePolicy")
    public NurseVisitPolicyBo issuePolicy(NurseVisitIssuePolicyParam nurseVisitIssuePolicyParam) {
        try{
            NurseVisitIssuePolicyReq nurseVisitIssuePolicyReq = NurseVisitInsuranceResourceConvert.convertor.toNurseVisitIssuePolicyReq(nurseVisitIssuePolicyParam);
            NurseVisitPolicyRes nurseVisitPolicyRes = nurseVisitInsuranceResource.issuePolicy(nurseVisitIssuePolicyReq);
            log.info("NurseVisitInsuranceResourceRpcImpl.issuePolicy nurseVisitPolicyRes={}",JSON.toJSONString(nurseVisitPolicyRes));
            return NurseVisitInsuranceResourceConvert.convertor.toNurseVisitPolicyBo(nurseVisitPolicyRes.getPolicy());
        }catch (Exception ex){
            log.info("NurseVisitInsuranceResourceRpcImpl.issuePolicy ex={}", ex);
        }
        return null;
    }

    /**
     * 保单查询
     * @param nurseVisitQueryParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.NurseVisitInsuranceResourceRpcImpl.queryPolicy")
    public NurseVisitPolicyBo queryPolicy(NurseVisitQueryParam nurseVisitQueryParam) {
        NurseVisitQueryReq nurseVisitQueryReq = NurseVisitInsuranceResourceConvert.convertor.toNurseVisitQueryReq(nurseVisitQueryParam);
        try{
            NurseVisitPolicyRes nurseVisitPolicyRes = nurseVisitInsuranceResource.queryPolicy(nurseVisitQueryReq);
            log.info("NurseVisitInsuranceResourceRpcImpl.queryPolicy nurseVisitPolicyRes={}", JSON.toJSONString(nurseVisitPolicyRes));
            return NurseVisitInsuranceResourceConvert.convertor.toNurseVisitPolicyBo(nurseVisitPolicyRes.getPolicy());
        }catch (Exception ex){
            log.info("NurseVisitInsuranceResourceRpcImpl.queryPolicy ex={}", ex);
        }
        return null;
    }
}
