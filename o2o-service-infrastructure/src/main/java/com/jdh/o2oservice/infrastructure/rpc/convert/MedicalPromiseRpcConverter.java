package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhMedicalPromiseQueryVo;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.MedicalPromiseBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseBo;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseListFromEsParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.UpdateMedicalPromiseStationParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.AngelStationExtDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.JdhMedicalPromiseQueryDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseParam;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.report.cmd.UpdateMedPromiseStationCmd;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2024/5/14 10:19 上午
 * @version: 1.0
 */
@Mapper
public interface MedicalPromiseRpcConverter {

    /**
     *
     */
    MedicalPromiseRpcConverter INS = Mappers.getMapper(MedicalPromiseRpcConverter.class);

    /**
     *
     * @param promiseDTO
     * @return
     */
    MedicalPromiseBO convert2MedicalPromiseBO(MedicalPromiseDTO promiseDTO);

    /**
     *
     * @param promiseDTO
     * @return
     */
    List<MedicalPromiseBO> convert2MedicalPromiseBO(List<MedicalPromiseDTO> promiseDTO);

    /**
     * 转换
     * @param medicalPromiseParam
     * @return
     */
    MedicalPromiseDispatchCmd convertToMedicalPromiseDispatchCmd(MedicalPromiseParam medicalPromiseParam);

    /**
     * 转换
     *
     * @param medicalPromiseDTO
     * @return
     */
    JdhMedicalPromiseQueryVo convertToJdhMedicalPromiseQueryVo(MedicalPromiseDTO medicalPromiseDTO);

    /**
     * 转换list
     *
     * @param medicalPromiseDTOList
     * @return
     */
    List<JdhMedicalPromiseQueryDto> convertToJdhMedicalPromiseQueryVoList(List<MedicalPromiseDTO> medicalPromiseDTOList);

    /**
     * 转换成AngelStationExtDto
     * @param angelStationDto
     * @return
     */
    AngelStationExtDto convertToAngelStationExtDto(AngelStationDto angelStationDto);

    JdOrderFullPageParam toJdOrderFullPageParam(MedicalPromiseListFromEsParam medicalPromiseListParam);

    UpdateMedPromiseStationCmd toUpdateMedPromiseStationCmd(UpdateMedicalPromiseStationParam updateMedicalPromiseStationParam);

    MedicalPromiseBo toMedicalPromiseBo(MedicalPromiseDTO medicalPromiseDTO);
}
