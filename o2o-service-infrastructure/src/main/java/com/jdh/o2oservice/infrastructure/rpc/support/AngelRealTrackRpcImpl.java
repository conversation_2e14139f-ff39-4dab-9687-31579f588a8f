package com.jdh.o2oservice.infrastructure.rpc.support;

import com.jdh.o2oservice.application.angel.AngelExtApplication;
import com.jdh.o2oservice.application.support.AngelLocationExtService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelLocationRealParam;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelRealTrackParam;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.support.dto.AngelLocationRealDto;
import com.jdh.o2oservice.export.support.query.AngelLocationRealRequest;
import com.jdh.o2oservice.infrastructure.rpc.convert.AngelRealTrackRpcConvert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName AngelRealTrackRpcImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/8 08:16
 */
@Service("angelRealTrackRpc")
public class AngelRealTrackRpcImpl implements AngelRealTrackRpc {

    @Resource
    private AngelExtApplication angelExtApplication;

    @Resource
    private AngelLocationExtService angelLocationExtService;

    /**
     * 查询服务者经纬度
     *
     * @param angelRealTrackParam
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelRealTrackBo queryAngelRealTrack(AngelRealTrackParam angelRealTrackParam) {
        AngelLocationDto extLocation = angelExtApplication.getExtLocation(angelRealTrackParam.getAngelId());

        AngelRealTrackBo angelRealTrackBo = new AngelRealTrackBo();
        angelRealTrackBo.setAngelId(String.valueOf(angelRealTrackParam.getAngelId()));
        if(Objects.nonNull(extLocation)) {
            angelRealTrackBo.setLat(extLocation.getLatitude());
            angelRealTrackBo.setLng(extLocation.getLongitude());
        }
        return angelRealTrackBo;
    }

    /**
     * 查询服务者经纬度
     *
     * @param angelLocationRealParam
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelLocationBo queryAngelLocationRealTrack(AngelLocationRealParam angelLocationRealParam) {
        AngelLocationRealRequest angelLocationRealRequest = new AngelLocationRealRequest();
        angelLocationRealRequest.setSourceId(angelLocationRealParam.getSourceId());
        angelLocationRealRequest.setPromiseId(angelLocationRealParam.getPromiseId());
        angelLocationRealRequest.setWorkId(angelLocationRealParam.getWorkId());
        angelLocationRealRequest.setAngelId(angelLocationRealParam.getAngelId());
        AngelLocationRealDto angelLocationAndPoint = angelLocationExtService.getAngelLocationAndPoint(angelLocationRealRequest);
        return AngelRealTrackRpcConvert.INS.convertToAngelLocationBo(angelLocationAndPoint);
    }
}
