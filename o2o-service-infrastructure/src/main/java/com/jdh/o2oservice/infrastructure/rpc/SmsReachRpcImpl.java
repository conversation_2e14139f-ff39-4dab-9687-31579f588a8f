package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.mobilePhoneMsg.sender.client.request.SmsTemplateMessage;
import com.jd.mobilePhoneMsg.sender.client.response.SmsTemplateResponse;
import com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.ReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachFactory;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @created 2023-12-19-4:59 下午
 */
@Slf4j
@Component("smsReachRpc")
public class SmsReachRpcImpl implements MessageReachRpc, MapAutowiredKey {
    /**
     * 短信发送超过当日上限
     */
    public static final String EXCEED_UPPER_LIMIT = "7";

    /**
     * smsMessageTemplateRpcService
     * 接口文档：https://cf.jd.com/pages/viewpage.action?pageId=105716237
     */
    @Resource
    private SmsMessageTemplateRpcService smsMessageTemplateRpcService;

    /**
     * jimClient
     */
    @Resource
    private Cluster jimClient;

    /**
     * 发送短信
     *
     * @param reachTemplate reachTemplate
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SmsReachRpcImpl.sendMessage")
    public Boolean sendMessage(ReachTemplate reachTemplate) {
        try {
            SmsTemplateMessage templateMessage = new SmsTemplateMessage();
            templateMessage.setToken(reachTemplate.getToken());
            templateMessage.setSenderNum(reachTemplate.getAccountName());
            templateMessage.setExtension(reachTemplate.getExtension());
            templateMessage.setTemplateParam(reachTemplate.getTemplateParams());
            templateMessage.setTemplateId(reachTemplate.getTemplateId());
            templateMessage.setMobileNum(reachTemplate.getMobileNum());
            log.info("SmsReachRpcImpl -> sendSmsMessage templateMessage:{}", JSON.toJSONString(templateMessage));

            // 发送短信jsf 接口
            SmsTemplateResponse response = smsMessageTemplateRpcService.sendSmsTemplateMessage(templateMessage);
            log.info("SmsReachAbilityImpl->sendSmsMessage 短信发送超过上限, result ={}", JSON.toJSONString(response));
            if (ReachConstant.SAME_CONENT_CODE.equals(response.getResultMsg().getErrorCode())
                    && ReachConstant.DAY_MAX_LIMIT_CODE.equals(response.getBaseResultMsg().getErrorCode())) {
                log.warn("SmsReachAbilityImpl->sendSmsMessage 频繁发送受限制,mobile={}", reachTemplate.getMobileNum());
                return true;
            }
            // 短信发送成功
            if (ReachConstant.BASE_RESULT_CODE.equals(response.getBaseResultMsg().getErrorCode())
                    && ReachConstant.RESULT_CODE.equals(response.getResultMsg().getErrorCode())) {
                log.info("SmsReachAbilityImpl->sendSmsMessage 发送成功,mobile={}", reachTemplate.getMobileNum());
                return true;
            }
            // 短信发送超过上限
            if (EXCEED_UPPER_LIMIT.equals(response.getResultMsg().getErrorCode())) {
                log.info("SmsReachAbilityImpl->sendSmsMessage 短信发送超过上限, mobile ={}", reachTemplate.getMobileNum());
                return true;
            }
            throw new BusinessException(SupportErrorCode.SMS_SEND_FAIL, response.getResultMsg().getErrorMsg() + ";" + response.getBaseResultMsg().getErrorMsg());
        } catch (Throwable e) {
            Profiler.businessAlarm("com.jdh.o2oservice.infrastructure.sms.SmsReachAbilityImpl.sendSmsMessage", e.getMessage());
            log.error("SmsReachAbilityImpl->sendSmsMessage error, paramMap={}, error msg={}", JSON.toJSONString(reachTemplate), e.getMessage(), e);
            throw new BusinessException(SupportErrorCode.SMS_SEND_FAIL, e.getMessage());
        }
    }

    @Override
    public String getSmsCode(String key) {
        return jimClient.get(key);
    }

    /**
     * @return
     */
    @Override
    public String getMapKey() {
        return MessageReachFactory.createRouteKey(ReachTypeEnum.REACH_SMS.getCode());
    }
}
