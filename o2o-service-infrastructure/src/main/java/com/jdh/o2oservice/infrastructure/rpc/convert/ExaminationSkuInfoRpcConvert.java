package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.medical.examination.export.dto.ExaminationGroupDTO;
import com.jdh.o2oservice.core.domain.product.bo.ExaminationSkuBindGroupBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 查询运营端商品套餐结果转换
 *
 * <AUTHOR>
 * @date 2024-08-27 16:34 2024-08-27 16:44
 */
@Mapper
public interface ExaminationSkuInfoRpcConvert {

    ExaminationSkuInfoRpcConvert ins = Mappers.getMapper(ExaminationSkuInfoRpcConvert.class);

    ExaminationSkuBindGroupBo convertToExaminationSkuBindGroupBo(ExaminationGroupDTO examinationGroupDTO);

    List<ExaminationSkuBindGroupBo> convertToExaminationSkuBindGroupBoList(List<ExaminationGroupDTO> examinationGroupDTOList);

}
