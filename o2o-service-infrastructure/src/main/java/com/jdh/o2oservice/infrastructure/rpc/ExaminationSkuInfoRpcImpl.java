package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.health.medical.examination.export.dto.ExaminationGroupDTO;
import com.jd.health.medical.examination.export.param.sku.QueryExaminationGroupParam;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.product.bo.ExaminationSkuBindGroupBo;
import com.jdh.o2oservice.core.domain.product.rpc.ExaminationSkuInfoRpc;
import com.jdh.o2oservice.core.domain.product.rpc.param.ExaminationSkuBindGroupParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.ExaminationSkuInfoRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 消费医疗运营端商品查询服务实现
 *
 * <AUTHOR>
 * @date 2024-08-27 16:34 2024-08-27 16:37
 */
@Component("ExaminationSkuInfoRpc")
@Slf4j
public class ExaminationSkuInfoRpcImpl implements ExaminationSkuInfoRpc {

    /**
     * productReadService
     */
    @Resource
    private SkuInfoExportService skuInfoExportService;

    /**
     * 查询运营端商品关联的套餐列表信息
     *
     * @param groupParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ExaminationSkuInfoRpcImpl.queryExaminationGroupListBySku")
    public List<ExaminationSkuBindGroupBo> queryExaminationGroupListBySku(ExaminationSkuBindGroupParam groupParam) {
        AssertUtils.nonNull(groupParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.hasText(groupParam.getSkuNo(), "商品编码不能为空");

        QueryExaminationGroupParam examinationGroupParam = new QueryExaminationGroupParam();
        examinationGroupParam.setSkuNo(groupParam.getSkuNo());

        JsfResult<List<ExaminationGroupDTO>> listJsfResult = skuInfoExportService.queryExaminationGroupListBySku(examinationGroupParam);

        if(Objects.isNull(listJsfResult) || !listJsfResult.isSuccess()){
            throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }

        return ExaminationSkuInfoRpcConvert.ins.convertToExaminationSkuBindGroupBoList(listJsfResult.getData());
    }
}
