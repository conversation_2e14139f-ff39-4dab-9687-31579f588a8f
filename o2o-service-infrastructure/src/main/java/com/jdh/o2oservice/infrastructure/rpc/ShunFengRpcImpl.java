package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.ShunFengUtils;
import com.jdh.o2oservice.core.domain.support.ship.ShunFengRpc;
import com.jdh.o2oservice.core.domain.support.ship.dto.*;
import com.jdh.o2oservice.core.domain.support.ship.param.*;
import com.jdh.o2oservice.ext.ship.reponse.ShunFengResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * @ClassName ShunFengRpcImpl
 * @Description 顺丰接口对接,此类为测试,后续迁移到扩展点
 * <AUTHOR>
 * @Date 2024/8/20 2:54 PM
 * @Version 1.0
 **/
@Service
@Slf4j
public class ShunFengRpcImpl implements ShunFengRpc {

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private ShunFengUtils shunFengUtils;

    /**
     * 预创建顺丰订单-获取eta预估数据
     * @param createOrderParam
     * @return
     */
    @Override
    @LogAndAlarm
    public PreCreateShunFengOrderDto preCreateOrder(PreCreateShunFengOrderParam createOrderParam) {
        String methodUrl = "/open/api/external/precreateorder";
        return this.processInvoke(methodUrl,createOrderParam,PreCreateShunFengOrderDto.class);
    }

    /**
     * 创建顺丰订单-预约单
     * @param createOrderParam
     * @return
     */
    @Override
    @LogAndAlarm
    public CreateShunFengOrderDto createOrder(CreateShunFengOrderParam createOrderParam){
        String methodUrl = "/open/api/external/createorder";
        return this.processInvoke(methodUrl,createOrderParam,CreateShunFengOrderDto.class);
    }

    /**
     * 取消顺丰订单-预约单
     * @param cancelOrderParam
     * @return
     */
    @Override
    @LogAndAlarm
    public CancelShunFengOrderDto cancelOrder(CancelShunFengOrderParam cancelOrderParam){
        String methodUrl = "/open/api/external/cancelorder";
        return this.processInvoke(methodUrl,cancelOrderParam,CancelShunFengOrderDto.class);
    }

    /**
     * 创建顺丰店铺
     * @param createShopParam
     * @return
     */
    @Override
    @LogAndAlarm
    public CreateShunFengShopDto createShop(CreateShunFengShopParam createShopParam) {
        String methodUrl = "/open/api/external/createshop";
        return this.processInvoke(methodUrl,createShopParam,CreateShunFengShopDto.class);
    }

    /**
     * 编辑顺丰店铺
     * @param updateShopParam
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateShop(UpdateShunFengShopParam updateShopParam) {
        String methodUrl = "/open/api/external/updateshop";
        this.processInvoke(methodUrl,updateShopParam,Object.class);
        return Boolean.TRUE;
    }

    /**
     * 查看店铺详情
     * @param getShopInfoParam
     * @return
     */
    @Override
    @LogAndAlarm
    public GetShunFengShopInfoDto getShopInfo(GetShunFengShopInfoParam getShopInfoParam) {
        String methodUrl = "/open/api/external/getshopinfo";
        return this.processInvoke(methodUrl,getShopInfoParam,GetShunFengShopInfoDto.class);
    }

    /**
     * 获取配送轨迹H5
     * @param riderViewParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ShunFengRiderViewDto riderView(ShunFengRiderViewParam riderViewParam) {
        String methodUrl = "/open/api/external/riderviewv2";
        return this.processInvoke(methodUrl,riderViewParam,ShunFengRiderViewDto.class);
    }

    /**
     * 获取配送员实时坐标接口
     * @param riderLatestPositionParam
     * @return
     */
    @Override
    @LogAndAlarm
    public RiderLatestPositionDto riderLatestPosition(RiderLatestPositionParam riderLatestPositionParam) {
        String methodUrl = "/open/api/external/riderlatestposition";
        return this.processInvoke(methodUrl,riderLatestPositionParam,RiderLatestPositionDto.class);
    }


    /**
     * 统一请求发送
     * @param methodUrl
     * @param requestObj
     * @param cls
     * @return
     * @param <T>
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = duccConfig.getShunFengConfig().getBaseUrl().concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(requestObj));
        log.info("jsonObject={}", JSON.toJSONString(jsonObject));

        try {
            requestUrl = requestUrl.concat("?sign=").concat(shunFengUtils.generateOpenSign(jsonObject.toString(),Long.parseLong(duccConfig.getShunFengConfig().getDevId()),duccConfig.getShunFengConfig().getDevKey()));
        } catch (Exception e) {
            log.info("processInvoke ex=",e);
            return null;
        }
        log.info("requestUrl={}", requestUrl);
        String httpResponse = SimpleHttpClient.simplePost(requestUrl, headMap, jsonObject.toString());
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        ShunFengResponse response = JSON.parseObject(httpResponse, new TypeReference<ShunFengResponse>() {
        });
        response.setErrorMsg(StringEscapeUtils.unescapeJava(response.getErrorMsg()));
        log.info("response={}", JSON.toJSONString(response));
        if (Objects.equals(response.getErrorCode(), 0)) {
            Object result = response.getResult();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        } else if(Objects.nonNull(response) && Objects.equals(String.valueOf(response.getErrorCode()), "1004")) {
            log.info("[ShunFengRpcImpl->processResponse],errorMsg={}", response.getErrorMsg());
            return null;
        }else {
            log.error("[ShunFengRpcImpl->processResponse],顺丰接口访问返回异常!");
            throw new BusinessException(BusinessErrorCode.CUSTOM_ERROR_CODE.formatDescription(response.getErrorMsg()));
        }
    }
}
