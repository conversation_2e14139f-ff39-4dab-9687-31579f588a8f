package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.ares.open.platform.export.dto.home.PartnerSourceOrderDTO;
import com.jd.newnethp.cps.center.export.dto.inspection.InspectSheetInfoDTO;
import com.jdh.o2oservice.core.domain.trade.bo.InspectSheetInfoBO;
import com.jdh.o2oservice.core.domain.trade.bo.PartnerSourceOrderBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/2 14:14
 **/
@Mapper
public interface InspectionQueryRpcConvert {
    InspectionQueryRpcConvert INSTANCE = Mappers.getMapper(InspectionQueryRpcConvert.class);

    @Mapping(target = "expireDate", source = "expireDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    InspectSheetInfoBO DTO2BO(InspectSheetInfoDTO data);

    PartnerSourceOrderBO DTO2BO(PartnerSourceOrderDTO data);
}
