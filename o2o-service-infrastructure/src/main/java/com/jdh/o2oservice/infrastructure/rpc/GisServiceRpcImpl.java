package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.lbs.geofencing.api.customfence.CustomPresortService;
import com.jd.lbs.geofencing.api.dto.BaseResponse;
import com.jd.lbs.geofencing.api.dto.CustomPresortRequestDto;
import com.jd.lbs.geofencing.api.dto.CustomPresortResponseDto;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.support.basic.rpc.GisServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.CustomPresortResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.CustomPresortRpcParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.GisServiceRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * GIS RPC服务实现类
 */
@Service
@Slf4j
public class GisServiceRpcImpl implements GisServiceRpc {

    /**
     * 外部围栏服务
     */
    @Resource
    private CustomPresortService customPresortService;
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * appKey
     */
    @Value("${gis.appKey}")
    private String appKey;

    /**
     * 根据用户具体地址查询站点坐标
     *
     * @param customerId
     * @param customPresortRpcParam
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.GisServiceRpcImpl.customPresort")
    public List<CustomPresortResponseBO> customPresort(String customerId, CustomPresortRpcParam customPresortRpcParam) {
        try {
            CustomPresortRequestDto customPresortRequestDto = GisServiceRpcConverter.instance.convertParam2Dto(customPresortRpcParam);
            if (CollectionUtil.isEmpty(customPresortRequestDto.getBusinessTypes())) {
                throw new SystemException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
            }

            customPresortRequestDto.setBusinessId(String.valueOf(generateIdFactory.getId()));

            BaseResponse<List<CustomPresortResponseDto>> listBaseResponse = customPresortService.customPresort(appKey, customerId, customPresortRequestDto);
            //200成功,201未命中
            if (!Objects.equals(200, listBaseResponse.getStatus()) && !Objects.equals(201, listBaseResponse.getStatus())) {
                log.info("GisServiceRpcImpl->customPresort,error={}", JSON.toJSONString(listBaseResponse));
                throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
            }
            return GisServiceRpcConverter.instance.convertDto2Bo(listBaseResponse.getResult());
        } catch (Throwable e) {
            log.info("GisServiceRpcImpl->customPresort,error={}", e.getMessage());
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }
}
