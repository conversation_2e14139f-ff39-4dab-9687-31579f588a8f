package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.TimeIntervalIntersection;
import com.jdh.o2oservice.core.domain.support.ship.UavRpc;
import com.jdh.o2oservice.core.domain.support.ship.dto.AirportCreateDto;
import com.jdh.o2oservice.core.domain.support.ship.dto.AirportUnbindOrderDto;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportChangeInfoParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportCreateParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportFlightScheduleParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportUnbindOrderParam;
import com.jdh.o2oservice.ext.ship.reponse.UavResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 无人机
 */
@Slf4j
@Service
public class UavRpcImpl implements UavRpc {

    @Autowired
    private DuccConfig duccConfig;


    /**
     * 查询航班时刻
     * @param airportFlightScheduleParam
     * @return
     */
    @Override
    @LogAndAlarm
    public List<Long> airportFlightSchedule(AirportFlightScheduleParam airportFlightScheduleParam) {
        //String methodUrl = "/outside/airport_flight_schedule";
        //airportFlightScheduleParam.setKey(duccConfig.getUavConfig().getKey());
        //return this.processInvokeGet(methodUrl,airportFlightScheduleParam,Long.class);

        List<Long> result = new ArrayList<>();
        LocalDate today = LocalDate.now();

        String[] businessHours = duccConfig.getUavConfig().getBusinessHours().split("-");
        LocalTime start = LocalTime.of(Integer.parseInt(businessHours[0].split(":")[0]),Integer.parseInt(businessHours[0].split(":")[1]),0);
        LocalTime end = LocalTime.of(Integer.parseInt(businessHours[1].split(":")[0]),Integer.parseInt(businessHours[1].split(":")[1]),0);

        TimeIntervalIntersection.TimeInterval timeIntervalConfig = new TimeIntervalIntersection.TimeInterval(start, end, duccConfig.getUavConfig().getIntervalMinute());
        List<TimeIntervalIntersection.TimeInterval> dateTimeIntervals = TimeIntervalIntersection.splitTimeIntervals(timeIntervalConfig);
        for (TimeIntervalIntersection.TimeInterval timeInterval:dateTimeIntervals) {
            LocalDateTime dateTime = LocalDateTime.of(today, timeInterval.getStart());
            long timestamp = dateTime.atZone(ZoneId.systemDefault())
                    .toInstant()
                    .toEpochMilli();
            result.add(timestamp/1000);
        }

        LocalDateTime dateTime = LocalDateTime.of(today, end);
        long timestamp = dateTime.atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
        result.add(timestamp/1000);

        return result;
    }


    /**
     * 下发飞行需求
     * @param airportCreateParam
     * @return
     */
    @Override
    @LogAndAlarm
    public AirportCreateDto create(AirportCreateParam airportCreateParam) {
        String methodUrl = "/outside/flight_work/create";
        airportCreateParam.setKey(duccConfig.getUavConfig().getKey());
        return this.processInvoke(methodUrl,airportCreateParam,AirportCreateDto.class);
    }


    /**
     * 追加飞行需求
     * @param airportChangeInfoParam
     * @return
     */
    @Override
    @LogAndAlarm
    public String changeInfo(AirportChangeInfoParam airportChangeInfoParam) {
        String methodUrl = "/outside/flight_work/change_info";
        airportChangeInfoParam.setKey(duccConfig.getUavConfig().getKey());
        return this.processInvoke(methodUrl,airportChangeInfoParam,String.class);
    }

    /**
     * 解绑飞行需求
     * @param airportUnbindOrderParam
     * @return
     */
    @Override
    @LogAndAlarm
    public AirportUnbindOrderDto unbindOrder(AirportUnbindOrderParam airportUnbindOrderParam) {
        String methodUrl = "/outside/flight_work/change_info";
        airportUnbindOrderParam.setKey(duccConfig.getUavConfig().getKey());
        return this.processInvoke(methodUrl,airportUnbindOrderParam, AirportUnbindOrderDto.class);
    }





    /**
     * 统一请求发送
     * @param methodUrl
     * @param requestObj
     * @param cls
     * @return
     * @param <T>
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = duccConfig.getUavConfig().getBaseUrl().concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(requestObj));
        log.info("jsonObject={}", JSON.toJSONString(jsonObject));

        log.info("requestUrl={}", requestUrl);
        String httpResponse = SimpleHttpClient.simplePost(requestUrl, headMap, jsonObject.toString());
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        UavResponse response = JSON.parseObject(httpResponse, new TypeReference<UavResponse>() {
        });
        log.info("response={}", JSON.toJSONString(response));
        if (Objects.equals(response.getErrcode(), 0)) {
            Object result = response.getResult();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        }else {
            log.error("[UavRpcImpl->processResponse],无人机接口访问返回异常!");
            throw new BusinessException(BusinessErrorCode.CUSTOM_ERROR_CODE.formatDescription(response.getErrcode()));
        }
    }

    private <T> List<T> processInvokeGet(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = duccConfig.getUavConfig().getBaseUrl().concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(requestObj));
        log.info("jsonObject={}", JSON.toJSONString(jsonObject));
        List<String> params = new ArrayList<>();
        jsonObject.forEach((k,v)->{
            params.add(k+"="+v);
        });
        requestUrl = requestUrl+"?"+ Joiner.on("&").join(params);
        log.info("requestUrl={}", requestUrl);
        String httpResponse = SimpleHttpClient.simpleGet(requestUrl);
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        UavResponse response = JSON.parseObject(httpResponse, new TypeReference<UavResponse>() {
        });
        log.info("response={}", JSON.toJSONString(response));
        if (Objects.equals(response.getErrcode(), 0)) {
            Object result = response.getResult();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseArray(JSON.toJSONString(result), cls);
        } else {
            log.error("[UavRpcImpl->processResponse],无人机接口访问返回异常!");
            throw new BusinessException(BusinessErrorCode.CUSTOM_ERROR_CODE.formatDescription(response.getErrcode()));
        }
    }
}
