package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.pop.patient.client.domain.PatientInfoDTO;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.PatientInfoRpcBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: yang<PERSON><PERSON>
 * @date: 2023/12/28 11:00 上午
 * @version: 1.0
 */
@Mapper
public interface PatientConverter {

    PatientConverter convertor = Mappers.getMapper(PatientConverter.class);


    PatientInfoRpcBO patientDto2RpcBO(PatientInfoDTO patientInfo);

}
