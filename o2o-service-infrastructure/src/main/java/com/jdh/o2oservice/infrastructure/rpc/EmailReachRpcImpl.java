package com.jdh.o2oservice.infrastructure.rpc;

import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.ReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachFactory;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName EmailReachRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/2/4 15:06
 **/
@Slf4j
@Component("emailReachRpc")
public class EmailReachRpcImpl implements MessageReachRpc, MapAutowiredKey {

    /**
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return MessageReachFactory.createRouteKey(ReachTypeEnum.REACH_EMAIL.getCode());
    }

    /**
     *
     * @param reachTemplate
     * @return
     */
    @Override
    public Boolean sendMessage(ReachTemplate reachTemplate) {
        return null;
    }

    /**
     *
     * @param key
     * @return
     */
    @Override
    public String getSmsCode(String key) {
        return null;
    }
}