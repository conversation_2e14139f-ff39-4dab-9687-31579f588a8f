package com.jdh.o2oservice.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.medicine.base.common.exception.BusinessErrorCode;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.sensitiveword.domain.SensitiveWordCheckRequest;
import com.jd.sensitiveword.domain.SensitiveWordCheckResult;
import com.jd.sensitiveword.service.SensitiveWordJsfService;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.KeeperDetectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.SensitiveWordParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 合规算法检测服务 文本红线违规检测
 * @author: yangxiyu
 * @date: 2024/7/19 13:32
 * @version: 1.0
 */
@Slf4j
@Component
public class KeeperDetectionServiceRpcImpl implements KeeperDetectionServiceRpc {

    /**
     * 天网敏感词
     */
    @Resource
    private SensitiveWordJsfService sensitiveWordJsfService;
    /**
     * 天网-单个敏感词检查接口
     *
     * @param sensitiveWordParam
     * @return
     */
    @Override
    public boolean checkSensitiveWord(SensitiveWordParam sensitiveWordParam) {
        try {
            log.info("KeeperDetectionServiceRpcImpl -> checkSensitiveWord start, text={}", JSON.toJSONString(sensitiveWordParam));
            SensitiveWordCheckRequest request = new SensitiveWordCheckRequest(sensitiveWordParam.getText(), sensitiveWordParam.getSence().getUseCategory(), Lists.newArrayList("0"));
            log.info("KeeperDetectionServiceRpcImpl -> checkSensitiveWord request={}", JsonUtil.toJSONString(request));
            SensitiveWordCheckResult wordCheckResult = sensitiveWordJsfService.checkSensitiveWord(request);
            log.info("KeeperDetectionServiceRpcImpl -> checkSensitiveWord end,result={}", JsonUtil.toJSONString(wordCheckResult));
            if (Objects.isNull(wordCheckResult)) {
                throw new BusinessException(BusinessErrorCode.JSF_CALL_ERROR, "sensitiveWordJsfService返回结果为null");
            }

            //调用失败 如超时、服务异常等情况时返回 1；接口成功调用返回 0
            //处理接口调用失败的情况
            if (!NumberUtils.INTEGER_ZERO.equals(wordCheckResult.state)) {
                log.error("KeeperDetectionServiceRpcImpl checkSensitiveWord fail message:{}", wordCheckResult.message);
                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR.formatDescription(wordCheckResult.message));
            }
            //是否通过: 请求体中存在一个或多个敏感词时 返回false;请求体中不包含敏感词 返回true
            return wordCheckResult.isPass;
        } catch (Exception e) {
            log.error("KeeperDetectionServiceRpcImpl -> textRedLineDetect exception 未知异常", e);
        }
        //报错兜底返回通过，不影响业务逻辑
        return true;
    }
}
