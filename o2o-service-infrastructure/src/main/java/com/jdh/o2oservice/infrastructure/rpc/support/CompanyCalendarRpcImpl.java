package com.jdh.o2oservice.infrastructure.rpc.support;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.ea.rainbow.attend.api.attendance.CompanyCalendarJsfService;
import com.jd.ea.rainbow.attend.api.biz.BizResult;
import com.jd.ea.rainbow.attend.api.dto.CompanyCalendarDTO;
import com.jd.ea.rainbow.attend.api.dto.QueryCompanyCalendarParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.rpc.CompanyCalendarRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.CompanyCalendarBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.QueryCompanyCalendarBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CompanyCalendarRpcImpl implements CompanyCalendarRpc {

    @Resource
    private CompanyCalendarJsfService companyCalendarJsfService;

    /**
     * 根据考勤组、工作计划、日期查询工作日程表
     * https://j-api.jd.com/fe-app-view/demandManage/readOnly/21428?interfaceType=3&methodId=144470&type=3
     *
     * @param bo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.support.CompanyCalendarRpcImpl.getCalendarByGroupAndTime")
    public List<CompanyCalendarBo> getCalendarByGroupAndTime(QueryCompanyCalendarBo bo) {
        QueryCompanyCalendarParam param = new QueryCompanyCalendarParam();
        param.setAccountSet("CHN");
        param.setTenantId("CN.JD.GROUP");
        param.setGroupCode("CHN001");
        param.setWorkPlanCode("CHN001");
        param.setStartDate(bo.getStartDate());
        param.setEndDate(bo.getEndDate());
        BizResult<CompanyCalendarDTO> result = companyCalendarJsfService.getCalendarByGroupAndTime(param);
        log.info("CompanyCalendarRpcImpl getCalendarByGroupAndTime param={}, result={}", JSON.toJSONString(param), JSON.toJSONString(result));
        if (Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getResult()) && CollectionUtils.isNotEmpty(result.getResult().getDateList())){
            return JSON.parseArray(JSON.toJSONString(result.getResult().getDateList()), CompanyCalendarBo.class);
        }
        return Lists.newArrayList();
    }
}
