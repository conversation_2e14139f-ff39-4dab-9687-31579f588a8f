package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseReqDto;
import com.jd.lbs.jdlbsapi.dto.c2c.TextParseResult;
import com.jdh.b2c.export.dto.address.AddressInfoDTO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.TextParseAddressBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.AddressTextParseRpcParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface JdhAddressRpcConverter {

    JdhAddressRpcConverter instance = Mappers.getMapper(JdhAddressRpcConverter.class);

    BaseAddressBo convertToBaseAddressBo(BaseAddressInfo baseAddressInfo);

    /**
     *
     * @param param
     * @return
     */
    TextParseReqDto convertToTextParseReqDto(AddressTextParseRpcParam param);

    /**
     *
     * @param textParseResult
     * @return
     */
    TextParseAddressBO convertToTextParseAddressBO(TextParseResult textParseResult);
}
