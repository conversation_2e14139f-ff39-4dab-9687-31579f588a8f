package com.jdh.o2oservice.infrastructure.rpc;


import com.alibaba.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport;
import com.jdh.afterdiag.medicalrecord.export.sickLeave.dto.SickLeaveInfoDTO;
import com.jdh.afterdiag.medicalrecord.export.sickLeave.dto.SickLeaveQueryParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SickLeaveExportRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.QuerySickCertBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.QuerySickCertParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.DoctorInfoRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description 查询病假单
 */
@Service
@Slf4j
public class SickLeaveExportRpcImpl implements SickLeaveExportRpc {

    @Autowired
    private SickLeaveExport sickLeaveExport;

    @Override
    @LogAndAlarm
    public QuerySickCertBO querySickCert(QuerySickCertParam querySickCertParam) {

        SickLeaveQueryParam sickLeaveQueryParam = new SickLeaveQueryParam();
        sickLeaveQueryParam.setDiagReportIdList(querySickCertParam.getMedicalPromiseIds());
        sickLeaveQueryParam.setPatientId(querySickCertParam.getPatientId());

        NhpClientInfo nhpClientInfo = DoctorInfoRpcConvert.ins.getNhpClientInfo();
        JsfResult<SickLeaveInfoDTO> jsfResult =  sickLeaveExport.querySickLeaveByReportId(sickLeaveQueryParam,nhpClientInfo);
        log.info("sickLeaveExport.querySickLeaveByReportId jsfResult={}", JSON.toJSONString(jsfResult));
        if(jsfResult==null){
            throw new BusinessException(new DynamicErrorCode("-1","上游接口返回为空"));
        }
        if(!jsfResult.isSuccess()){
            throw new BusinessException(new DynamicErrorCode("-1",jsfResult.getMsg()));
        }
        return JSON.parseObject(JSON.toJSONString(jsfResult.getData()),QuerySickCertBO.class);
    }
}
