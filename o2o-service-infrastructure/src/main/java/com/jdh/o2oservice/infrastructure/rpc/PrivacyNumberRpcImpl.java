package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.jdcc.privacy.number.gateway.facade.authorization.AuthorizationServiceFacade;
import com.jd.jdcc.privacy.number.gateway.facade.business.BindNumberServiceFacade;
import com.jd.jdcc.privacy.number.gateway.facade.dto.ResultDto;
import com.jd.jdcc.privacy.number.gateway.facade.dto.authorization.AuthorizationParameter;
import com.jd.jdcc.privacy.number.gateway.facade.dto.business.BindAxeRequest;
import com.jd.jdcc.privacy.number.gateway.facade.dto.business.BindNumberResultDto;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.reach.model.PrivacyNumber;
import com.jdh.o2oservice.core.domain.support.reach.repository.query.PrivacyNumberQuery;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.PrivacyNumberRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant.*;

/**
 * <AUTHOR>
 * @created 2024-01-05-3:19 下午
 */
@Component("privacyNumberRpc")
@Slf4j
public class PrivacyNumberRpcImpl implements PrivacyNumberRpc {

    /**
     * 吉姆客户端
     */
    @Resource
    private Cluster jimClient;

    /**
     * authorizationServiceFacade
     */
    @Resource
    private AuthorizationServiceFacade authorizationServiceFacade;

    /**
     * bindNumberServiceFacade
     */
    @Resource
    private BindNumberServiceFacade bindNumberServiceFacade;

    /**
     * 获取隐私号码
     *
     * @param query 查询
     * @return {@link PrivacyNumber}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.PrivacyNumberRpcImpl.getPrivacyNumber")
    public PrivacyNumber getPrivacyNumber(PrivacyNumberQuery query) {
        if (query == null || StringUtils.isBlank(query.getTenant()) || StringUtils.isBlank(query.getPasswd())) {
            ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription("当前业务未配置隐私号租户信息");
            throw new BusinessException(errorCode);
        }
        String token = this.getAccessToken(query);
        if(StringUtils.isBlank(token)){
            return null;
        }
        BindAxeRequest bindAxeRequest = new BindAxeRequest();
        bindAxeRequest.setPhoneNoA(query.getPhoneNumber());
        bindAxeRequest.setTenantCode(query.getTenant());
        bindAxeRequest.setToken(token);
        if (query.getCallDisplayType() != null) {
            bindAxeRequest.setCallDisplayType(query.getCallDisplayType());
        } else {
            bindAxeRequest.setCallDisplayType(NumConstant.NUM_3);
        }
        bindAxeRequest.setBusinessId(query.getBusinessId());

        //绑定关系有效时间
        LocalDateTime time = TimeUtils.getCurrentLocalDateTime();
        time = time.plusSeconds(query.getBindDurationSeconds());
        bindAxeRequest.setExpiration(TimeUtils.localDateTimeToStr(time));

        try {
            log.info("PrivacyNumberRpcImpl -> getPrivacyNumber query:{}", JSON.toJSONString(query));
            ResultDto<BindNumberResultDto> resultDto = bindNumberServiceFacade.bindAxe(bindAxeRequest);
            log.info("PrivacyNumberRpcImpl -> getPrivacyNumber result:{}",JSON.toJSONString(resultDto));
            if(!resultDto.getSuccess()){
                throw new BusinessException(new DynamicErrorCode(resultDto.getResultCode().toString(),resultDto.getErrorMessage()));
            }
            BindNumberResultDto numberResultDto = resultDto.getData();
            PrivacyNumber privacyNumber = new PrivacyNumber();
            privacyNumber.setSecretNo(numberResultDto.getResult().getData().getSecretNo());
            privacyNumber.setExtension(numberResultDto.getResult().getData().getExtension());
            return privacyNumber;
        }catch (BusinessException be){
            log.error("PrivacyNumberRpcImpl -> getPrivacyNumber BusinessException, query:{}", query, be);
            throw be;
        } catch (Exception e){
            log.error("PrivacyNumberRpcImpl -> getPrivacyNumber exception, query:{}", query, e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }

    /**
     * 获取访问令牌
     *
     * @return {@link String}
     */
    private String getAccessToken(PrivacyNumberQuery query){
        String token;
        String tokenCacheKey = PRIVACY_NUMBER_CACHE_KEY + ":" + query.getTenant();
        try {
            token = jimClient.get(tokenCacheKey);
            if(StringUtils.isNotBlank(token)){
                return token;
            }
        }catch (Exception e){
            log.error("PrivacyNumberRpcImpl -> getAccessToken, fetch from cache exception",e);
        }

        token = getAccessTokenRemote(query);
        try {
            jimClient.setEx(tokenCacheKey, token, PRIVACY_NUMBER_CACHE_EXPIRE, TimeUnit.HOURS);
        }catch (Exception e){
            log.error("PrivacyNumberRpcImpl -> getAccessToken, cache exception, token:{}", token,e);
        }
        return token;
    }

    /**
     * getAccessTokenRemote
     *
     * @return {@link String}
     */
    private String getAccessTokenRemote(PrivacyNumberQuery query){
        AuthorizationParameter parameter = new AuthorizationParameter();
        parameter.setTenantCode(query.getTenant());
        parameter.setPassword(query.getPasswd());
        try {
            log.info("PrivacyNumberRpcImpl -> getAccessTokenRemote parameter:{}",JSON.toJSONString(parameter));
            ResultDto<String> resultDto = authorizationServiceFacade.getAccessToken(parameter);
            log.info("PrivacyNumberRpcImpl -> getAccessTokenRemote result:{}", JSON.toJSONString(resultDto));
            if(!resultDto.getSuccess()){
                throw new BusinessException(new DynamicErrorCode(resultDto.getResultCode().toString(),resultDto.getErrorMessage()));
            }
            return resultDto.getData();
        }catch (BusinessException be){
            log.error("PrivacyNumberRpcImpl -> getAccessTokenRemote BusinessException, params:{}", JSON.toJSONString(parameter), be);
            throw be;
        } catch (Exception e){
            log.error("PrivacyNumberRpcImpl -> getAccessTokenRemote exception, params:{}", JSON.toJSONString(parameter), e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }
}
