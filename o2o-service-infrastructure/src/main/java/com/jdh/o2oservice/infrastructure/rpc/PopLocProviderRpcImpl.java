package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.health.xfyl.merchant.export.dto.supplier.VenderGoodsInfoDTO;
import com.jd.health.xfyl.merchant.export.dto.supplier.VenderGoodsResultDTO;
import com.jd.health.xfyl.merchant.export.param.VenderGoodsParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.enums.MarryEnum;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.rpc.PopLocProviderRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.ServiceRpcBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * POP服务套餐查询
 * @author: yangxiyu
 * @date: 2023/12/28 11:22 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class PopLocProviderRpcImpl implements PopLocProviderRpc {


    /**
     * xfylMerchantAppointApiExportService
     */
    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;

    @LogAndAlarm
    @Override
    public List<ServiceRpcBO> listGoods(String skuId, JdhPromise promise,  String storeId) {

        VenderGoodsParam param = new VenderGoodsParam();
        param.setSkuNo(skuId);
        param.setStoreNo(storeId);

        // 有的商家在查询套餐的时候需要订单对应的LOC码数据，这种商家是不支持买约一体的，当promise不为空时，将核销码传递到下游。
        if(Objects.nonNull(promise)){
            param.setCodeNo(promise.getCode());
            param.setPwdNumber(promise.getCodePwd());
            param.setOuterSkuIdSnapshot(EntityUtil.getFiledDefaultNull(promise.findBasicService(), PromiseService::getOutSkuId));
            promise.findBasicService();
        }
        VenderGoodsResultDTO goodsResultDTO = JsfRpcInvokeUtil.invoke(()->xfylMerchantAppointApiExportService.getVenderGoodsBySku(param));
        if (CollectionUtils.isEmpty(goodsResultDTO.getInfoList())){
            return Collections.emptyList();
        }
        log.info("PopLocProviderRpcImpl->getGoods goodsResultDTO={}", JSON.toJSONString(goodsResultDTO));
        List<ServiceRpcBO> res = Lists.newArrayList();
        for (VenderGoodsInfoDTO venderGoodsInfoDTO : goodsResultDTO.getInfoList()) {
            ServiceRpcBO serviceRpcBO = new ServiceRpcBO();
            serviceRpcBO.setServiceId(Long.valueOf(skuId));
            serviceRpcBO.setOutServiceId(venderGoodsInfoDTO.getGoodsId());
            serviceRpcBO.setOutServiceName(venderGoodsInfoDTO.getGoodsName());

            serviceRpcBO.setSupportGender(Objects.isNull(venderGoodsInfoDTO.getUserGender()) ? GenderEnum.COMMON.getType() : venderGoodsInfoDTO.getUserGender());
            serviceRpcBO.setSupportMarry(Objects.isNull(venderGoodsInfoDTO.getUserMarriage()) ? MarryEnum.COMMON.getType() : venderGoodsInfoDTO.getUserMarriage());
            res.add(serviceRpcBO);
        }
        return res;
    }
}
