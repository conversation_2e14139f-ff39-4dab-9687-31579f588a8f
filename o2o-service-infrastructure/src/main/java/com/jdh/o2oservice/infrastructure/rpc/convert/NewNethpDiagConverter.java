package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.newnethp.diag.export.triage.dto.assignresult.AssignDoctorDTO;
import com.jd.newnethp.diag.export.triage.dto.assignresult.DiagAssignSuspendParam;
import com.jd.newnethp.diag.export.triage.dto.assignresult.DiagLatestAssignResultDTO;
import com.jd.newnethp.diag.export.triage.param.ChangeAssignSwitchStatusParam;
import com.jd.newnethp.diag.export.triage.param.OrderTransferByDoctorParam;
import com.jd.newnethp.diag.export.triage.param.OrderTransferParam;
import com.jd.newnethp.trade.center.export.core.dto.submit.DiagOrderSubmitDTO;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.DoctorReplyParam;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.TradeCancelDiagParam;
import com.jd.newnethp.trade.center.export.core.param.diagprocess.TradeDiagEndParam;
import com.jd.newnethp.trade.center.export.core.param.submit.param.DiagOrderParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagDoctorInfoBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagLatestAssignResultBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderSubmitBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName NewNethpDiagConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/4/29 15:03
 **/
@Mapper
public interface NewNethpDiagConverter {

    /**
     * JdhPromiseInfrastructureConverter
     */
    NewNethpDiagConverter convertor = Mappers.getMapper(NewNethpDiagConverter.class);

    /**
     * param2DiagOrderParam
     * @param param
     * @return
     */
    DiagOrderParam param2DiagOrderParam(NewNethpDiagOrderParam param);

    /**
     * param2DoctorReplyParam
     * @param param
     * @return
     */
    DoctorReplyParam param2DoctorReplyParam(NewNethpDiagDoctorReplyParam param);

    /**
     * param2OrderTransferParam
     * @param param
     * @return
     */
    OrderTransferParam param2OrderTransferParam(NewNethpOrderTransferByDoctorParam param);

    /**
     * param2ChangeAssignSwitchStatusParam
     * @param param
     * @return
     */
    ChangeAssignSwitchStatusParam param2ChangeAssignSwitchStatusParam(NewNethpChangeAssignSwitchStatusParam param);

    /**
     * param2DiagAssignSuspendParam
     * @param param
     * @return
     */
    DiagAssignSuspendParam param2DiagAssignSuspendParam(NewNethpDiagAssignSuspendParam param);

    /**
     * param2TradeCancelDiagParam
     * @param param
     * @return
     */
    TradeCancelDiagParam param2TradeCancelDiagParam(NewNethpTradeCancelDiagParam param);

    /**
     * param2TradeDiagEndParam
     * @param param
     * @return
     */
    TradeDiagEndParam param2TradeDiagEndParam(NewNethpTradeEndDiagParam param);

    /**
     * submit2NewNethpDiagOrderResult
     * @param dto
     * @return
     */
    NewNethpDiagOrderSubmitBO submit2NewNethpDiagOrderResult(DiagOrderSubmitDTO dto);

    /**
     * assignDoctorDTO2NewNethpDiagDoctorInfoBO
     * @param dto
     * @return
     */
    NewNethpDiagDoctorInfoBO assignDoctorDTO2NewNethpDiagDoctorInfoBO(AssignDoctorDTO dto);

    /**
     * assign2DiagLatestAssignResult
     * @param dto
     * @return
     */
    @Mapping(target = "doctorBaseInfoBO" ,source = "assignDoctorDTO")
    @Mapping(target = "doctorBaseInfoBOList" ,source = "assignDoctorDTOList")
    NewNethpDiagLatestAssignResultBO assign2DiagLatestAssignResult(DiagLatestAssignResultDTO dto);
}