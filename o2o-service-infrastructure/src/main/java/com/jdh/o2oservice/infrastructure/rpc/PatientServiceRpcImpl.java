package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.pop.patient.client.domain.JsfResult;
import com.jd.pop.patient.client.domain.PatientInfoDTO;
import com.jd.pop.patient.client.service.PatientInfoManageService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.promise.rpc.PatientServiceRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.PatientInfoRpcBO;
import com.jdh.o2oservice.infrastructure.rpc.convert.PatientConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: yangxiyu
 * @date: 2023/12/28 10:53 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class PatientServiceRpcImpl implements PatientServiceRpc {

    private static final String SUCCESS_CODE = "0";

    @Resource
    private PatientInfoManageService patientInfoManageService;


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.PatientServiceRpcImpl.queryByPatientId")
    public PatientInfoRpcBO queryByPatientId(String userPin, Long patientId) {
        PatientInfoDTO param = new PatientInfoDTO();
        param.setUserPin(userPin);
        param.setPatientId(patientId);
        JsfResult<PatientInfoDTO> result;
        try {
            result = patientInfoManageService.getPatientInfoByPatientIdAndUserPin(param);
            log.info("PatientServiceRpcImpl -> queryByPatientId end, result={}", JSON.toJSONString(result));
        }catch (Exception e){
            log.error("PatientServiceRpcImpl->queryByPatientId error", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        if (result != null && Objects.equals(SUCCESS_CODE, result.getCode())) {
            return PatientConverter.convertor.patientDto2RpcBO(result.getData());
        }else{
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }
}
