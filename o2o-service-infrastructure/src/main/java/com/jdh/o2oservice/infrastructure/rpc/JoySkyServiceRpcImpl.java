package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.health.medical.examination.export.dto.XfylManApprovalRecordDTO;
import com.jd.health.medical.examination.export.param.supplier.XfylManApprovalRecordParam;
import com.jd.health.medical.examination.export.service.supplier.XfylManApprovalRecordExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JoySkyServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: joysky审批流
 * @Author: wangpengfei144
 * @Date: 2024/7/16
 */
@Service
@Slf4j
public class JoySkyServiceRpcImpl implements JoySkyServiceRpc {

    /**
     * 体检运营端joysky
     */
    @Resource
    private XfylManApprovalRecordExportService xfylManApprovalRecordExportService;


    /**
     * 查询审批列表
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public List<XfylManApprovalRecordDTO> queryApprovalRecordList(XfylManApprovalRecordParam param) {
        JsfResult<List<XfylManApprovalRecordDTO>> result = xfylManApprovalRecordExportService.queryApprovalRecordList(param);
        if (Objects.nonNull(result) && result.isSuccess()){
            return result.getData();
        }
        return null;
    }
}
