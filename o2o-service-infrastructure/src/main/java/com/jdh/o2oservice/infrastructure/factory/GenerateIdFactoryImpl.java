package com.jdh.o2oservice.infrastructure.factory;

import com.global.service.id.GlobalServiceID;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.EnvEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 全局唯一ID工厂
 * @author: yangxiyu
 * @date: 2022/12/9 9:17 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class GenerateIdFactoryImpl implements GenerateIdFactory {
    /**
     * 全局唯一报告表
     */
    public static final int  EXAMINATIONID= 2;

    /**
     * 当前配置
     */
    private static String ACTIVE;

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }

    /**
     * globalServiceID
     */
    @Resource
    private GlobalServiceID globalServiceID;

    @Resource
    private GenerateIdFactoryFailBack factoryFailBack;

    /**
     * @return
     * @throws Exception
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryImpl.getIdStr")
    public String getIdStr() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return ""+System.currentTimeMillis();
            }
            long id = globalServiceID.getShortId();
            return ""+id;
        }catch (Throwable e){
            log.error("GenerateIdFactoryImpl-`getIdStr` is fail", e);
            return factoryFailBack.getIdStr();
        }
    }

    /**
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryImpl.getId", logSwitch = false)
    public Long getId() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return System.currentTimeMillis();
            }
            return globalServiceID.getShortId();
        }catch (Throwable e){
            log.error("GenerateIdFactoryImpl-`getId` is fail", e);
            return factoryFailBack.getId();
        }
    }

    /**
     *
     * @param requiredNum
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryImpl.getBatchId")
    public Queue<Long> getBatchId(int requiredNum) {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                Queue<Long> res = new LinkedBlockingQueue<>(requiredNum);
                for (int i = 0; i < requiredNum; i++){
                    res.add(System.currentTimeMillis());
                }
                return res;
            }
            // getBatchShortId 最多返回50个
            LinkedBlockingDeque<Long> ids = new LinkedBlockingDeque<>();
            if(requiredNum > 50){
                int times = requiredNum/50;
                if ((requiredNum % 50) != 0){
                    times += 1;
                }
                for (int i = 0; i < times; i++){
                    ids.addAll(globalServiceID.getBatchShortId(50));
                }
            }else{
                ids.addAll(globalServiceID.getBatchShortId(requiredNum));

            }
            return ids;
        }catch (Throwable e){
            log.error("GenerateIdFactoryImpl-`getBatchId` is fail", e);
            return factoryFailBack.getBatchId(requiredNum);

        }
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.factory.GenerateIdFactoryImpl.getReportId")
    public Long getReportId() {
        try {
            if (EnvEnum.DEV_TEST.getCode().equals(ACTIVE)){
                return System.currentTimeMillis();
            }
            return globalServiceID.getId(EXAMINATIONID);
        }catch (Throwable e){
            log.error("GenerateIdFactoryImpl-`getReportId` is fail", e);
            return factoryFailBack.getReportId();
        }
    }
}
