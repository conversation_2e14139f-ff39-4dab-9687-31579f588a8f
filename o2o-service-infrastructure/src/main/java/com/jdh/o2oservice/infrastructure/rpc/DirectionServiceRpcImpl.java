package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.lbs.jdlbsapi.dto.*;
import com.jd.lbs.jdlbsapi.search.DirectionService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.DirectionConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName DirectionServiceRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 11:14
 **/
@Slf4j
@Service
public class DirectionServiceRpcImpl implements DirectionServiceRpc {

    /**
     * directionService
     */
    @Resource
    private DirectionService directionService;

    /**
     * 距离矩阵接口
     * 传入起点终点经纬度、计算模式，可计算二者距离和方案估算时间（含路况）
     * @param param
     * https://lbsapi.jd.com/iframe.html?nav=2&childNav=0-1&childURL=/doc/guide/addressService/geocodingService/
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DirectionServiceRpcImpl.getDirectionResult", logSwitch = false)
    public DirectionResultBO getDirectionResult(DirectionRequestParam param) {
        try {
            if(Objects.equals(param.getFromLocation(), param.getToLocation())) {
                DirectionResultBO result = new DirectionResultBO();
                result.setDistance(0.0);
                result.setDuration(0.0);
                result.setMode(param.getTravelMode().getMode());
                return result;
            }
            if (Objects.equals(param.getTravelMode(), TravelMode.DRIVING)) {
                DrivingDirectionRequest request = new DrivingDirectionRequest();
                request.setFromLocation(param.getFromLocation());
                request.setToLocation(param.getToLocation());
                request.setAppkey("789F9F3418954AB39B5FB5274F88BD6F");
                log.info("DirectionServiceRpcImpl -> getDirectionResult DRIVING,request={}", JSON.toJSONString(request));
                BaseResponse<List<DrivingDirectionServiceRoutes>> drivingDirectionResult = directionService.getDrivingDirectionResult(request);
                if (null != drivingDirectionResult && Objects.equals(drivingDirectionResult.getStatus(), 200)) {
                    if (CollectionUtils.isNotEmpty(drivingDirectionResult.getResult())) {
                        DrivingDirectionServiceRoutes direction = drivingDirectionResult.getResult().stream().filter(result -> Objects.equals(result.getMode(), TravelMode.DRIVING.getMode())).findFirst().get();
                        return DirectionConverter.convertor.convertResultBO(direction);
                    }
                }else {
                    throw new BusinessException(Objects.isNull(drivingDirectionResult) ? BusinessErrorCode.UNKNOWN_ERROR : new DynamicErrorCode(String.valueOf(drivingDirectionResult.getStatus()), drivingDirectionResult.getMessage()));
                }
            } else if (Objects.equals(param.getTravelMode(), TravelMode.BICYCLING)) {
                BicyclingDirectionRequestDto request = new BicyclingDirectionRequestDto();
                request.setFromLocation(param.getFromLocation());
                request.setToLocation(param.getToLocation());
                request.setAppkey("789F9F3418954AB39B5FB5274F88BD6F");
                log.info("DirectionServiceRpcImpl -> getDirectionResult BICYCLING,request={}", JSON.toJSONString(request));
                BaseResponse<List<BicyclingDirectionRouteInfo>> bicyclingDirectionResult = directionService.getBicyclingDirectionResult(request);
                if (null != bicyclingDirectionResult && Objects.equals(bicyclingDirectionResult.getStatus(), 200)) {
                    if (CollectionUtils.isNotEmpty(bicyclingDirectionResult.getResult())) {
                        BicyclingDirectionRouteInfo direction = bicyclingDirectionResult.getResult().stream().filter(result -> Objects.equals(result.getMode(), TravelMode.BICYCLING.getMode())).findFirst().get();
                        return DirectionConverter.convertor.convertResultBO(direction);
                    }
                }else {
                    throw new BusinessException(Objects.isNull(bicyclingDirectionResult) ? BusinessErrorCode.UNKNOWN_ERROR : new DynamicErrorCode(String.valueOf(bicyclingDirectionResult.getStatus()), bicyclingDirectionResult.getMessage()));
                }
            }
        } catch (Throwable e) {
            log.error("DirectionServiceRpcImpl -> getDirectionResult error", e);
        }
        return null;
    }
}