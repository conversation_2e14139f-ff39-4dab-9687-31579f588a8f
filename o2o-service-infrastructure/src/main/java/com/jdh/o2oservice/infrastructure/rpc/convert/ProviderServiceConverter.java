package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.xfyl.merchant.export.dto.ServiceGuaranteelDTO;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jd.health.xfyl.merchant.export.param.ServiceDetailParam;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ServiceItemMaterialPackageVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ServiceItemVo;
import com.jdh.o2oservice.core.domain.provider.bo.PromiseProviderCallbackParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ServiceDetailBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.AppointmentRpcParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.promise.cmd.PromiseCallbackCmd;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ProviderServiceConverter
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Mapper
public interface ProviderServiceConverter {

    /**
     * ins
     */
    ProviderServiceConverter ins = Mappers.getMapper(ProviderServiceConverter.class);

    /**
     * 参数转换
     * @param param
     * @return
     */
    ServiceDetailParam convertParam(ServiceDetailRpcParam param);

    /**
     * 结果转换
     * @param dto
     * @return
     */
    ServiceDetailBo dto2Bo(ServiceGuaranteelDTO dto);

    /**
     * convert to AppointmentParam
     * @param appointmentRpcParam
     * @return
     */
    @Mapping(source = "promiseId", target="jdAppointmentId")
    @Mapping(source = "promiseStatus", target="orderStatus")
    AppointmentParam convertToAppointmentParam(AppointmentRpcParam appointmentRpcParam);

    ServiceItemMaterialPackageVo convertToServiceItemMaterialPackageVo(ItemMaterialPackageRelCmd itemMaterialPackageRelCmd);

    ServiceItemVo convertToServiceItemVo(ServiceItemDto serviceItemDto);

    List<ServiceItemVo> convertToServiceItemVos(List<ServiceItemDto> serviceItemDtos);

    PromiseCallbackCmd convertPromiseCallbackCmd(PromiseProviderCallbackParam callbackParam);


}
