package com.jdh.o2oservice.infrastructure.rpc;

import cn.jdl.jecap.api.order.create.CommonCreateOrderApi;
import cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi;
import cn.jdl.jecap.request.order.create.CommonCheckPreCreateOrderRequest;
import cn.jdl.jecap.request.order.create.CommonCreateOrderRequest;
import cn.jdl.jecap.request.order.fulfillment.CommonModifyCancelOrderRequest;
import cn.jdl.jecap.request.order.fulfillment.CommonOrderCancelRequest;
import cn.jdl.jecap.response.Response;
import cn.jdl.jecap.response.order.create.CommonCheckPreCreateOrderResponse;
import cn.jdl.jecap.response.order.create.CommonCreateOrderResponse;
import cn.jdl.jecap.response.order.fulfillment.CommonModifyCancelOrderResponse;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.core.domain.support.ship.JdLogisticsRpc;
import com.jdh.o2oservice.core.domain.support.ship.param.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.JdLogisticsRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 京东物流
 * zhangcan37
 */
@Component
@Slf4j
public class JdLogisticsRpcImpl implements JdLogisticsRpc {

    @Autowired
    private CommonCreateOrderApi commonCreateOrderApi;

    @Autowired
    private CommonModifyCancelOrderApi commonModifyCancelOrderApi;


    /**
     * 查询可约时段
     */
    @LogAndAlarm
    @Override
    public CommonCheckPreCreateOrderBo commonCheckPreCreateOrderV1(CommonCheckPreCreateOrderParam commonCheckPreCreateOrderParam){
        CommonCheckPreCreateOrderRequest commonCheckPreCreateOrderRequest = JdLogisticsRpcConverter.instance.toCommonCheckPreCreateOrderRequest(commonCheckPreCreateOrderParam);
        Response<CommonCheckPreCreateOrderResponse> response = commonCreateOrderApi.commonCheckPreCreateOrderV1(commonCheckPreCreateOrderRequest);
        log.info("commonCheckPreCreateOrderV1 response = {}", JSON.toJSONString(response));
        if(response==null){
            throw new BusinessException(new DynamicErrorCode("-1", "京东物流查询时段接口返回数据为空!"));
        }
        if(!response.isSuccess()){
            throw new BusinessException(new DynamicErrorCode(response.getCode()+"", response.getMsg()));
        }
        return JdLogisticsRpcConverter.instance.toCommonCheckPreCreateOrderBo(response.getData());
    }

    /**
     * 创建物流单
     */
    @LogAndAlarm
    @Override
    public CommonCreateOrderBo commonCreateOrderV1(CommonCreateOrderParam commonCreateOrderParam){
        CommonCreateOrderRequest request = JSON.parseObject(JSON.toJSONString(commonCreateOrderParam),CommonCreateOrderRequest.class);
        Response<CommonCreateOrderResponse> response = commonCreateOrderApi.commonCreateOrderV1(request);
        log.info("commonCreateOrderV1 response = {}", JSON.toJSONString(response));
        if(response==null){
            throw new BusinessException(new DynamicErrorCode("-1", "京东物流创建物流单接口返回数据为空!"));
        }
        if(!response.isSuccess()){
            throw new BusinessException(new DynamicErrorCode(response.getCode()+"", response.getMsg()));
        }
        return JdLogisticsRpcConverter.instance.toCommonCreateOrderBo(response.getData());
    }


    /**
     * 修改物流单
     */
    @LogAndAlarm
    @Override
    public CommonModifyCancelOrderBo commonModifyOrderV1(CommonModifyOrderParam commonModifyOrderParam){
        CommonModifyCancelOrderRequest request = JSON.parseObject(JSON.toJSONString(commonModifyOrderParam),CommonModifyCancelOrderRequest.class);
        Response<CommonModifyCancelOrderResponse> response = commonModifyCancelOrderApi.commonModifyOrderV1(request);
        log.info("commonModifyOrderV1 response = {}", JSON.toJSONString(response));
        if(response==null){
            throw new BusinessException(new DynamicErrorCode("-1", "京东物流创建物流单接口返回数据为空!"));
        }
        if(!response.isSuccess()){
            throw new BusinessException(new DynamicErrorCode(response.getCode()+"", response.getMsg()));
        }
        return JdLogisticsRpcConverter.instance.toCommonModifyCancelOrderBo(response.getData());
    }


    /**
     * 物流单取消
     */
    @LogAndAlarm
    @Override
    public CommonModifyCancelOrderBo commonCancelOrderV1(CommonCancelOrderParam commonCancelOrderParam){
        CommonOrderCancelRequest request = JSON.parseObject(JSON.toJSONString(commonCancelOrderParam),CommonOrderCancelRequest.class);
        Response<CommonModifyCancelOrderResponse> response = commonModifyCancelOrderApi.commonCancelOrderV1(request);
        log.info("commonCancelOrderV1 response = {}", JSON.toJSONString(response));
        if(response==null){
            throw new BusinessException(new DynamicErrorCode("-1", "京东物流取消物流单接口返回数据为空!"));
        }
        if(!response.isSuccess()){
            throw new BusinessException(new DynamicErrorCode(response.getCode()+"", response.getMsg()));
        }
        return JdLogisticsRpcConverter.instance.toCommonModifyCancelOrderBo(response.getData());
    }



}
