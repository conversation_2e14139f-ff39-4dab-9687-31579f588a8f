package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.health.medical.examination.export.dto.XfylManApprovalResultDto;
import com.jd.health.medical.examination.export.param.supplier.XfylManApprovalRecordParam;
import com.jd.health.medical.examination.export.service.supplier.XfylManApprovalRecordExportService;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.core.domain.settlement.rpc.XfylManApprovalServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 运营端审核api
 *
 * <AUTHOR>
 * @date 2024-08-26 10:30
 */
@Slf4j
@Service("xfylManApprovalServiceRpc")
public class XfylManApprovalServiceRpcImpl implements XfylManApprovalServiceRpc {

    /**
     * xfylManApprovalRecordExportService
     */
    @Resource
    XfylManApprovalRecordExportService xfylManApprovalRecordExportService;

    /**
     * 提交审核
     *
     * @param param
     * @return
     */
    @Override
    public XfylManApprovalResultDto addAdjustManApprovalRecord(XfylManApprovalRecordParam param) {
        log.info("XfylManApprovalServiceRpcImpl -> addAdjustManApprovalRecord param={}", JsonUtil.toJSONString(param));
        XfylManApprovalResultDto ret = JsfRpcInvokeUtil.invoke(() -> xfylManApprovalRecordExportService.addAdjustManApprovalRecord(param));
        log.info("XfylManApprovalServiceRpcImpl -> addAdjustManApprovalRecord result={}", JsonUtil.toJSONString(ret));
        return ret;
    }
}
