package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelSkillRelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelStationBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelWorkGroupCountBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchAngelSkillDictParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchAngelWorkCountQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelStationParam;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelSkillRelDto;
import com.jdh.o2oservice.export.angel.query.AngelSkillDictPageRequest;
import com.jdh.o2oservice.export.angel.query.AngelStationRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkGroupCountDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkCountQuery;
import com.jdh.o2oservice.export.ztools.query.QueryAngelStationRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName DispatchAngelStationConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 16:26
 **/
@Mapper
public interface DispatchFlowDependConverter {

    /**
     *
     */
    DispatchFlowDependConverter convertor = Mappers.getMapper(DispatchFlowDependConverter.class);

    /**
     *
     * @param param
     * @return
     */
    QueryAngelStationRequest convertQueryAngelStationRequest(DispatchQueryAngelStationParam param);

    /**
     *
     * @param angelStationDtoList
     * @return
     */
    List<DispatchAngelStationBO> convertDispatchAngelStationBO(List<AngelStationDto> angelStationDtoList);

    /**
     *
     * @param param
     * @return
     */
    AngelStationRequest convertAngelStationRequest(DispatchQueryAngelParam param);

    /**
     *
     * @param jdhAngelDtoList
     * @return
     */
    List<DispatchAngelBO> convertDispatchAngelBO(List<JdhAngelDto> jdhAngelDtoList);

    /**
     *
     * @param param
     * @return
     */
    AngelSkillDictPageRequest convertAngelSkillDictPageRequest(DispatchAngelSkillDictParam param);

    /**
     *
     * @param jdhAngelSkillRelDtoList
     * @return
     */
    List<DispatchAngelSkillRelBO> convertDispatchAngelSkillRelBO(List<JdhAngelSkillRelDto> jdhAngelSkillRelDtoList);

    /**
     *
     * @param query
     * @return
     */
    AngelWorkCountQuery convertAngelWorkCountQuery(DispatchAngelWorkCountQuery query);

    /**
     *
     * @param list
     * @return
     */
    List<DispatchAngelWorkGroupCountBO> convertDispatchAngelWorkGroupCountBO(List<AngelWorkGroupCountDto> list);
}