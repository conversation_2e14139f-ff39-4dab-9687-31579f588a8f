package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.health.record.data.jsf.bean.extend.UnBindReportCmd;
import com.jdh.health.record.data.jsf.bean.report.ReportInfo;
import com.jdh.health.record.data.jsf.bean.report.par.ReportSaveRequest;
import com.jdh.health.record.data.jsf.bean.report.par.UploadReportCommandJsfPar;
import com.jdh.health.record.data.jsf.service.ReportExtendService;
import com.jdh.health.record.data.jsf.service.ReportRepositoryService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.core.domain.report.model.UnBindReportBo;
import com.jdh.o2oservice.core.domain.report.rpc.ReportCenterRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class ReportCenterRpcImpl implements ReportCenterRpc {

    @Autowired
    ReportRepositoryService reportRepositoryService;

    @Autowired
    private ReportExtendService reportExtendService;

    @Value("${oss.defaultBucket}")
    private String fileBucket;

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ReportCenterRpcImpl.syncMedicalReportToReportCenter")
    public SyncReportToCenterResBo createMedicalReportToReportCenter(ReportCenterReport reportCenterReport) {
        try {

            ReportSaveRequest reportSaveRequest = new ReportSaveRequest();
            reportSaveRequest.setUserPin(reportCenterReport.getUserPin());
            reportSaveRequest.setPatientId(reportCenterReport.getPatientId());

            reportSaveRequest.setReportSource(reportCenterReport.getReportSource());
            reportSaveRequest.setReportType(reportCenterReport.getReportType());

            reportSaveRequest.setReportCategory(10);
            reportSaveRequest.setFileMd5(reportCenterReport.getFileMd5());
            reportSaveRequest.setFileOriginOssUrl(reportCenterReport.getReportOss());
            reportSaveRequest.setStructOssUrl(reportCenterReport.getStructReportOss());
            reportSaveRequest.setBucketName(fileBucket);

            com.jdh.health.record.data.jsf.bean.report.par.child.ReportInfo reportInfo = new com.jdh.health.record.data.jsf.bean.report.par.child.ReportInfo();
            reportInfo.setUserName(reportCenterReport.getPatientName());
            reportInfo.setReportUrl(reportCenterReport.getReportOss());
            reportInfo.setJdAppointmentId(reportCenterReport.getPromiseId());
            reportInfo.setExaminationTime(reportCenterReport.getExaminationTime());
            reportInfo.setSkuName(reportCenterReport.getServiceItemName());

            reportSaveRequest.setReportInfo(reportInfo);
            reportSaveRequest.setBizCode(105);

            log.info("reportRepositoryService.saveReport param = {} ", JSON.toJSON(reportSaveRequest));
            JsfResult<ReportInfo> rpcRs = reportRepositoryService.saveReport(reportSaveRequest);
            log.info("reportRepositoryService.saveReport result = {} ", JSON.toJSON(rpcRs));

            if (Objects.nonNull(rpcRs)) {
                //400015代表推过相同的PDF，在档案处认为是同一份报告，但在消医处认为不是，此时将报告中心ID更新到消医库
                if (rpcRs.isSuccess() || StringUtils.equals("400015",rpcRs.getCode())){
                    return SyncReportToCenterResBo.builder().reportCenterId(rpcRs.getData().getReportId()).code(rpcRs.getCode()).build();
                }
            }

//            if(Objects.nonNull(rpcRs) && rpcRs.isSuccess() && Objects.nonNull(rpcRs.getData()) && StringUtils.isNotBlank(rpcRs.getData().getReportId())) {
//                return rpcRs.getData().getReportId();
//            }

            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);

        } catch (Throwable e) {
            log.error("orReportCenterRpcImpl syncMedicalReportToReportCenter is err：", e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ReportCenterRpcImpl.updateMedicalReportToReportCenter")
    public SyncReportToCenterResBo updateMedicalReportToReportCenter(ReportCenterReport reportCenterReport) {
        try {
            UploadReportCommandJsfPar uploadReportCommandJsfPar = new UploadReportCommandJsfPar();
            uploadReportCommandJsfPar.setReportId(reportCenterReport.getReportCenterId());
            uploadReportCommandJsfPar.setUserPin(reportCenterReport.getUserPin());
            uploadReportCommandJsfPar.setPatientId(String.valueOf(reportCenterReport.getPatientId()));

            uploadReportCommandJsfPar.setReportSource(Objects.nonNull(reportCenterReport.getReportSource()) ? String.valueOf(reportCenterReport.getReportSource()) : null);
            uploadReportCommandJsfPar.setReportType(reportCenterReport.getReportType());

            uploadReportCommandJsfPar.setReportCategory(10);
            uploadReportCommandJsfPar.setFileMd5(reportCenterReport.getFileMd5());
            uploadReportCommandJsfPar.setReportUrl(reportCenterReport.getReportOss());
            uploadReportCommandJsfPar.setStructOssUrl(reportCenterReport.getStructReportOss());
            uploadReportCommandJsfPar.setReportBucketName(fileBucket);

            uploadReportCommandJsfPar.setPatientName(reportCenterReport.getPatientName());

            uploadReportCommandJsfPar.setExaminationDate(reportCenterReport.getExaminationTime());

            uploadReportCommandJsfPar.setBizCode("105");

            if (StringUtil.isNotBlank(reportCenterReport.getServiceItemName())){
                uploadReportCommandJsfPar.setSkuName(reportCenterReport.getServiceItemName());
            }

            log.info("reportRepositoryService.updateReportInfo param = {} ", JSON.toJSON(uploadReportCommandJsfPar));
            JsfResult<Boolean> rpcRs = reportRepositoryService.updateReportInfo(uploadReportCommandJsfPar);
            log.info("reportRepositoryService.updateReportInfo result = {} ", JSON.toJSON(rpcRs));

            if(Objects.nonNull(rpcRs) && rpcRs.isSuccess()) {
                return SyncReportToCenterResBo.builder().code(rpcRs.getCode()).success(rpcRs.getData()).build();
            }

            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);

        } catch (Throwable e) {
            log.error("orReportCenterRpcImpl updateMedicalReportToReportCenter is err：", e);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
    }

    /**
     * 解除报告绑定
     *
     * @param unBindReportBo 解除绑定的报告信息
     * @return 解除绑定操作是否成功
     */
    @Override
    @LogAndAlarm
    public Boolean unbindReport(UnBindReportBo unBindReportBo) {
        UnBindReportCmd unBindReportCmd = new UnBindReportCmd();
        unBindReportCmd.setId(unBindReportBo.getId());
        unBindReportCmd.setPin(unBindReportBo.getPin());
        unBindReportCmd.setPatientId(unBindReportBo.getPatientId());
        JsfResult<Boolean> booleanJsfResult = reportExtendService.unBindReport(unBindReportCmd);
        if(booleanJsfResult.isSuccess()) {
            return booleanJsfResult.getData();
        }
        return null;
    }

}
