package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.uim2.facade.response.res.Uim2DimResourceDto;
import com.jd.uim2.facade.response.role.Uim2RoleDto;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.Uim2RoleBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 角色对象转换
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Mapper
public interface RoleRpcConvert {
    RoleRpcConvert ins = Mappers.getMapper(RoleRpcConvert.class);

    Uim2DimResourceBO toUim2DimResourceBO(Uim2DimResourceDto uim2DimResourceDto);

    List<Uim2DimResourceBO> toUim2DimResourceBOList(List<Uim2DimResourceDto> uim2DimResourceDtos);

    Uim2RoleBO toUim2RoleBO(Uim2RoleDto uim2RoleDto);

    List<Uim2RoleBO> toUim2RoleBOList(List<Uim2RoleDto> uim2RoleDtos);
}
