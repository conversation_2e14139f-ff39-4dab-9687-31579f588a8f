package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.O2oHttpClient;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotCardRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName DongDongRobotCardRpcImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/9/25 17:22
 */
@Service
@Slf4j
public class DongDongRobotCardRpcImpl implements DongDongRobotCardRpc {

    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 发送咚咚机器人消息（群消息）
     *
     * @param groupId
     * @param atUsers
     * @return
     */
    @Override
    public boolean sendDongDongRobotCard(String groupId, JSONArray atUsers) {
        //获取appToken
        JSONObject accessTokenJson = getAppAccessToken();
        String appAccessToken = accessTokenJson.getString("appAccessToken");

        //获取teamAccessToken
        JSONObject tokenJson = getTeamAccessToken(appAccessToken);
        String teamAccessToken = tokenJson.getString("teamAccessToken");

        //发送互动卡片消息
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("x-stage","PROD");
        headerMap.put("authorization", MessageFormat.format("Bearer {0}", teamAccessToken));

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appId", "5nH15lZK4oniHev5SlNt");
        paramMap.put("requestId", String.valueOf(generateIdFactory.getId()));
        paramMap.put("dateTime", System.currentTimeMillis());
        paramMap.put("groupId", groupId);
        paramMap.put("tenantId", "CN.JD.GROUP");
        paramMap.put("erp", "yaoqinghai");

        //params参数组装
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("robotId", "00_c4919652924045a4");

        //data参数组装
        Map<String, Object> data = new HashMap<>();
//        data.put("templateId", "");
//        data.put("templateType", 1);
//        data.put("summary", "");
        data.put("reload", false);

//        //at参数组装
//        Map<String, Object> at = new HashMap<>();
//        at.put("atAll", false);
//        at.put("users", atUsers);
//        data.put("at", at);

        //cardData参数组装
        Map<String, Object> cardData = new HashMap<>();
        cardData.put("templateCardId", "wwH7E7rgOKBcRqCtpQ2ic");
        cardData.put("templateCardVersion", "0.0.8");

        //卡片参数组装
        Map<String, Object> cardParamData = new HashMap<>();
        cardParamData.put("alarmHead", "测试测试测试");
        cardParamData.put("alarmContent", "就是测试");
        cardParamData.put("detailSkipurl", "https://o2os-yf.jd.com/quickInspect/serviceStationManage/list/create?id=177521092460545");
        cardData.put("templateCardVariable", cardParamData);

        data.put("cardData", cardData);
        paramsMap.put("data", data);

        paramMap.put("params", paramsMap);

        log.info("DongDongRobotCardRpcImpl -> sendDongDongRobotCard, headerMap={}, paramMap={}", headerMap, paramMap);
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/suite/v1/timline/sendJUEMsg", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotCardRpcImpl -> sendDongDongRobotCard, result={}", json);

        return false;
    }



    /**
     * 获取getTeamAccessToken
     * teamAccessToken
     * @return
     */
    public JSONObject getTeamAccessToken(String appAccessToken){
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("x-stage","PROD");

        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appAccessToken",appAccessToken);
        paramMap.put("openTeamId","01fa30349540670d991ffec19be8227e");
        log.info("DongDongRobotCardRpcImpl -> getTeamAccessToken, headerMap={}, paramMap={}", headerMap, paramMap);
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/auth/v1/team_access_token", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotCardRpcImpl -> getTeamAccessToken, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            return result.getJSONObject("data");
        }
        return null;
    }

    /**
     * 获取getAppAccessToken
     * appAccessToken
     * @return
     */
    public JSONObject getAppAccessToken(){
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type","application/json; charset=utf-8");
        headerMap.put("x-stage","PROD");

        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("appKey","5nH15lZK4oniHev5SlNt");
        paramMap.put("appSecret","NzgD0QQf7mb04nKK6Gfq");
        log.info("DongDongRobotCardRpcImpl -> getAppAccessToken, headerMap={}, paramMap={}", headerMap, paramMap);
        String json = O2oHttpClient.postJson("http://openme.jd.local/open-api/auth/v1/app_access_token", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("DongDongRobotCardRpcImpl -> getAppAccessToken, result={}", json);
        JSONObject result = JsonUtil.parseObject(json);
        if (Objects.equals(CommonConstant.ZERO, result.getInteger("code"))) {
            return result.getJSONObject("data");
        }
        return null;
    }
}
