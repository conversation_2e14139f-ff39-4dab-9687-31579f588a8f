package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.trade2.core.domain.address.export.dto.AbstractAddressItemDTO;
import com.jd.trade2.core.domain.address.export.param.DefaultAddressItemParam;
import com.jd.trade2.core.domain.amount.export.dto.model.AmountCollectionDTO;
import com.jd.trade2.core.domain.coupon.export.dto.model.AbstractCouponItemDTO;
import com.jd.trade2.core.domain.coupon.export.dto.model.CouponCollectionDTO;
import com.jd.trade2.core.domain.freight.export.dto.model.AbstractFreightItemDTO;
import com.jd.trade2.core.domain.invoice.export.dto.model.AbstractInvoiceItemDTO;
import com.jd.trade2.core.domain.invoice.export.param.model.normalinvoice.NormalInvoiceItemParam;
import com.jd.trade2.core.domain.order.export.param.part.business.UserChooseBusinessInfoPartParam;
import com.jd.trade2.core.domain.payment.export.dto.model.AbstractPaymentItemDTO;
import com.jd.trade2.core.domain.paymentpassword.export.dto.AbstractPaymentPasswordItemDTO;
import com.jd.trade2.core.domain.paymentpassword.export.param.AbstractPaymentPasswordItemParam;
import com.jd.trade2.core.domain.presale.export.dto.AbstractPresaleItemDTO;
import com.jd.trade2.core.domain.redpacket.export.dto.model.AbstractRedPacketItemDTO;
import com.jd.trade2.core.domain.shipment.export.dto.AbstractShipmentItemDTO;
import com.jd.trade2.core.domain.shipment.export.param.DefaultShipmentItemParam;
import com.jd.trade2.core.domain.shipment.export.param.thirdparty.ThirdPartyShipmentItemParam;
import com.jd.trade2.core.domain.store.export.dto.model.AbstractStoreItemDTO;
import com.jd.trade2.horizontal.instant.retail.base.export.servicefee.dto.part.HIRBPackingServiceFeePartDTO;
import com.jdh.o2oservice.core.domain.trade.bo.FreezeStateBo;
import com.jdh.o2oservice.core.domain.trade.bo.JdhMedicalPromiseQueryBo;
import com.jdh.o2oservice.core.domain.trade.bo.PromiseBo;
import com.jdh.o2oservice.core.domain.trade.bo.TradeAngelPromiseBo;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.promise.dto.FreezeStateDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TradeParamConverter 交易域的实体转换
 * <AUTHOR>
 * @version 2024/3/4 10:35
 **/
@Mapper(componentModel = "spring")
public interface TradeParamConverter {

    /**
     * 转换交易中地址参数
     *
     * @param abstractAddressItemDTO 中台出参
     * @return AddressInfo
     */
    @Mappings({
            @Mapping(source = "addressId", target = "id"),
            @Mapping(source = "selectedFlag", target = "selected"),
            @Mapping(source = "addressDefaultFlag", target = "addressDefault"),
            @Mapping(source = "consigneeName", target = "name"),
    })
    AddressInfoValueObject convertAddressInfo(AbstractAddressItemDTO abstractAddressItemDTO);

    /**
     * 转换交易中金额参数
     *
     * @param amountCollectionDTO amountCollectionDTO
     * @return AmountInfo
     */
    AmountInfoValueObject convertAmountInfo(AmountCollectionDTO amountCollectionDTO);

    /**
     * 转换交易中优惠卷参数
     *
     * @param abstractCouponItemDTO abstractCouponItemDTO
     * @return CouponInfo
     */
    @Mappings({
            @Mapping(source = "discount", target = "parValue"),
            @Mapping(source = "allowOverlap", target = "overlap"),
            @Mapping(source = "couponType", target = "type"),
            @Mapping(source = "couponPlatform", target = "platform"),
            @Mapping(source = "discountInfoList", target = "discountItemResultList"),
    })
    CouponInfoValueObject convertCouponInfo(AbstractCouponItemDTO abstractCouponItemDTO);

    @Mappings({
            @Mapping(source = "availableCouponList", target = "availableCouponList"),
            @Mapping(source = "unAvailAbleCouponList", target = "unavailableCouponList"),

    })
    CouponInfoRecommendValueObject convertCouponInfoRecommend(CouponCollectionDTO couponCollectionDTO);

    @AfterMapping
    default void afterMappingConvertCouponInfoRecommend(CouponCollectionDTO couponCollectionDTO, @MappingTarget CouponInfoRecommendValueObject couponInfoRecommend) {
        if (couponCollectionDTO == null) {
            return;
        }
        Integer availableCount = couponCollectionDTO.getAvailableCount();
        Integer selectCount = couponCollectionDTO.getSelectCount();
        List<AbstractCouponItemDTO> availableCouponList = couponCollectionDTO.getAvailableCouponList();
        if (CollectionUtils.isEmpty(availableCouponList)) {
            return;
        }
        if (availableCount == null || availableCount == 0) {
            couponInfoRecommend.setAvailableCount(availableCouponList.size());
        }
        if (selectCount == null || selectCount == 0) {
            List<AbstractCouponItemDTO> selectCouponList = availableCouponList.stream().filter(AbstractCouponItemDTO::isSelected).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(selectCouponList)) {
                couponInfoRecommend.setSelectCount(selectCouponList.size());
            }
        }
    }

    /**
     * 转换交易中运费参数
     *
     * @param abstractFreightItemDTO abstractFreightItemDTO
     * @return
     */
    BundleFreightInfoValueObject convertBundleFreightInfo(AbstractFreightItemDTO abstractFreightItemDTO);

    FreightItemInfoValueObject convertFreightItemInfo(AbstractFreightItemDTO abstractFreightItemDTO);

    /**
     * 转换交易中发票参数
     *
     * @param abstractInvoiceItemDTO abstractInvoiceItemDTO
     * @return InvoiceTradeInfo
     */
    InvoiceTradeInfoValueObject convertInvoiceTradeInfo(AbstractInvoiceItemDTO abstractInvoiceItemDTO);

    /**
     * 转换交易中发票参数
     *
     * @param invoiceTradeInfo invoiceTradeInfo
     * @return AbstractInvoiceItemParam
     */
    NormalInvoiceItemParam convertAbstractInvoiceItemDTO(InvoiceTradeInfoValueObject invoiceTradeInfo);

    /**
     * 转换交易中支付方式参数
     *
     * @param abstractPaymentItemDTO abstractPaymentItemDTO
     * @return PaymentInfo
     */
    PaymentInfoValueObject convertPaymentInfo(AbstractPaymentItemDTO abstractPaymentItemDTO);

    /**
     * 转换交易中配送方式参数
     *
     * @param abstractShipmentItemDTO abstractShipmentItemDTO
     * @return ShipmentInfo
     */
    ShipmentInfoValueObject convertShipmentInfo(AbstractShipmentItemDTO abstractShipmentItemDTO);

    /**
     * 转换交易中配送方式参数
     *
     * @param shipmentInfo shipmentInfo
     * @return AbstractShipmentItemParam
     */
    DefaultShipmentItemParam convertDefaultShipmentItemParam(ShipmentInfoValueObject shipmentInfo);

    /**
     * 转换交易中配送方式参数
     *
     * @param shipmentInfo shipmentInfo
     * @return ThirdPartyShipmentItemParam
     */
    ThirdPartyShipmentItemParam convertThirdPartyShipmentItemParam(ShipmentInfoValueObject shipmentInfo);

    /**
     * 地址入参的转换
     *
     * @param addressInfo addressInfo
     * @return AbstractAddressItemParam
     */
    @Mappings({
            @Mapping(source = "id", target = "addressId"),
            @Mapping(source = "selected", target = "selectedFlag"),
            @Mapping(source = "addressDefault", target = "addressDefaultFlag")
    })
    DefaultAddressItemParam convertAbstractAddressItemParam(AddressInfoValueObject addressInfo);


//	@Mappings({
//			@Mapping(source = "calendarTagDTOList", target = "calendarTagPartList"),
//			@Mapping(source = "calendarDayDTOList", target = "calendarDayPartList"),
//	})
//	BatchPromiseInfo convertBatchPromiseInfo(BatchPromiseItemDTO batchPromiseItemDTO);
//
//	@Mappings({
//			@Mapping(source = "calendarOffsetDTOList", target = "calendarOffsetInfoList"),
//	})
//	OffsetPromiseInfo convertOffsetPromiseInfo(OffsetPromiseItemDTO abstractPromiseItemDTO);
//
//	PointPromiseInfo convertPointPromiseInfo(PointPromiseItemDTO abstractPromiseItemDTO);

    List<UserChooseBusinessInfoPartParam> convertUserChooseBusinessInfoList(List<UserChooseBusinessInfoValueObject> userChooseBusinessInfoList);

    /**
     * 门店信息转换
     *
     * @param abstractStoreItemDTO 出参
     * @return StoreInfo
     */
    StoreInfoValueObject convertStoreInfo(AbstractStoreItemDTO abstractStoreItemDTO);

    /**
     * 转换红包的出参
     *
     * @param itemList itemList
     * @return HongBaoItem
     */
    List<HongBaoItemValueObject> convertHongBaoItemList(List<AbstractRedPacketItemDTO> itemList);

    /**
     * 转换支付密码的出参
     *
     * @param abstractPaymentPasswordItemDTO abstractPaymentPasswordItemDTO
     * @return PaymentPassword
     */
    PaymentPasswordValueObject convertPaymentPassword(AbstractPaymentPasswordItemDTO abstractPaymentPasswordItemDTO);

    /**
     * 转换支付密码的入参
     *
     * @param paymentPassword paymentPassword
     * @return AbstractPaymentPasswordItemParam
     */
    AbstractPaymentPasswordItemParam convertAbstractPaymentPasswordItemParam(PaymentPasswordValueObject paymentPassword);


    /**
     * 转换服务费的出参
     *
     * @param hirbPackingServiceFeePartDTOList
     * @return
     */
    List<ServiceFeeDetailInfoValueObject> convertServiceFeeItemInfo(List<HIRBPackingServiceFeePartDTO> hirbPackingServiceFeePartDTOList);

    /**
     * 转换预售的出参
     *
     * @param abstractPresaleItemDTO abstractPresaleItemDTO
     * @return PresaleInfoVo
     */
    PresaleInfoValueObject convertPresaleItemDTO(AbstractPresaleItemDTO abstractPresaleItemDTO);
    /**
     * 转换冻结结果的出参
     *
     * @param freezeStateDto
     * @return FreezeStateBo
     */
    FreezeStateBo convertFreezeStateBo(FreezeStateDto freezeStateDto);

    /**
     * 转换list
     *
     * @param medicalPromiseDTOList
     * @return
     */
    List<JdhMedicalPromiseQueryBo> convertToJdhMedicalPromiseQueryBoList(List<MedicalPromiseDTO> medicalPromiseDTOList);

    /**
     * 转换list
     *
     * @param promiseDtoList
     * @return
     */
    List<PromiseBo> convertToPromiseBoList(List<PromiseDto> promiseDtoList);
    /**
     * 转换冻结结果的出参
     *
     * @param angelWorkDto
     * @return
     */
    TradeAngelPromiseBo convertTradeAngelPromiseBo(AngelWorkDto angelWorkDto);
}
