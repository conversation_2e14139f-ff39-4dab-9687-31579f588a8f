package com.jdh.o2oservice.infrastructure.rpc;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.health.medical.examination.export.dto.AppointmentInfoDTO;
import com.jd.health.medical.examination.export.param.QueryAppointmentInfoParam;
import com.jd.health.medical.examination.export.service.SelfAppointRecordExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.trade.rpc.SelfAppointRecordExportServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description 体检预约
 * @Date 2024/9/19 下午4:09
 * <AUTHOR>
 **/
@Slf4j
@Service
public class SelfAppointRecordExportServiceRpcImpl implements SelfAppointRecordExportServiceRpc {

    @Resource
    private SelfAppointRecordExportService selfAppointRecordExportService;

    /**
     * 体检预约列表
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.SelfAppointRecordExportServiceRpcImpl.queryAppointmentInfoByCondition")
    public List<AppointmentInfoDTO> queryAppointmentInfoByCondition(QueryAppointmentInfoParam param) {
        JsfResult<List<AppointmentInfoDTO>> result = selfAppointRecordExportService.queryAppointmentInfoByCondition(param);
        log.info("SelfAppointRecordExportServiceRpcImpl queryAppointmentInfoByCondition param={}, result", JSON.toJSONString(param), JSON.toJSONString(result));
        if (Objects.nonNull(result) && result.isSuccess()){
            return result.getData();
        }
        return Lists.newArrayList();
    }
}
