package com.jdh.o2oservice.infrastructure.rpc.convert;

/**
 * @ClassName AngelWorkRpcConvert
 * @Description
 * <AUTHOR>
 * @Date 2024/10/29 2:59 PM
 * @Version 1.0
 **/

import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.AngelWorkDetailBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.QueryAngelWorkParam;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkTwoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName AngelWorkRpcConvert
 * @Description
 * <AUTHOR>
 * @Date 2024/10/29 2:59 PM
 * @Version 1.0
 **/
@Mapper
public interface AngelWorkRpcConvert {

    AngelWorkRpcConvert INS = Mappers.getMapper(AngelWorkRpcConvert.class);

    AngelWorkTwoQuery toAngelWorkTwoQuery(QueryAngelWorkParam queryAngelWorkParam);

    List<AngelWorkDetailBO> toAngelWorkDetailBO(List<AngelWorkDetailDto> angelWorkDetailDtos);
}
