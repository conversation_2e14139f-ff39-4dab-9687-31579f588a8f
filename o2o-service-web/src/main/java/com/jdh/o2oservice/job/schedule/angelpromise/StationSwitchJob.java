package com.jdh.o2oservice.job.schedule.angelpromise;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.SupplierTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkShipCancelContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkShipCreateContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName StationSwitchJob
 * @Description
 * <AUTHOR>
 * @Date 2024/12/24 3:53 PM
 * @Version 1.0
 **/
@Slf4j
@Component
public class StationSwitchJob implements ScheduleFlowTask {

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;



    /**
     * 服务站切换三方运力供应商,处理历史数据
     * @param scheduleContext
     * @return
     * @throws Exception
     */
    @Override
    @LogAndAlarm
    public TaskResult doTask(ScheduleContext scheduleContext) throws Exception {
        log.info("StationSwitchJob doTask 我执行了");
        try{

            Date excuteTime = new Date();

            AngelStationPageQuery angelStationPageQuery = new AngelStationPageQuery();
            angelStationPageQuery.setPageSize(10);


            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());

            Date nowStart = TimeUtils.getDateStart(calendar.getTime());
            Date nowEnd = TimeUtils.getDateEnding(calendar.getTime());


            angelStationPageQuery.setQueryUpdateTimeStart(nowStart);
            angelStationPageQuery.setQueryUpdateTimeEnd(nowEnd);

            angelStationPageQuery.setStationModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());

            //昨天修改了服务站的数据-返回第一页
            Page<JdhStation> jdhStationPage =  jdhStationRepository.findPageList(angelStationPageQuery);
            if(jdhStationPage.getPages()==0){
                log.info("StationSwitchJob doTask 昨天没有修改服务站,job终止");
                return TaskResult.success();
            }

            for (int i = 1; i <= jdhStationPage.getPages(); i++) {
                angelStationPageQuery.setPageNum(i);
                Page<JdhStation> loopJdhStationPage =  jdhStationRepository.findPageList(angelStationPageQuery);
                for (JdhStation jdhStation:loopJdhStationPage.getRecords()) {
                    log.info("StationSwitchJob doTask jdhStation={}", JSON.toJSONString(jdhStation));

                    //查询今天的运单是否和服务站的服务商一致
                    List<AngelShip> angelShips = this.queryAngelShip(jdhStation.getStationId(),excuteTime);
                    log.info("StationSwitchJob doTask angelShips={}",JSON.toJSONString(angelShips));
                    if(CollectionUtils.isEmpty(angelShips)){
                        continue;
                    }
                    for (AngelShip angelShip:angelShips) {
                        //判断服务站三方供应商是否和ship一致
                        if(!angelShip.getType().equals(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier()))){
                            //查询实验室信息,获取实验室联系人和电话
                            StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(angelShip.getReceiverId());
                            if(storeInfoBo==null){
                                log.info("StationSwitchJob doTask storeInfoBo为空 该运单不做任何处理");
                                continue;
                            }
                            //取消旧运单,创建新运单
                            ///维护standCancelCode字段
                            angelShip.setStandCancelCode(AngelShipCancelCodeStatusEnum.SYSTEM_CANCEL.getType());
                            angelShipRepository.save(angelShip);

                            AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
                            angelWorkDBQuery.setWorkIds(Collections.singletonList(angelShip.getWorkId()));
                            AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);

                            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
                            medicalPromiseListQuery.setPromiseId(angelWork.getPromiseId());
                            List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
                            if(!medicalPromiseList.get(0).getAngelStationId().equals(jdhStation.getAngelStationId()+"")){
                                log.info("StationSwitchJob doTask 运单id和服务站id不匹配 运单id={} 服务站id",angelShip.getShipId(),jdhStation.getAngelStationId());
                                continue;
                            }
                            try{
                                //取消运单
                                Boolean cancelResult = this.cancelShip(angelShip,angelWork);
                                if(cancelResult){
                                    //创建运单
                                    this.callShip(angelShip,angelWork,jdhStation,storeInfoBo,medicalPromiseList.get(0).getAngelStationId());
                                }
                            }catch (Exception e){
                                log.info("StationSwitchJob doTask 三方运力调用异常",e);
                                return TaskResult.fail(e.getMessage());
                            }
                        }
                    }
                }
            }

        }catch (Exception e){
            log.info("StationSwitchJob doTask 异常",e);
            return TaskResult.fail(e.getMessage());
        }
        return TaskResult.success();
    }

    /**
     * 创建运单
     * @param angelShip
     * @param angelWork
     * @return
     */
    private AngelShip callShip(AngelShip angelShip,AngelWork angelWork,JdhStation jdhStation,StoreInfoBo storeInfoBo,String angelStationId){

        AngelWorkShipCreateContext shipCreateContext = new AngelWorkShipCreateContext();
        shipCreateContext.setVerticalCode(angelWork.getVerticalCode());
        shipCreateContext.setServiceType(angelWork.getServiceType());
        shipCreateContext.setWorkId(angelWork.getWorkId());
        shipCreateContext.setShopNo(angelStationId);
        shipCreateContext.setProviderShopNo(jdhStation.getStationId());
        shipCreateContext.setIsPrepay(CommonConstant.ZERO);
        shipCreateContext.setReceiverAddress(angelShip.getReceiverFullAddress());
        shipCreateContext.setReceiverName(angelShip.getReceiverName());
        shipCreateContext.setReceiverPhone(storeInfoBo.getStorePhone());
        shipCreateContext.setCargoWeight(0.1);
        shipCreateContext.setSupplierAddress(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getFullAddress());
        shipCreateContext.setSupplierPhone(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointPhone());
        shipCreateContext.setSupplierName(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointName());
        shipCreateContext.setAngelType(jdhStation.getAngelType());
        shipCreateContext.setDeliveryType(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier()));
        shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier())));
        shipCreateContext.setIsDirectDelivery(CommonConstant.ZERO);
        shipCreateContext.setAngelWork(angelWork);
        shipCreateContext.setInfo(angelShip.getJdhAngelShipExtVo().getShipRemark());
        shipCreateContext.setShipTaskList(angelShip.getJdhAngelShipExtVo().getShipTaskList());
        shipCreateContext.setInvokeRecall(false);
        return angelShipDomainService.createAngelShip(shipCreateContext);
    }

    /**
     * 取消运单
     * @param angelShip
     * @param angelWork
     */
    private Boolean cancelShip(AngelShip angelShip,AngelWork angelWork){
        //取消运单
        AngelWorkShipCancelContext cancelShipContext = AngelWorkShipCancelContext.builder().build();

        cancelShipContext.setWorkId(angelWork.getWorkId());
        cancelShipContext.setShipId(angelShip.getShipId());
        cancelShipContext.setStandCancelCode(AngelShipCancelCodeStatusEnum.SYSTEM_CANCEL.getType());

        return angelShipDomainService.cancelShip(cancelShipContext);
    }

    /**
     * 根据实验室id查询骑手运单信息
     * @param stationId
     * @return
     */
    private List<AngelShip> queryAngelShip(String stationId,Date excuteTime){
        log.info("StationSwitchJob doTask queryAngelShip jdhStation={} excuteTime={}",stationId,excuteTime);
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setReceiverId(stationId);
        angelShipDBQuery.setTypes(Arrays.asList(DeliveryTypeEnum.RIDER_DELIVERY.getType(),DeliveryTypeEnum.SHUNFENG_DELIVERY.getType()));
        angelShipDBQuery.setQueryPlanCallTimeStart(excuteTime);
        angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(), AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus()));
        return angelShipRepository.findList(angelShipDBQuery);
    }
}
