package com.jdh.o2oservice.job.support;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * work扫描，每晚八点提醒第二天有待服务的护士
 *
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class AngelServicingHourBeforeRemindScanJob implements SimpleJob {

    /**
     *
     */
    @Resource
    @Lazy
    private AngelWorkRepository angelWorkRepository;
    @Resource
    @Lazy
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    @Lazy
    private ReachApplication reachApplication;


    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        log.info("AngelServicingHourBeforeRemindScanJob start");
        Set<String> businessCode = Sets.newHashSet(BusinessModeEnum.ANGEL_CARE.getCode(), BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        List<Integer> workStatus = Lists.newArrayList(AngelWorkStatusEnum.RECEIVED.getType());

        List<JdhVerticalBusiness> businessList = verticalBusinessRepository.findByBusinessMode(businessCode);
        if (CollectionUtils.isEmpty(businessList)) {
            return;
        }
        List<String> verticalCodes = businessList.stream().map(JdhVerticalBusiness::getVerticalCode).collect(Collectors.toList());

        LocalDate curDay = LocalDate.now();
        int hour = LocalTime.now().getHour();

        LocalDateTime startTime = curDay.atTime(hour, 0, 0).plusHours(2);
        LocalDateTime endTime = curDay.atTime(hour, 0, 0).plusHours(3);

        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setStatusList(workStatus);
        angelWorkDBQuery.setVerticalCodes(verticalCodes);
        angelWorkDBQuery.setServiceStartTimeBegin(TimeUtils.localDateTimeToDate(startTime));
        angelWorkDBQuery.setServiceStartTimeEnd(TimeUtils.localDateTimeToDate(endTime));
        angelWorkDBQuery.setPageNum(1);
        angelWorkDBQuery.setPageSize(100);

        while (true){
            Page<AngelWork> page = angelWorkRepository.pageScanJob(angelWorkDBQuery);
            if (page == null || CollectionUtils.isEmpty(page.getRecords())){
                log.info("AngelServicingHourBeforeRemindScanJob end");
                return;
            }
            for (AngelWork angelWork : page.getRecords()) {
                try {
                    // 在服务时间前一个小时发出事件，触发短信提醒
                    Event event = new Event(angelWork, AngelWorkEventTypeEnum.WORK_SERVICING_HOUR_BEFORE_REMIND, null, null);
                    LocalDateTime publishTime = TimeUtils.dateToLocalDateTime(angelWork.getWorkStartTime());
                    event.setPublishTime(publishTime.plusHours(-1));
                    reachApplication.submitTask(event);
                }catch (Exception e){
                    log.error("AngelServicingHourBeforeRemindScanJob error workId={}", angelWork.getWorkId(), e);
                }
            }
            angelWorkDBQuery.setPageNum((int)(page.getCurrent()+1));
        }


    }

}
