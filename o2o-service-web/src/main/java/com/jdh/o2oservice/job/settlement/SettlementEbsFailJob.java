package com.jdh.o2oservice.job.settlement;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.service.SettlementEbsApplication;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.export.settlement.dto.JdhSettlementEbsDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 重试EBS发送失败消息
 *
 * <AUTHOR>
 * @date 2024/05/31
 */
@Slf4j
@Component
public class SettlementEbsFailJob implements SimpleJob {
    /**
     * ebs
     */
    @Resource
    @Lazy
    private SettlementEbsApplication settlementEbsApplication;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("[RetryFailEbsJob.execute]，重试EBS发送失败消息任务开始");
        JdhSettlementEbs totalJdhSettlementEbs = new JdhSettlementEbs();
        totalJdhSettlementEbs.setSendStatus(-1);
        totalJdhSettlementEbs.setPageNum(1);
        totalJdhSettlementEbs.setPageSize(100);
        PageDto<JdhSettlementEbsDto> totalDto = settlementEbsApplication.queryPage(totalJdhSettlementEbs);
        log.info("[RetryFailEbsJob.execute]，重试EBS发送失败消息任务结束 totalDto={}", JSON.toJSONString(totalDto));
        if (totalDto != null && CollUtil.isNotEmpty(totalDto.getList())) {
            for (int i=1; i<=totalDto.getTotalPage(); i++) {
                JdhSettlementEbs jdhSettlementEbs = new JdhSettlementEbs();
                jdhSettlementEbs.setSendStatus(-1);
                jdhSettlementEbs.setPageNum(i);
                jdhSettlementEbs.setPageSize(100);
                PageDto<JdhSettlementEbsDto> pageDto = settlementEbsApplication.queryPage(jdhSettlementEbs);
                if (pageDto == null || CollUtil.isEmpty(pageDto.getList())) {
                    break;
                }
                for (JdhSettlementEbsDto dto : pageDto.getList()) {
                    log.info("[RetryFailEbsJob.execute]，重试EBS发送失败消息-入参 dto={}", JSON.toJSONString(dto));
                    JdhSettlementEbs sendEbs = new JdhSettlementEbs();
                    sendEbs.setPreId(dto.getPreId());
                    Boolean ret = Boolean.FALSE;
                    String errorMsg = "";
                    try {
                        ret = settlementEbsApplication.retrySendToEbs(sendEbs);
                    } catch (Exception e) {
                        log.error("[RetryFailEbsJob.execute]，重试EBS发送失败消息-异常", e);
                        errorMsg = e.getMessage();
                    }
                    log.info("[RetryFailEbsJob.execute]，重试EBS发送失败消息-结果 ret={}", ret);
                    if (!Boolean.TRUE.equals(ret)) {
                        UmpUtil.showWarnMsg(UmpKeyEnum.RETRY_EBS_WARN, "preId=[" + sendEbs + "], errorMsg={" + errorMsg + "}");
                    }
                }
            }
        }
        log.info("[RetryFailEbsJob.execute]，重试EBS发送失败消息任务结束");
    }
}
