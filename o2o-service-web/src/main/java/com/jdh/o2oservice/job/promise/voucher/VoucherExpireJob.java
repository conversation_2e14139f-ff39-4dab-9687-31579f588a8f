package com.jdh.o2oservice.job.promise.voucher;

import com.alibaba.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.export.promise.cmd.ExpireVoucherCmd;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * VoucherExpireJob
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Slf4j
@Component
public class VoucherExpireJob implements SimpleJob {

    /**
     * jdhVoucherApplication
     */
    @Resource
    @Lazy
    private VoucherApplication voucherApplication;

    /**
     * 执行
     *
     * @param shardingContext 切分上下文
     */
    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        int pageSize = 50;

        LocalDateTime start = LocalDate.now().plusDays(-1).atStartOfDay();
        LocalDateTime end = LocalDate.now().atStartOfDay();

        List<Integer> statusNotInList = Arrays.asList(JdhVoucherStatusEnum.COMPLETE.getStatus(), JdhVoucherStatusEnum.INVALID.getStatus());
        VoucherPageRequest pageRequest = new VoucherPageRequest();
        pageRequest.setStatusNotInList(statusNotInList);
        Response<PageDto<VoucherDto>> page = null;

        // 按照时间段每十分钟扫描一批数据，避免每次分页查询扫描的同条数过多导致的慢SQL
        while (start.isBefore(end)) {
            int pageNum = 1;
            LocalDateTime endTime = start.plusMinutes(10);
            log.info("VoucherExpireJob -> execute start={}, endTime={}", start, endTime);
            // start <= expireDate < endTime
            pageRequest.setExpireDateGe(TimeUtils.localDateTimeToDate(start));
            pageRequest.setExpireDateLt(TimeUtils.localDateTimeToDate(endTime));
            do {
                pageRequest.setPageNum(pageNum);
                pageRequest.setPageSize(pageSize);
                page = voucherApplication.pageQueryVoucher(pageRequest);
                log.info("VoucherExpireJob -> execute pagination:{}", JSON.toJSONString(page));
                if (Objects.nonNull(page) && Objects.nonNull(page.getData())) {
                    List<VoucherDto> voucherDtoList = page.getData().getList();
                    voucherDtoList.forEach(this::expireVoucher);
                }
                pageNum++;
                // 返回数据为空，或者页码小于总页数，则退出循环
            } while (Objects.nonNull(page) && Objects.nonNull(page.getData()) && pageNum <= page.getData().getTotalPage());
            // 重置start
            start = endTime;
        }
    }


    /**
     * 过期服务单
     *
     * @param voucherDto voucherDto
     */
    private void expireVoucher(VoucherDto voucherDto) {
        try {
            ExpireVoucherCmd cmd = ExpireVoucherCmd.builder()
                    .voucherId(voucherDto.getVoucherId()).build();
            if (!Objects.equals(voucherDto.getStatus(), JdhVoucherStatusEnum.EXPIRED.getStatus())) {
                voucherApplication.expireVoucher(cmd);
            }
        } catch (Exception e) {
            log.error("VoucherExpireJob -> expireVoucher exception voucherDto:{}", JSON.toJSONString(voucherDto), e);
        }

    }
}
