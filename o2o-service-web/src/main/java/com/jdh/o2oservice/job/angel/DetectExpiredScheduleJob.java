package com.jdh.o2oservice.job.angel;

import com.alibaba.fastjson.JSON;

import com.jdh.o2oservice.application.angel.service.AngelScheduleApplication;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: jdh-o2o-service
 * @description: 定时清理状态为已过期以及逻辑删除排班
 * @author: luxingchen3
 * @create: 2024-04-25 16:04
 **/
@Slf4j
@Component
public class DetectExpiredScheduleJob  implements SimpleJob {
    @Resource
    @Lazy
    private AngelScheduleApplication angelScheduleApplication;
    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("[DetectExpiredScheduleJob.execute]，检测过期排班任务开始");
         Integer res=angelScheduleApplication.detectExpiredSchedule();
        log.info("[DetectExpiredScheduleJob.execute]，检测过期排班任务结束 res", JSON.toJSONString(res));
    }
}
