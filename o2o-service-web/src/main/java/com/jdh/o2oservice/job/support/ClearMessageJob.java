package com.jdh.o2oservice.job.support;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachMessagePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachTaskPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachMessagePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTaskPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时清理消息
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class ClearMessageJob implements SimpleJob {


    @Resource
    @Lazy
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private AngelRepository angelRepository;
    @Resource
    @Lazy
    private ReachApplication reachApplication;
    /**
     *
     */
    @Resource
    @Lazy
    private JdhReachMessagePoMapper jdhReachMessagePoMapper;
    /**
     *
     */
    @Resource
    @Lazy
    private JdhReachTaskPoMapper jdhReachTaskPoMapper;

    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {

        try {
            removeCommonMessages();
            removeBoxMessages();
        }catch (Exception e){
            log.error("ClearMessageJob->execute error", e);
        }


        try{
            removeReachTask();
        }catch (Exception e){
            log.error("ClearMessageJob->removeReachTask error", e);
        }
    }

    /**
     * 删除通用的消息记录,删除15天前的数据
     */
    private void removeCommonMessages() {
        log.info("ClearMessageJob->removeCommonMessages start");

        LocalDateTime start = LocalDate.now().plusDays(-16).atStartOfDay();
        LocalDateTime end = LocalDate.now().plusDays(-15).atStartOfDay();
        List<Integer> reachTypes = Lists.newArrayList(ReachTypeEnum.REACH_SMS.getCode(), ReachTypeEnum.REACH_EMAIL.getCode()
                , ReachTypeEnum.REACH_JM.getCode(), ReachTypeEnum.PUSH.getCode(), ReachTypeEnum.JM_MESSAGE.getCode()
                , ReachTypeEnum.MASTER_APP_MESSAGE.getCode(), ReachTypeEnum.STORE_PROGRAM_MQ_MESSAGE.getCode());

        while (start.isBefore(end)) {
            LocalDateTime endTime = start.plusHours(1);
            log.info("ClearMessageJob->removeCommonMessages startTime={}, endTime={}", start, endTime);

            List<Long> messageIds = jdhReachMessagePoMapper.listMessageIds(TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(endTime), reachTypes);

            if (CollectionUtils.isNotEmpty(messageIds)) {
                log.info("ClearMessageJob->removeCommonMessages start 开始时间：{}, 删除条数={}", start, messageIds.size());

                List<List<Long>> ids = Lists.partition(messageIds, 200);
                ids.forEach(id -> {
                    try {
                        LambdaQueryWrapper<JdhReachMessagePo> deleteMessage = new LambdaQueryWrapper<JdhReachMessagePo>();
                        deleteMessage.in(JdhReachMessagePo::getMessageId, id);
                        jdhReachMessagePoMapper.delete(deleteMessage);
                        Thread.sleep(500L);
                    } catch (Exception e) {
                        log.error("ClearMessageJob->removeCommonMessages start", e);
                    }
                });
            }
            start = endTime;
        }
    }


    /**
     * 删除消息盒子里的消息
     */
    private void removeBoxMessages() {
        log.info("ClearMessageJob->removeBoxMessages start");
        LocalDateTime start = LocalDate.now().plusDays(-91).atStartOfDay();;
        LocalDateTime end = LocalDate.now().plusDays(-90).atStartOfDay();;
        List<Integer> reachTypes = Lists.newArrayList(ReachTypeEnum.APP_NOTIFY.getCode());
        while (start.isBefore(end)) {
            LocalDateTime endTime = start.plusHours(1);
            log.info("ClearMessageJob->removeBoxMessages startTime={}, endTime={}", start, endTime);
            List<Long> messageIds = jdhReachMessagePoMapper.listMessageIds(TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(endTime), reachTypes);

            if (CollectionUtils.isNotEmpty(messageIds)) {
                log.info("ClearMessageJob->removeBoxMessages start 开始时间：{}, 删除条数={}", start, messageIds.size());
                List<List<Long>> ids = Lists.partition(messageIds, 200);
                ids.forEach(id -> {
                    try {
                        LambdaQueryWrapper<JdhReachMessagePo> deleteMessage = new LambdaQueryWrapper<JdhReachMessagePo>();
                        deleteMessage.in(JdhReachMessagePo::getMessageId, id);
                        jdhReachMessagePoMapper.delete(deleteMessage);
                        Thread.sleep(500L);
                    } catch (Exception e) {
                        log.error("ClearMessageJob->removeBoxMessages start", e);
                    }
                });
            }
            start = endTime;
        }
    }

    private void removeReachTask() {
        log.info("ClearMessageJob->removeReachTask start");
        LocalDateTime start = LocalDate.now().plusDays(-91).atStartOfDay();;
        LocalDateTime end = LocalDate.now().plusDays(-90).atStartOfDay();;
        while (start.isBefore(end)) {
            LocalDateTime endTime = start.plusHours(1);
            log.info("ClearMessageJob->removeReachTask startTime={}, endTime={}", start, endTime);
            List<Long> taskIds = jdhReachTaskPoMapper.listTaskIds(TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(endTime));

            if (CollectionUtils.isNotEmpty(taskIds)) {
                log.info("ClearMessageJob->removeReachTask start 开始时间：{}, 删除条数={}", start, taskIds.size());
                List<List<Long>> ids = Lists.partition(taskIds, 200);
                ids.forEach(id -> {
                    try {
                        LambdaQueryWrapper<JdhReachTaskPo> deleteMessage = new LambdaQueryWrapper<JdhReachTaskPo>();
                        deleteMessage.in(JdhReachTaskPo::getTaskId, id);
                        jdhReachTaskPoMapper.delete(deleteMessage);
                        Thread.sleep(500L);
                    } catch (Exception e) {
                        log.error("ClearMessageJob->removeReachTask start", e);
                    }
                });
            }
            start = endTime;
        }
    }
}
