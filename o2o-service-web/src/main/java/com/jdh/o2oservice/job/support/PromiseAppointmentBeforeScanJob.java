package com.jdh.o2oservice.job.support;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromiseHistoryPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromiseHistoryPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * 预约时间前一天扫描，如果预约成功结果回调的时间是预约时间的前一天，则不处理，因为预约成功回调的时候已经触发了短信任务
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class PromiseAppointmentBeforeScanJob implements SimpleJob {

    /**
     *
     */
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    /**
     *
     */
    @Resource
    @Lazy
    private ReachApplication reachApplication;
    /**
     *
     */
    @Resource
    private JdhPromiseHistoryPoMapper jdhPromiseHistoryPoMapper;

    /**
     * 达达交接统一处理器
     */
    @Resource
    private MapperHandler mapperHandler;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;


    @Override
    public void execute(ShardingContext shardingContext) {

        try {
            scanBeforeDay();
        }catch (Exception e){
            log.error("PromiseAppointmentBeforeScanJob->execute error", e);
        }
    }

    /**
     * 预约时间前一天提醒扫描
     * （1）扫描预约时间符合的履约单数据；
     * （2）对于定时任务执行当日预约成功的数据不进行处理（因为当日预约成功回调的时候已经触发了短信任务）。
     */
    public void scanBeforeDay(){

        List<Integer> promiseStatus = Lists.newArrayList(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus()
                , JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus(), JdhPromiseStatusEnum.MODIFY_FAIL.getStatus()
                ,JdhPromiseStatusEnum.CANCEL_FAIL.getStatus());

        LocalDateTime startTime = LocalDate.now().plusDays(1).atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().plusDays(2).atTime(0, 0, 0);
        int pageNum = 0;
        int pageSize = 100;
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<JdhPromisePo> pos = jdhPromisePoMapper.appointmentDayBefore(promiseStatus, TimeUtils.localDateTimeToDate(startTime),
                    TimeUtils.localDateTimeToDate(endTime), "xfylPop", pageNum*pageSize, pageSize);
            if (CollectionUtils.isEmpty(pos)) {
                log.info("PromiseAppointmentBeforeScanJob scanBeforeDay promise is empty");
                return;
            }

            log.info("PromiseAppointmentBeforeScanJob scanBeforeDay size={}", pos.size());
            List<Long> promiseIds = pos.stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());
            LocalDateTime historyEnd = LocalDate.now().plusDays(1).atTime(0, 0, 0);
            LocalDateTime historyStart = LocalDate.now().atTime(0, 0, 0);

            // 过滤调当日预约第二天的预约数据
            List<Integer> afterStatus = Lists.newArrayList(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus()
                    , JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus());
            List<JdhPromiseHistoryPo> historyPos = jdhPromiseHistoryPoMapper.listByTimeRange(promiseIds,  TimeUtils.localDateTimeToDate(historyStart), TimeUtils.localDateTimeToDate(historyEnd), afterStatus);

            Map<Long, List<JdhPromiseHistoryPo>> historyGroup = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(historyPos)){
                historyGroup = historyPos.stream().collect(Collectors.groupingBy(JdhPromiseHistoryPo::getPromiseId));
            }
            for (JdhPromisePo po : pos) {

                // 达达交接是否执行开关
                if (Boolean.TRUE.equals(duccConfig.getPopForwardJobGlobalSwitch()) && Boolean.TRUE.equals(mapperHandler.handler(String.valueOf(po.getPromiseId()), "promiseIdMapper"))) {
                    log.info("PromiseAppointmentBeforeScanJob->scanBeforeDay 达达交接命中规则 promiseId={}", po.getPromiseId());
                    continue;
                }

                // 非任务执行日预约成功的单子，才需要触发任务
                if (!historyGroup.containsKey(po.getPromiseId())){
                    JdhPromise promise = new JdhPromise();
                    promise.setPromiseId(po.getPromiseId());
                    Event event = new Event(promise, PromiseEventTypeEnum.APPOINTMENT_BEFORE_DAY_REMIND, null, null);
                    reachApplication.submitTask(event);
                }
            }
            pageNum++;
        }
    }
}
