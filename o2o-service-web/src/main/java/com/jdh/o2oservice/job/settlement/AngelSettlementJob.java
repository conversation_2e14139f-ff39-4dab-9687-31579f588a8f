//package com.jdh.o2oservice.job.settlement;
//
//import cn.hutool.core.collection.CollUtil;
//import com.jd.medicine.base.common.util.JsonUtil;
//import com.jd.medicine.base.common.util.DateUtil;
//import com.jdh.o2oservice.application.angel.service.AngelApplication;
//import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
//import com.jdh.o2oservice.base.ducc.DuccConfig;
//import com.jdh.o2oservice.base.exception.BusinessException;
//import com.jdh.o2oservice.base.factory.GenerateIdFactory;
//import com.jdh.o2oservice.common.result.response.PageDto;
//import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
//import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
//import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
//import com.jdh.o2oservice.core.domain.settlement.vo.AngelAddAccountAmountVo;
//import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
//import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
//import com.jdh.o2oservice.export.angel.query.AngelRequest;
//import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
//import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.elasticjob.api.ShardingContext;
//import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 全职护士保底工资
// *
// * <AUTHOR>
// * @date 2024/05/31
// */
//@Slf4j
//@Component
//public class AngelSettlementJob implements SimpleJob {
//
//    /**
//     * serviceSettleReadApplication
//     */
//    @Resource
//    @Lazy
//    private JdServiceSettleReadApplication serviceSettleReadApplication;
//    /** */
//    @Resource
//    DuccConfig duccConfig;
//    /** */
//    @Resource
//    AngelApplication angelApplication;
//    /**
//     *
//     */
//    @Resource
//    private GenerateIdFactory generateIdFactory;
//    /**
//     * hySettleRpc
//     */
//    @Autowired
//    private HySettleRpc hySettleRpc;
//
//
//
//    /**
//     *
//     * @param shardingContext
//     */
//    @Override
//    public void execute(ShardingContext shardingContext) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.MONTH, 0);
//        calendar.set(Calendar.DAY_OF_MONTH, 1);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.SECOND, 0);
//
//        Calendar calendarEnnd = Calendar.getInstance();
//        calendarEnnd.add(Calendar.MONTH, 1);
//        calendarEnnd.set(Calendar.DAY_OF_MONTH, 1);
//        calendarEnnd.set(Calendar.HOUR_OF_DAY, 0);
//        calendarEnnd.set(Calendar.MINUTE, 0);
//        calendarEnnd.set(Calendar.SECOND, 0);
//        AngelSettleQuery angelSettlQuery = new AngelSettleQuery();
//        angelSettlQuery.setJobNature(1);
//        angelSettlQuery.setSettleStatus(SettleStatusEnum.SETTLED.getType());
//        angelSettlQuery.setSettleTimeStart(calendar.getTime());
//        angelSettlQuery.setSettleTimeEnd(calendarEnnd.getTime());
//
//        log.info("[AngelSettlementJob.execute],全职护士保底工资,calendar={}", DateUtil.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
//        log.info("[AngelSettlementJob.execute],全职护士保底工资,calendarEnnd={}", DateUtil.formatDate(calendarEnnd.getTime(),"yyyy-MM-dd HH:mm:ss"));
//
//        angelSettlQuery.setPageNum(1);
//        angelSettlQuery.setPageSize(10000);
//
//        Set<Long> angelBlackListSet = duccConfig.getAngelBlackListSet();
//        String angelBasicSalary = duccConfig.getAngelBasicSalary();
//
//        Map<String, String> angelSettleTypeMap = duccConfig.getAngelSettleTypeMap();
//        String settleDeLayTime = angelSettleTypeMap.get("1");
//        calendarEnnd.set(Calendar.DAY_OF_MONTH, Integer.parseInt(settleDeLayTime));
//        calendarEnnd.set(Calendar.HOUR_OF_DAY, 0);
//        calendarEnnd.set(Calendar.MINUTE, 0);
//        calendarEnnd.set(Calendar.SECOND, 0);
//        Date expectSettleTime = calendar.getTime();
//
//        BigDecimal angelBasicSalaryAmount = new BigDecimal(angelBasicSalary);
//        log.info("[AngelSettlementJob.execute],全职护士保底工资,angelSettlQuery={}",angelSettlQuery);
//        PageDto<AngelSettlementDto> totalDto = serviceSettleReadApplication.querySettlementPageBySettleTime(angelSettlQuery);
//        if (totalDto != null && CollUtil.isNotEmpty(totalDto.getList())){
//            List<AngelSettlementDto> list = totalDto.getList();
//            Map<Long,List<AngelSettlementDto>> map = list.stream().collect(Collectors.groupingBy(AngelSettlementDto::getAngelId));
//            for(Map.Entry<Long, List<AngelSettlementDto>> entry : map.entrySet()){
//                if(angelBlackListSet.contains(entry.getKey())){
//                    log.info("AngelSettlementJob -> 全职护士保底工资，不结算 angelId:{}", entry.getKey());
//                    continue;
//                }
//
//                List<AngelSettlementDto> angelSettlementList = entry.getValue();
//                BigDecimal settleAmount = angelSettlementList.stream().map(AngelSettlementDto::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                if(settleAmount.compareTo(angelBasicSalaryAmount) < 0){
//                    BigDecimal subtractAMount = angelBasicSalaryAmount.subtract(settleAmount);
//                    this.addWithdrawAccountAmount(entry.getKey(),subtractAMount,expectSettleTime);
//                }
//            }
//        }
//        log.info("[AngelSettlementJob.execute]，end");
//    }
//
//    /**
//     *
//     * @param angelId
//     * @param settleAmout
//     * @param expectSettleTime
//     * @return
//     */
//    private void addWithdrawAccountAmount(Long angelId,BigDecimal settleAmout,Date expectSettleTime){
//        AngelAddAccountAmountVo angelAddAccountAmountVo = new AngelAddAccountAmountVo();
//        angelAddAccountAmountVo.setAccountId(getNethpDocId(angelId));
//        angelAddAccountAmountVo.setAmount( settleAmout );
//        angelAddAccountAmountVo.setRecordTime(new Date());
//        angelAddAccountAmountVo.setWithdrawalTime(expectSettleTime);
//        angelAddAccountAmountVo.setChangeBusinessId(generateIdFactory.getIdStr());
//        angelAddAccountAmountVo.setFeeType(SettleItemTypeEnum.ADJUST.getHuYiFeeType());
//        angelAddAccountAmountVo.setOutBusinessData(JsonUtil.toJSONString(angelAddAccountAmountVo));
//        Boolean result = hySettleRpc.addWithdrawAccountAmount(angelAddAccountAmountVo);
//        if(!result){
//            log.error("AngelSettlementJob.addWithdrawAccountAmount,angelId={},settleAmout={}", angelId, settleAmout);
//        }
//    }
//
//    /**
//     *
//     * @param angelId
//     * @return
//     */
//    private Long getNethpDocId(Long angelId){
//        AngelRequest angelRequest = new AngelRequest();
//        angelRequest.setAngelId(angelId);
//        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
//        if(Objects.nonNull(jdhAngelDto) && Objects.nonNull(jdhAngelDto.getNethpDocId())){
//            return jdhAngelDto.getNethpDocId();
//        }else{
//            throw new BusinessException(TradeErrorCode.ANGEL_INFO_NULL);
//        }
//    }
//}
