package com.jdh.o2oservice.job.provider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.provider.service.ProviderPromiseApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.core.domain.provider.enums.JdhProviderJobProcessStatus;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.export.provider.cmd.ProviderPromiseRejectCmd;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhProviderJobPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhProviderJobPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商家领域预约单自动处理，超过24小时处于待确认、驳回的预约单自动驳回。
 * @author: yangxiyu
 * @date: 2024/3/29 11:18 上午
 * @version: 1.0
 */
@Slf4j
@Component
public class ProviderPromiseAutoProcessJob implements SimpleJob {

    /**
     * 中间态
     */
    private static final Set<Integer> PROMISE_STATUS_MIDDLE = Sets.newHashSet(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus(),
            JdhPromiseStatusEnum.MODIFY_ING.getStatus(), JdhPromiseStatusEnum.CANCEL_ING.getStatus());
    /**
     * providerPromiseRepository
     */
    @Resource
    @Lazy
    private JdhProviderJobPoMapper jdhProviderJobPoMapper;
    /** */
    @Resource
    @Lazy
    private JdhPromisePoMapper jdhPromisePoMapper;
    /** 避免循环依赖需要加@Lazy，elasticjob和@JmqProducer都依赖了BeanPostProcessor */
    @Resource
    @Lazy
    private ProviderPromiseApplication providerPromiseApplication;

    /**
     * 达达交接统一处理器
     */
    @Resource
    private MapperHandler mapperHandler;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 执行
     *
     * @param shardingContext 切分上下文
     */
    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        log.info("ProviderPromiseAutoProcessJob->execute start");
        Page<JdhProviderJobPo> param = new Page<>(1, 50);
        LambdaQueryWrapper<JdhProviderJobPo> queryWrapper = Wrappers.lambdaQuery();
        LocalDateTime cur = LocalDateTime.now();
        LocalDateTime before = cur.plusDays(-1L);
        Date start = TimeUtils.localDateTimeToDate(before);
        Date end = TimeUtils.localDateTimeToDate(cur.plusDays(-7L));
        queryWrapper.in(JdhProviderJobPo::getProcessStatus, Lists.newArrayList(JdhProviderJobProcessStatus.MODIFY_WAITING_PROCESS.getStatus(),
                    JdhProviderJobProcessStatus.SUBMIT_WAITING_PROCESS.getStatus(),
                    JdhProviderJobProcessStatus.CANCEL_WAITING_PROCESS.getStatus()))
                .eq(JdhProviderJobPo::getYn, YnStatusEnum.YES.getCode())
                .ge(JdhProviderJobPo::getUpdateTime, end)
                .le(JdhProviderJobPo::getUpdateTime, start);
        Page<JdhProviderJobPo> page = null;
        List<JdhPromisePo> promisePos = null;
        do{
            try {
                log.info("ProviderPromiseAutoProcessJob->execute queryWrapper={}", queryWrapper);
                page = jdhProviderJobPoMapper.selectPage(param, queryWrapper);
                if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                    return;
                }

                List<Long> promiseIds = page.getRecords().stream().map(JdhProviderJobPo::getPromiseId).collect(Collectors.toList());
                LambdaQueryWrapper<JdhPromisePo> listQuery = Wrappers.lambdaQuery();
                listQuery.in(JdhPromisePo::getPromiseId, promiseIds);
                listQuery.eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());
                // 仅仅处理中间态的promise，存在预约单商家没有确认，但是promise已经核销的场景
                listQuery.in(JdhPromisePo::getPromiseStatus, PROMISE_STATUS_MIDDLE);
                log.info("ProviderPromiseAutoProcessJob->execute listQuery={}", listQuery);
                promisePos = jdhPromisePoMapper.selectList(listQuery);
            }catch (Exception e){
                log.error("ProviderPromiseAutoProcessJob->execute query error, msg={}", e.getMessage());
                UmpUtil.showWarnMsg(UmpKeyEnum.PROVIDER_AUTO_PROCESS_JOB_EXECUTE_ERROR);
                return;
            }

            Map<Long, Long> channelMap = promisePos.stream().collect(Collectors.toMap(JdhPromisePo::getPromiseId, JdhPromisePo::getChannelNo));
            for (JdhProviderJobPo record : page.getRecords()) {
                if (!channelMap.containsKey(record.getPromiseId())){
                    log.info("ProviderPromiseAutoProcessJob->execute abandon promiseId={}", record.getPromiseId());
                    continue;
                }

                // 达达交接是否执行开关
                if (Boolean.TRUE.equals(duccConfig.getPopForwardJobGlobalSwitch()) && Boolean.TRUE.equals(mapperHandler.handler(String.valueOf(record.getPromiseId()), "promiseIdMapper"))) {
                    log.info("ProviderPromiseAutoProcessJob->execute 达达交接命中规则 promiseId={}", record.getPromiseId());
                    continue;
                }

                try {
                    ProviderPromiseRejectCmd cmd = new ProviderPromiseRejectCmd();
                    cmd.setPromiseId(String.valueOf(record.getPromiseId()));
                    cmd.setProcessStatus(String.valueOf(record.getProcessStatus()));
                    cmd.setChannelNo(channelMap.get(record.getPromiseId()));
                    cmd.setReason("商家超过24小时未审核自动驳回，请重新提交预约");
                    providerPromiseApplication.reject(cmd);
                }catch (Exception e){
                    log.error("ProviderPromiseAutoProcessJob->execute error, promiseId={}, msg={}", record.getPromiseId(), e.getMessage());
                    UmpUtil.showWarnMsg(UmpKeyEnum.PROVIDER_AUTO_PROCESS_JOB_EXECUTE_ERROR);
                }
            }
            param.setCurrent(param.getCurrent() + 1);
        }while (page.hasNext());

        log.info("ProviderPromiseAutoProcessJob->execute ending");

    }
}
