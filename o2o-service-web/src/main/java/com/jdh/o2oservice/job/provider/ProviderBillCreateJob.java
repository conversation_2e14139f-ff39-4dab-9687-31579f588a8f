package com.jdh.o2oservice.job.provider;

import IceInternal.Ex;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.provider.service.ProviderBillApplication;
import com.jdh.o2oservice.export.provider.cmd.JdhProviderBillCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * @Description: 实验室账单生成Job
 * @Author: wangpengfei144
 * @Date: 2024/6/15
 */
@Slf4j
@Component
public class ProviderBillCreateJob implements SimpleJob {

    /**
     * 账单
     */
    @Autowired
    @Lazy
    private ProviderBillApplication providerBillApplication;


    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        log.info("ProviderBillCreateJob,日账单定时任务开始，time={}", System.currentTimeMillis());
        try {
            JdhProviderBillCmd jdhProviderBillCmd = new JdhProviderBillCmd();

            LocalDate startDate = LocalDate.now().minusDays(1L);
            LocalDate endDate = LocalDate.now();
            jdhProviderBillCmd.setEndDate(DateUtil.date(endDate));
            jdhProviderBillCmd.setStartDate(DateUtil.date(startDate));
            Boolean bill = providerBillApplication.createBill(jdhProviderBillCmd);
            log.info("ProviderBillCreateJob,日账单定时任务结束，time={}", System.currentTimeMillis());
        }catch (Exception e){
            log.info("ProviderBillCreateJob,日账单定时任务失败，time={}", System.currentTimeMillis(),e);
        }


    }

    public static void main(String[] args) {
        System.out.println(DateUtil.endOfDay(DateUtil.yesterday()));
    }
}
