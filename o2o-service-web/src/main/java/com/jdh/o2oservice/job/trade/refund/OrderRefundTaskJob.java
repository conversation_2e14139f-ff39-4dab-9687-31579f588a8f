package com.jdh.o2oservice.job.trade.refund;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.trade.service.JdOrderRefundApplication;
import com.jdh.o2oservice.core.domain.trade.bo.OrderRefundDetailBo;
import com.jdh.o2oservice.core.domain.trade.bo.RefundResult;
import com.jdh.o2oservice.core.domain.trade.enums.RefundStatusEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundDetail;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OrderRefundTaskJob
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Slf4j
@Component
public class OrderRefundTaskJob implements SimpleJob {

    /**
     * jdOrderRefundApplication
     */
    @Resource
    @Lazy
    private JdOrderRefundApplication jdOrderRefundApplication;

    /**
     * 执行
     *
     * @param shardingContext 切分上下文
     */
    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        JdOrderRefundDetail jdOrderRefundDetail = JdOrderRefundDetail.builder().refundStatusList(Arrays.asList(RefundStatusEnum.NO_REFUND.getType(),RefundStatusEnum.REFUND_FAIL.getType())).build();
        List<JdOrderRefundDetail> jdOrderRefundDetailList = jdOrderRefundApplication.findJdOrderRefundDetailList(jdOrderRefundDetail);
        if(CollUtil.isEmpty(jdOrderRefundDetailList)){
            return;
        }
        List<RefundResult> refundResultList = new ArrayList<>();
        Map<Long,JdOrderRefundDetail> jdOrderRefundDetailMap = jdOrderRefundDetailList.stream().collect(
                Collectors.toMap(JdOrderRefundDetail::getTaskDetailId, refundDetail -> refundDetail, (t, t2) -> t2));
        for(Map.Entry<Long, JdOrderRefundDetail> entry : jdOrderRefundDetailMap.entrySet()){
            JdOrderRefundDetail refundDetail = entry.getValue();

            JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
            jdOrderRefundTask.setTaskId(refundDetail.getTaskId());
            List<JdOrderRefundTask> refundTaskList = jdOrderRefundApplication.findJdOrderRefundTaskList(jdOrderRefundTask);
            if (CollectionUtils.isNotEmpty(refundTaskList)){
                if(Objects.nonNull(refundDetail.getRefundAmount()) && refundDetail.getRefundAmount().compareTo(BigDecimal.ZERO) == 0 && "赠随主退".equals(refundTaskList.get(0).getOperator())){
                    log.info("OrderRefundTaskJob execute vtp 赠随主退");
                    this.dealZeroAmount(refundDetail);
                    continue;
                }

                String refundDetailStr = refundTaskList.get(0).getRefundDetail();
                if (StringUtils.isNotBlank(refundDetailStr)){
                    OrderRefundDetailBo orderRefundDetailBo = JSON.parseObject(refundDetailStr, OrderRefundDetailBo.class);
                    refundDetail.setKtOperationType(orderRefundDetailBo.getKtOperationType());
                }
            }

            Boolean result = jdOrderRefundApplication.appointmentRefund(refundDetail);
            RefundResult refundResult = new RefundResult(result ? RefundStatusEnum.REFUND_ING.getType() : RefundStatusEnum.REFUND_FAIL.getType() ,refundDetail.getTransactionNum());
            refundResultList.add(refundResult);
        }
        jdOrderRefundApplication.updateRefundStatusByTransactionNum(refundResultList);
        log.info("OrderRefundTaskJob -> execute refundResultList ={}",refundResultList);
    }

    /**
     *
     * @param jdOrderRefundDetail
     */
    private void dealZeroAmount(JdOrderRefundDetail jdOrderRefundDetail) {
        String transactionNumber = jdOrderRefundDetail.getTransactionNum();
        log.info("OrderRefundTaskJob -> dealZeroAmount, transactionNumber={}",transactionNumber);
        if(jdOrderRefundDetail.getRefundStatus() == RefundStatusEnum.NO_REFUND.getType()){
            //订单状态更新
            jdOrderRefundApplication.updateRefundSuccByTransactionNum(transactionNumber,jdOrderRefundDetail);
        }
    }
}
