package com.jdh.o2oservice.job.support;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelEventTypeEnum;
import com.jdh.o2oservice.core.domain.angel.event.AngelEventBody;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelWorkPoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * work扫描，预约时间前1小时的工单，向护士发生短信、通知提醒
 *
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class AngelServicingDayBeforeRemindScanJob implements SimpleJob {


    @Resource
    @Lazy
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private AngelRepository angelRepository;
    @Resource
    @Lazy
    private ReachApplication reachApplication;
    /**
     *
     */
    @Resource
    @Lazy
    private JdhAngelWorkPoMapper jdhAngelWorkPoMapper;

    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        log.info("AngelServicingDayBeforeRemindScanJob start");
        Set<String> businessCode = Sets.newHashSet(BusinessModeEnum.ANGEL_CARE.getCode(), BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        List<Integer> workStatus = Lists.newArrayList(AngelWorkStatusEnum.RECEIVED.getType());

        List<JdhVerticalBusiness> businessList = verticalBusinessRepository.findByBusinessMode(businessCode);
        if (CollectionUtils.isEmpty(businessList)) {
            return;
        }
        List<String> verticalCodes = businessList.stream().map(JdhVerticalBusiness::getVerticalCode).collect(Collectors.toList());

        // 初始化时间范围
        LocalDateTime startTime = LocalDate.now().plusDays(1).atStartOfDay();
        LocalDateTime endTime = LocalDate.now().plusDays(2).atStartOfDay();


        List<Long> angelIds = jdhAngelWorkPoMapper.listWaitServiceAngelId(workStatus, verticalCodes, TimeUtils.localDateTimeToDate(startTime), TimeUtils.localDateTimeToDate(endTime));
        for (Long angelId : angelIds) {
            try {
                JdhAngel jdhAngel = angelRepository.find(new JdhAngelIdentifier(angelId));
                AngelEventBody angelEventBody = new AngelEventBody(TimeUtils.localDateToStr(startTime.toLocalDate()));
                Event event = new Event(jdhAngel, AngelEventTypeEnum.ANGEL_SERVICING_DAY_BEFORE_REMIND, null, angelEventBody);
                reachApplication.submitTask(event);
            } catch (Exception e) {
                log.error("AngelServicingDayBeforeRemindScanJob error angelId={}", angelId, e);
            }
        }
    }
}
