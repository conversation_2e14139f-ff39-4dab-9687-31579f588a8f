package com.jdh.o2oservice.job.angel;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.InventoryInitConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angel.service.AngelStationInventoryDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * @program: AngelStationInventoryRollJob
 * @description: 滚动服务站库存
 * @author: 姚庆海
 * @create: 2024-07-25
 **/
@Slf4j
@Component
public class AngelStationInventoryRollJob implements SimpleJob {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private AngelStationInventoryDomainService angelStationInventoryDomainService;

    private static final Integer PAGE_SIZE = 50;

    /**
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("[AngelStationInventoryRollJob.execute],服务站库存滚动开始!");
        InventoryInitConfig scheduleTimeSlice = duccConfig.getScheduleTimeSlice();
        log.info("[AngelStationInventoryRollJob.execute],服务站库存滚动天数配置，scheduleTimeSlice={}!", JSON.toJSONString(scheduleTimeSlice));
        Integer initDays = scheduleTimeSlice.getInitDays();

        LocalDate rollDate = TimeUtils.getCurrentLocalDate().plusDays(initDays);
        log.info("[AngelStationInventoryRollJob.execute],服务站库存滚动---{}----库存!");
        try{
            int pageNo = 1;
            do{
                AngelStationPageQuery angelStationPageQuery = AngelStationPageQuery.builder()
                        .angelStationStatus(AngelStationStatusEnum.ALIVE.getCode())
                        .stationModeType(AngelStationModeTypeEnum.FULL_TIME.getCode())
                        .build();
                angelStationPageQuery.setPageNum(pageNo);
                angelStationPageQuery.setPageSize(PAGE_SIZE);
                Page<JdhStation> pageList = jdhStationRepository.findPageList(angelStationPageQuery);
                if(Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getRecords())) {
                    log.info("[AngelStationInventoryRollJob.execute],服务站库存滚动结束!");
                    return;
                }

                List<JdhStation> records = pageList.getRecords();
                angelStationInventoryDomainService.rollJdhStationInventory(records, rollDate, false, null);
            }while(++pageNo <= CommonConstant.NUMBER_THOUSAND);
            log.info("[AngelStationInventoryRollJob.execute],服务站库存滚动结束。有滚动排期的服务站");
        }catch (Exception ex) {
            log.error("[AngelStationInventoryRollJob.execute],滚动服务站排期异常!", ex);
            UmpUtil.showWarnMsg(UmpKeyEnum.ANGEL_STATION_INVENTORY_ROLL_ERROR, "服务站库存滚动异常");
        }
    }
}
