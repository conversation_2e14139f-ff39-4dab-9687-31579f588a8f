package com.jdh.o2oservice.job.dispatch;

import com.alibaba.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.angel.cmd.AngelUpdateTakeOrderStatusCmd;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName DispatchAngelJob
 * @Description
 * <AUTHOR>
 * @Date 2024/5/1 09:52
 **/
@Slf4j
//@Component
public class DispatchAngelSwitchOperateJob implements SimpleJob {

    /**
     * dispatchApplication
     */
    @Resource
    @Lazy
    private DispatchApplication dispatchApplication;

    /**
     * angelApplication
     */
    @Resource
    @Lazy
    private AngelApplication angelApplication;

    /**
     * 执行
     *
     * @param shardingContext 切分上下文
     */
    @Override
    @PFTracing
    public void execute(ShardingContext shardingContext) {
        log.info("DispatchAngelSwitchOperateJob -> execute start, shardingContext={}", JSON.toJSONString(shardingContext));
        int pageSize = 50;
        int pageNum = 1;
        AngelPageRequest request = new AngelPageRequest();
        request.setJobNature(0);
        request.setTakeOrderStatus(0);
        PageDto<JdhAngelDto> pageDto;
        do{
            request.setPageNum(pageNum);
            request.setPageSize(pageSize);
            log.info("DispatchAngelSwitchOperateJob -> execute request:{}", JSON.toJSONString(request));
            pageDto = angelApplication.queryAngelByPage(request);
            log.info("DispatchAngelSwitchOperateJob -> execute pageDto:{}", JSON.toJSONString(pageDto));
            if(Objects.nonNull(pageDto) && Objects.nonNull(pageDto.getList())){
                List<JdhAngelDto> list = pageDto.getList();
                list.forEach(jdhAngelDto -> {
                    AngelUpdateTakeOrderStatusCmd cmd = new AngelUpdateTakeOrderStatusCmd();
                    cmd.setAngelId(jdhAngelDto.getAngelId());
                    cmd.setTakeOrderStatus(0);
                    angelApplication.updateTakeOrderStatus(cmd);
                });
            }
            pageNum ++;
        }while (Objects.nonNull(pageDto) && pageNum <= pageDto.getPageNum());


    }
}