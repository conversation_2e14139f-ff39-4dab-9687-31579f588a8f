<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>o2o-service-domain</artifactId>
        <groupId>com.jdh.o2oservice</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>o2o-service-angelpromise-domain</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>o2o-service-angelpromise-core-ext</module>
        <module>o2o-service-angelpromise-core</module>
    </modules>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

</project>
