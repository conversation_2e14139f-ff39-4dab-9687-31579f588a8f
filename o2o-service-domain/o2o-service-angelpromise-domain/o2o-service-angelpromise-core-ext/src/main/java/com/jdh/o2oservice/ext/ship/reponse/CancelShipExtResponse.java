package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

/**
 * @Description: 取消运单响应
 * @Author: wangpengfei144
 * @Date: 2024/9/27
 */
@Data
public class CancelShipExtResponse {

    /**
     * 扣款金额，单位：分
     */
    private Integer deductAmount;
    /**
     * 取消发起人
     */
    private Integer abortType;
    /**
     * 取消责任人
     */
    private  Integer punishType;
    /**
     * 取消原因
     */
    private String abortReason;
    /**
     * 送回费，单位：分（取件后取消订单，当取消费大于订单实付金额时，会产生送回费，需用户额外支付）
     */
    private Integer sendBackFee;

}
