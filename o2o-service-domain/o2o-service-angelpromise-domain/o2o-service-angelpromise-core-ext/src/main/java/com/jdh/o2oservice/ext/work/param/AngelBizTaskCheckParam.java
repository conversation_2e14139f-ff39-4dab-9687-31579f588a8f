package com.jdh.o2oservice.ext.work.param;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName:AngelBizTaskCheckParam
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/27 00:05
 * @Vserion: 1.0
 **/
@Data
public class AngelBizTaskCheckParam {

    /**
     * 任务单Id
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 任务单状态：1=待服务，2=服务中，3=配送中，4=服务完成，5=服务取消，6=服务退款
     */
    private Integer	taskStatus;

    /**
     * 被服务人Id
     */
    private String patientId;

    /**
     * 被服务人地址
     */
    private String patientFullAddress;

    /**
     * 被服务人纬度
     */
    private Double patientAddressLat;

    /**
     * 被服务人经度
     */
    private Double patientAddressLng;

    /**
     * 服务开始时间
     */
    private Date taskStartTime;

    /**
     * 服务结束时间
     */
    private Date taskEndTime;

    /**
     * 扩展信息：被服务人姓名、性别、就医记录图片、点子签名图片
     */
    private String taskExtVo;
}
