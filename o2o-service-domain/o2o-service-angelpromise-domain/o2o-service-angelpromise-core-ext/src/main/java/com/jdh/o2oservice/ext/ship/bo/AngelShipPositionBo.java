package com.jdh.o2oservice.ext.ship.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description 运单上报位置
 */
@Data
public class AngelShipPositionBo {


    private String outShipId;//三方运力单号

    private Integer remainingTime;//预计剩余时间 单位:分

    private Integer remainingDistance;//预计剩余航程 单位: 米

    private Double alt;//海拔

    private Double lat;//飞机实时位置坐标

    private Double lon;//飞机实时位置坐标

    private Double vel;//速度 单位: m/s

    private Long timestamp;//时间戳/秒
}
