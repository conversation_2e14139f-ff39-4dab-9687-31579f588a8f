package com.jdh.o2oservice.ext.ship.param;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName:CreateDadaShipBo
 * @Description: 创建达达运单业务对象
 * @Author: yaoqinghai
 * @Date: 2024/4/21 15:21
 * @Vserion: 1.0
 **/
@Data
@Builder
public class CreateDadaShipParam {

    /**
     * 京东单据号,订单号->履约单号->派单号
     */
    private String businessOrderNo;

    /**
     * 门店编号，门店创建后可在门店列表查看
     */
    private String shopNo;

    /**
     * 京东运单ID
     */
    private String originId;

    /**
     * 订单金额（单位：元）
     */
    private BigDecimal cargoPrice;

    /**
     * 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
     */
    private Integer isPrepay;

    /**
     * 发货人经度
     */

    private Double supplierLng;

    /**
     * 发货人纬度
     */
    private Double supplierLat;

    /**
     * 发货人地址
     */
    private String supplierAddress;

    /**
     * 发货人手机号
     */
    private String supplierPhone;

    /**
     * 发货人姓名
     */
    private String supplierName;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货人座机号（手机号和座机号必填一项）
     */
    private String receiverTel;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人地址纬度
     */
    private Double receiverLat;

    /**
     * 收货人地址经度
     */
    private Double receiverLng;

    /**
     * 回调URL
     */
    private String callback;

    /**
     * 订单重量（单位：Kg）
     */
    private Double cargoWeight;

    /**
     * 小费（单位：元，精确小数点后一位，小费金额不能高于订单金额。）
     */
    private BigDecimal tips;

    /**
     * 订单备注
     */
    private String info;

    /**
     * 支持配送的物品品类
     */
    private Integer cargoType;

    /**
     * 订单商品数量
     */
    private Integer cargoNum;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 预约发单时间（unix时间戳(10位)，精确到分）
     */
    private Integer delayPublishTime;

    /**
     * 是否根据期望送达时间预约发单（0-否，即时发单；1-是，预约发单）
     */
    private Integer isExpectFinishOrder;

    /**
     * 期望送达时间（单位秒，不早于当前时间）
     */
    private Long expectFinishTimeLimit;

    /**
     * 是否选择直拿直送（0：不需要；1：需要)
     */
    private Integer isDirectDelivery;

    /**
     * 级联地址 一级
     */
    private Long firstArea;

    /**
     * 级联地址 二级
     */
    private Long secondArea;

    /**
     * 级联地址 三级
     */
    private Long thirdArea;

    /**
     * 级联地址 四级
     */
    private Long fourthArea;

    /**
     * 计划上门时间-开始时间
     */
    private Date workStartTime;

    /**
     * 计划上门时间-结束时间
     */
    private Date workEndTime;

    private String startAirportId;//起飞机场ID

    private String endAirportId;//终点机场ID

    private List<Long> medicalPromiseIds;//检测码
}
