package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

/**
 * 达达接口返回公共对象
 *
 * @link <a href="https://newopen.qa.imdada.cn/#/development/file/registerRules">...</a>
 * @date 2024-05-27 02:29
 * <AUTHOR>
 */
@Data
public class DaDaResponse {

    /**
     * 响应状态，成功为"success"，失败为"fail"
     */
    private String status;

    /**
     * 响应返回码，参考接口返回码
     *
     * @link <a href="https://newopen.qa.imdada.cn/#/development/file/code">...</a>
     */
    private Integer code;

    /**
     * 响应描述
     */
    private String msg;

    /**
     * 响应结果，JSON对象，详见具体的接口描述
     */
    private Object result;

    /**
     * 错误编码，与code一致
     */
    private Integer errorCode;
}
