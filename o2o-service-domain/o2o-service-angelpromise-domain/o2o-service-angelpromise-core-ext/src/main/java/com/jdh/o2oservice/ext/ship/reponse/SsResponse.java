package com.jdh.o2oservice.ext.ship.reponse;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ShunFengResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/20 4:39 PM
 * @Version 1.0
 **/
@Data
public class SsResponse {

    private static final Integer SUCCESS_CODE = 200;

    private static final String SUCCESS_MSG = "成功";


    private Integer status;//错误码0成功

    private String msg;//错误描述

    private Object data;//返回数据


    public static SsResponse buildSuccessResult(Object data) {
        return buildNewResult(SUCCESS_CODE, SUCCESS_MSG, data);
    }

    public static SsResponse buildErrorResult(Integer errorCode, String errorMsg) {
        return buildNewResult(errorCode, errorMsg, null);
    }

    private static SsResponse buildNewResult(Integer errorCode, String errorMsg, Object data) {
        SsResponse r = new SsResponse();
        r.setStatus(errorCode);
        r.setMsg(errorMsg);
        r.setData(data);
        return r;
    }
}
