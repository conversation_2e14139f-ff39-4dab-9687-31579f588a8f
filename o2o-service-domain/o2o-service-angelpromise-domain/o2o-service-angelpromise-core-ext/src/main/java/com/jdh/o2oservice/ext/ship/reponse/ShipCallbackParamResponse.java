package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName ShipCallbackParamResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/27 21:40
 */
@Data
public class ShipCallbackParamResponse {

    /**
     * 运力订单号，默认为空
     */
    private String clientId;

    /**
     * 第三方订单ID，对应下单接口中的内部订单号
     */
    private Long orderId;

    /**
     * 订单状态：不同的运力供应商状态码不同
     */
    private Integer orderStatus;

    /**
     * 供应商订单状态：不同的运力供应商状态码不同
     */
    private Integer status;

    /**
     * 订单状态描述
     */
    private String statusDesc;

    /**
     * 订单子状态
     */
    private Integer subStatus;

    /**
     * 订单子状态描述
     */
    private String subStatusDesc;

    /**
     * 重复回传状态原因(1-重新分配骑士，2-骑士转单)。重复的状态消息默认不回传，如系统支持可在开发助手-应用信息中开启【运单重抛回调通知】开关
     * 达达回传字段
     */
    private Integer repeatReasonType;

    /**
     * 订单取消原因,其他状态下默认值为空字符串
     */
    private String cancelReason;

    /**
     * 订单取消原因来源(1:配送员取消；2:商家主动取消；3:系统或客服取消；0:默认值)
     */
    private Integer cancelFrom;

    /**
     * 更新时间，时间戳除了创建达达运单失败=1000的精确毫秒，其他时间戳精确到秒
     */
    private Long updateTime;

    /**
     * 配送员id
     * 达达：达达配送员id，接单以后会传
     */
    private String dmId;

    /**
     * 配送员名称
     * 达达：配送员姓名，接单以后会传
     */
    private String dmName;

    /**
     * 配送员电话
     * 达达：配送员手机号，接单以后会传
     */
    private String dmMobile;

    /**
     * 骑手头像
     */
    private String dmHeadIcon;

    /**
     * 配送员纬度
     */
    private double latitude;

    /**
     * 配送员经度
     */
    private double longitude;

    /**
     * 配送员接单时间
     */
    private String time;

    /**
     * 预计送达时间文案
     */
    private String estimateDeliveryTimeTip;

    /**
     * 发件码
     */
    private String sendCode;

    /**
     * 收货码
     */
    private String finishCode;

    /**
     * 扣款金额, 单位：元
     */
    private BigDecimal deductAmount;

    /**
     * 取消责任人
     */
    private Integer punishType;

    /**
     * 送回费，单位：元
     */
    private BigDecimal sendBackFee;

    /**
     * 退款金额，单位：元
     */
    private BigDecimal drawback;

    /**
     * 加密串
     * 达达：对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
     */
    private String signature;
}
