package com.jdh.o2oservice.ext.work.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName:WorkBizParamCheckParam
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/26 19:04
 * @Vserion: 1.0
 **/
@Data
public class WorkBizParamCheckParam {

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 来源Id，派单id
     */
    private Long sourceId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务者Cpin
     */
    private String angelPin;

    /**
     * 服务者头像
     */
    private String angelHeadImg;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者姓名加密
     */
    private String angelNameEncrypt;

    /**
     * 服务者联系方式
     */
    private String angelPhone;

    /**
     * 服务者联系方式加密
     */
    private String angelPhoneEncrypt;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 服务者工单状态：1待接单，2、已接单，3、待服务，4、服务中，5送检中，6服务完成，7退款中，8已退款，9已取消
     */
    private Integer	workStatus;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 扩展信息：订单信息{订单Id、订单remark,预约人姓名、联系方式}，医生着装图片，医疗废物处理图片
     */
    private String extend;

    /**
     * 保单Id
     */
    private String insureId;

    /**
     * 投保状态：1=审核中，2=成功，3=失败，4=失效
     */
    private Integer	insureStatus;

    /**
     * 服务者任务单列表
     */
    private List<AngelBizTaskCheckParam> angelBizTaskCheckParamList;
}
