package com.jdh.o2oservice.ext.ship.param;

import lombok.Data;

/**
 * @ClassName:DeliveryStatusBackParam
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/28 10:57
 * @Vserion: 1.0
 **/
@Data
public class DeliveryStatusBackParam {

    /**
     * 运力订单号，默认为空
     */
    private String clientId;

    /**
     * 第三方订单ID，对应下单接口中的内部订单号
     */
    private Long orderId;

    /**
     * 订单状态：不同的运力供应商状态码不同
     */
    private Integer orderStatus;

    /**
     * 重复回传状态原因(1-重新分配骑士，2-骑士转单)。重复的状态消息默认不回传，如系统支持可在开发助手-应用信息中开启【运单重抛回调通知】开关
     * 达达回传字段
     */
    private Integer repeatReasonType;

    /**
     * 订单取消原因,其他状态下默认值为空字符串
     */
    private String cancelReason;

    /**
     * 订单取消原因来源(1:配送员取消；2:商家主动取消；3:系统或客服取消；0:默认值)
     */
    private Integer cancelFrom;

    /**
     * 更新时间，时间戳除了创建达达运单失败=1000的精确毫秒，其他时间戳精确到秒
     */
    private Long updateTime;

    /**
     * 加密串
     * 达达：对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
     */
    private String signature;

    /**
     * 配送员id
     * 达达：达达配送员id，接单以后会传
     */
    private Integer dmId;

    /**
     * 配送员名称
     * 达达：配送员姓名，接单以后会传
     */
    private String dmName;

    /**
     * 配送员电话
     * 达达：配送员手机号，接单以后会传
     */
    private String dmMobile;

    /**
     * 收货码
     */
    private String finishCode;
}
