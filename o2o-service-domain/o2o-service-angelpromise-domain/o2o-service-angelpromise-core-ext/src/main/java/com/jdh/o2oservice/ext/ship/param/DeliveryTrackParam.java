package com.jdh.o2oservice.ext.ship.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @ClassName:DeliveryTrackParam
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/13 18:30
 * @Vserion: 1.0
 **/
@Data
public class DeliveryTrackParam {

    /**
     * 三方订单号
     */
    private String orderId;

    @JSONField(name = "order_id")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
