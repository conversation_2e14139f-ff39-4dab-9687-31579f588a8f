package com.jdh.o2oservice.ext.ship.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 达达运单状态枚举
 * @ClassName:AngelShipStatusEnum
 * @Description: 运单状态
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2023/11/25 15:04
 * @Vserion: 1.0
 **/
@Getter
@AllArgsConstructor
public enum StanderAngelShipStatusEnum {
    /**
     *
     */
    SHIP_ORDER_INIT(0, "初始状态", Sets.newHashSet(1, 2, 3, 4, 5, 8, 9, 10, 100, 1000)),

    WAITING_RECEIVE_ORDER(1, "待骑手接单", Sets.newHashSet(1, 2, 3, 4, 5, 8, 9, 10, 100, 1000)),

    WAITING_RECEIVE_GOODS(2, "骑手正在赶来", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100)),

    DELIVERING_GOODS(3, "配送中", Sets.newHashSet(3, 4, 5, 8, 9, 10)),

    ORDER_SHIP_FINISH(4, "已送到实验室", Sets.newHashSet(4)),

    ORDER_SHIP_CANCEL(5, "已取消", Sets.newHashSet(5)),

    DISPATCH_SHIP_ORDER(8, "已追加待接单", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100)),

    DELIVERED_BACK(9, "妥投异常之物品返回中", Sets.newHashSet(4, 9, 10)),

    DELIVERED_BACK_FINISH(10, "妥投异常之物品返回完成", Sets.newHashSet()),

    WAITING_TRANSFER(11, "转单中", Sets.newHashSet(1, 2, 3, 4, 5, 9, 10, 100)),

    ORDER_SHIP_CANCELING(12, "申请取消中", Sets.newHashSet(1, 2, 3, 4, 5, 9, 10, 100)),

    ORDER_SHIP_CANCEL_COMMISSIONING(13, "取消单客服中", Sets.newHashSet(1, 2, 3, 4, 5, 9, 10, 100)),

    KNIGHT_REACH_STORE(100, "骑手已上门", Sets.newHashSet(3, 4, 5, 8, 9, 10, 100)),

    CREATE_ORDER_FAIL(1000, "创建达达运单失败", Sets.newHashSet()),

    CANCEL_SHIP_FAIL(400, "主动取消运单失败", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100)),

    CANCEL_APPOINTMENT_FAIL(401, "取消运单成功取消预约单失败", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100)),

    ;


    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 运单状态描述
     */
    private String shipStatusDesc;

    /**
     * 下一状态集合
     */
    private Set<Integer> nextStatusSet;

    private static List<Integer> refundShipStatusSet = Lists.newArrayList(StanderAngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus(), StanderAngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), StanderAngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());
    private static List<Integer> illegalShipStatusSet = Lists.newArrayList(StanderAngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), StanderAngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());
    public static List<Integer> canRefundShipStatusList = Lists.newArrayList(StanderAngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(), StanderAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus(), StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(),
            StanderAngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus());

    public static String getStatusDesc(Integer status) {
        if(Objects.isNull(status)){
            return null;
        }

        for (StanderAngelShipStatusEnum value : StanderAngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(status)){
                return value.getShipStatusDesc();
            }
        }
        return null;
    }

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchRefundStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return refundShipStatusSet.contains(status);
    }

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchShipIllegalStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return illegalShipStatusSet.contains(status);
    }

    /**
     * 骑手接单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchReceiveOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(status);
    }

    /**
     * 派单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchCallOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return StanderAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(status);
    }

    /**
     * 匹配运单id
     *
     * @param shipStatus
     * @return
     */
    public static StanderAngelShipStatusEnum matchShipStatusEnum(Integer shipStatus){
        for (StanderAngelShipStatusEnum value : StanderAngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(shipStatus)){
                return value;
            }
        }
        return null;
    }

    /**
     * 判断当前运单状态是否可以取消
     *
     * @param shipStatus
     * @return
     */
    public static boolean matchCanCancelStatus(Integer shipStatus) {
        if(canRefundShipStatusList.contains(shipStatus)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 判断正向状态
     * @param shipStatus
     * @return
     */
    public static Boolean checkForwardStatus(Integer shipStatus){
        return StanderAngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus().equals(shipStatus) ||  StanderAngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(shipStatus)
                ||  StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(shipStatus) ||  StanderAngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(shipStatus)
                ||  StanderAngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus().equals(shipStatus) ||  StanderAngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus().equals(shipStatus);
    }
}
