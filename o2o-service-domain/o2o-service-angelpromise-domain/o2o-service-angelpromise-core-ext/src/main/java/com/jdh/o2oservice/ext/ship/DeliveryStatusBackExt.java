package com.jdh.o2oservice.ext.ship;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jd.matrix.sdk.base.IDomainAbilityExtension;
import com.jdh.o2oservice.common.ext.ExtBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.param.CheckDeliverStatusParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryStatusBackParam;

/**
 * @InterfaceName:DeliveryStatusBackExt
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/28 10:47
 * @Vserion: 1.0
 **/

public interface DeliveryStatusBackExt extends IDomainAbilityExtension {

    /**
     * 签名验证
     *
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_STATUS_BACK_EXT,
            name = "运力验证签名"
    )
    ExtResponse<Boolean> verifySignature(String verifyParam);
}
