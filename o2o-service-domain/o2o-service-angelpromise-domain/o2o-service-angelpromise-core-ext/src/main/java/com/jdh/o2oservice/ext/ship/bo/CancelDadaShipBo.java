package com.jdh.o2oservice.ext.ship.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @ClassName:CancelDadaShipBo
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 01:54
 * @Vserion: 1.0
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelDadaShipBo {

    /**
     * 第三方订单编号
     */
    private String orderId;

    /**
     * 取消原因id
     */
    private Integer cancelReasonId;

    /**
     * 取消原因描述
     */
    private String cancelReason;

    @JSONField(name = "order_id")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JSONField(name = "cancel_reason_id")
    public Integer getCancelReasonId() {
        return cancelReasonId;
    }

    public void setCancelReasonId(Integer cancelReasonId) {
        this.cancelReasonId = cancelReasonId;
    }

    @JSONField(name = "cancel_reason")
    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }
}
