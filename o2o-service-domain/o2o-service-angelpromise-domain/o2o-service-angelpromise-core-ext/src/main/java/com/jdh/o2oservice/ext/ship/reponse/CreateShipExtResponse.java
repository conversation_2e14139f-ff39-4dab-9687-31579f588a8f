package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CreateShipResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/25 13:51
 */
@Data
public class CreateShipExtResponse {

    /**
     * 供应商侧运单号
     */
    private String outOrderNo;

    /**
     * 配送距离，寄件地址到收件地址
     */
    private BigDecimal totalDistance;

    /**
     * 预计呼叫时间
     */
    private Date estimateCallTime;

    /**
     * 预计接单时间
     */
    private Date estimateGrabTime;

    /**
     * 预计取件时间
     */
    private Date estimatePickUpTime;

    /**
     * 预计完单时间
     */
    private Date estimateReceiveTime;

    /**
     * 费用信息
     */
    private CreateShipExtFeeResponse extFeeResponse;

    /**
     * 门店编号，在供应商侧创建的店铺编码
     */
    private String shopNo;

    private Long expectTakeoffTime;//无人机起飞时间


}
