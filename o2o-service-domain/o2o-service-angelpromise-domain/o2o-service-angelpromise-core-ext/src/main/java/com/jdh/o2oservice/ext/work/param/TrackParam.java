package com.jdh.o2oservice.ext.work.param;

import lombok.Data;

/**
 * @ClassName:TrackParam
 * @Description:
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/6/28 13:43
 * @Vserion: 1.0
 **/
@Data
public class TrackParam {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 运单id
     */
    private Long shipId;

    /**
     * 服务者类型
     */
    private Integer angelType;

    /**
     * 工单状态
     */
    private Integer workStatus;

    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 业务模式
     */
    private String businessMode;
}
