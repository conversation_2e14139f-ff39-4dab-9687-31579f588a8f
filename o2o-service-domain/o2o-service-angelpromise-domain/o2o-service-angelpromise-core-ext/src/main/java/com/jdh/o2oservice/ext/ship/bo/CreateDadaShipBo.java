package com.jdh.o2oservice.ext.ship.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName CreateDadaShipBo
 * @Description
 * <AUTHOR>
 * @Date 2024/10/8 13:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateDadaShipBo {

    /**
     * 门店编号，门店创建后可在门店列表查看
     */
    private String shopNo;

    /**
     * 第三方订单ID
     */
    private String originId;

    /**
     * 订单金额（单位：元）
     */
    private BigDecimal cargoPrice;

    /**
     * 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
     */
    private Integer isPrepay;

    /**
     * 发货人经度
     */

    private Double supplierLng;

    /**
     * 发货人纬度
     */
    private Double supplierLat;

    /**
     * 发货人地址
     */
    private String supplierAddress;

    /**
     * 发货人手机号
     */
    private String supplierPhone;

    /**
     * 发货人姓名
     */
    private String supplierName;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货人座机号（手机号和座机号必填一项）
     */
    private String receiverTel;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人地址纬度
     */
    private Double receiverLat;

    /**
     * 收货人地址经度
     */
    private Double receiverLng;

    /**
     * 回调URL
     */
    private String callback;

    /**
     * 订单重量（单位：Kg）
     */
    private Double cargoWeight;

    /**
     * 小费（单位：元，精确小数点后一位，小费金额不能高于订单金额。）
     */
    private BigDecimal tips;

    /**
     * 订单备注
     */
    private String info;

    /**
     * 支持配送的物品品类
     */
    private Integer cargoType;

    /**
     * 订单商品数量
     */
    private Integer cargoNum;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 预约发单时间（unix时间戳(10位)，精确到分）
     */
    private Integer delayPublishTime;

    /**
     * 是否根据期望送达时间预约发单（0-否，即时发单；1-是，预约发单）
     */
    private Integer isExpectFinishOrder;

    /**
     * 期望送达时间（单位秒，不早于当前时间）
     */
    private Long expectFinishTimeLimit;

    /**
     * 是否选择直拿直送（0：不需要；1：需要)
     */
    private Integer isDirectDelivery;

    /**
     * 订单来源标识
     */
    private String originMark;

    /**
     * 订单来源编号
     */
    private String originMarkNo;

    @JSONField(name = "shop_no")
    public String getShopNo() {
        return shopNo;
    }

    public void setShopNo(String shopNo) {
        this.shopNo = shopNo;
    }
    @JSONField(name = "origin_id")
    public String getOriginId() {
        return originId;
    }

    public void setOriginId(String originId) {
        this.originId = originId;
    }

    @JSONField(name = "cargo_price")
    public BigDecimal getCargoPrice() {
        return cargoPrice;
    }

    public void setCargoPrice(BigDecimal cargoPrice) {
        this.cargoPrice = cargoPrice;
    }
    @JSONField(name = "is_prepay")
    public Integer getIsPrepay() {
        return isPrepay;
    }

    public void setIsPrepay(Integer isPrepay) {
        this.isPrepay = isPrepay;
    }
    @JSONField(name = "supplier_lng")
    public Double getSupplierLng() {
        return supplierLng;
    }

    public void setSupplierLng(Double supplierLng) {
        this.supplierLng = supplierLng;
    }
    @JSONField(name = "supplier_lat")
    public Double getSupplierLat() {
        return supplierLat;
    }

    public void setSupplierLat(Double supplierLat) {
        this.supplierLat = supplierLat;
    }
    @JSONField(name = "supplier_address")
    public String getSupplierAddress() {
        return supplierAddress;
    }

    public void setSupplierAddress(String supplierAddress) {
        this.supplierAddress = supplierAddress;
    }
    @JSONField(name = "supplier_phone")
    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }
    @JSONField(name = "supplier_name")
    public String getSupplierName() {
        return supplierName;
    }
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
    @JSONField(name = "receiver_name")
    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    @JSONField(name = "receiver_address")
    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }
    @JSONField(name = "receiver_lat")
    public Double getReceiverLat() {
        return receiverLat;
    }

    public void setReceiverLat(Double receiverLat) {
        this.receiverLat = receiverLat;
    }
    @JSONField(name = "receiver_lng")
    public Double getReceiverLng() {
        return receiverLng;
    }

    public void setReceiverLng(Double receiverLng) {
        this.receiverLng = receiverLng;
    }
    @JSONField(name = "callback")
    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }
    @JSONField(name = "cargo_weight")
    public Double getCargoWeight() {
        return cargoWeight;
    }

    public void setCargoWeight(Double cargoWeight) {
        this.cargoWeight = cargoWeight;
    }
    @JSONField(name = "tips")
    public BigDecimal getTips() {
        return tips;
    }

    public void setTips(BigDecimal tips) {
        this.tips = tips;
    }
    @JSONField(name = "info")
    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
    @JSONField(name = "cargo_type")
    public Integer getCargoType() {
        return cargoType;
    }

    public void setCargoType(Integer cargoType) {
        this.cargoType = cargoType;
    }
    @JSONField(name = "cargo_num")
    public Integer getCargoNum() {
        return cargoNum;
    }

    public void setCargoNum(Integer cargoNum) {
        this.cargoNum = cargoNum;
    }
    @JSONField(name = "invoice_title")
    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }
    @JSONField(name = "delay_publish_time")
    public Integer getDelayPublishTime() {
        return delayPublishTime;
    }

    public void setDelayPublishTime(Integer delayPublishTime) {
        this.delayPublishTime = delayPublishTime;
    }
    @JSONField(name = "is_expect_finish_order")
    public Integer getIsExpectFinishOrder() {
        return isExpectFinishOrder;
    }

    public void setIsExpectFinishOrder(Integer isExpectFinishOrder) {
        this.isExpectFinishOrder = isExpectFinishOrder;
    }
    @JSONField(name = "expect_finish_time_limit")
    public Long getExpectFinishTimeLimit() {
        return expectFinishTimeLimit;
    }

    public void setExpectFinishTimeLimit(Long expectFinishTimeLimit) {
        this.expectFinishTimeLimit = expectFinishTimeLimit;
    }
    @JSONField(name = "is_direct_delivery")
    public Integer getIsDirectDelivery() {
        return isDirectDelivery;
    }

    public void setIsDirectDelivery(Integer isDirectDelivery) {
        this.isDirectDelivery = isDirectDelivery;
    }

    @JSONField(name = "receiver_phone")
    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    @JSONField(name = "receiver_tel")
    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel;
    }

    @JSONField(name = "origin_mark")
    public String getOriginMark() {
        return originMark;
    }

    public void setOriginMark(String originMark) {
        this.originMark = originMark;
    }

    @JSONField(name = "origin_mark_no")
    public String getOriginMarkNo() {
        return originMarkNo;
    }

    public void setOriginMarkNo(String originMarkNo) {
        this.originMarkNo = originMarkNo;
    }
}
