package com.jdh.o2oservice.ext.ship.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName:CancelDadaShipBo
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 01:54
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelDadaShipParam {

    /**
     * 京东生成的运单编号
     */
    private String orderId;

    /**
     * 供应商订单编码
     */
    private String providerOrderId;

    /**
     * 取消原因id
     */
    private Integer cancelReasonId;

    /**
     * 取消原因描述
     */
    private String cancelReason;
}
