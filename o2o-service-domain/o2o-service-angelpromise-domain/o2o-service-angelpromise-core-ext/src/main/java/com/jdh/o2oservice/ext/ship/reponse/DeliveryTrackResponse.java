package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

/**
 * @ClassName:DeliveryTrackResponse
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/6/13 14:00
 * @Vserion: 1.0
 **/
@Data
public class DeliveryTrackResponse {

    /**
     * 轨迹链接
     */
    private String trackUrl;

    public void convertToHttps() {
        this.trackUrl = this.trackUrl.replaceAll("http:", "https:");
    }
}
