package com.jdh.o2oservice.ext.ship;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jd.matrix.sdk.base.IDomainAbilityExtension;
import com.jdh.o2oservice.common.ext.ExtBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.RiderPositionParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @InterfaceName:CreateShipExt
 * @Description: 创建运单扩展点
 * @Author: yaoqinghai
 * @Date: 2024/5/27 02:09
 * @Vserion: 1.0
 **/
public interface CreateShipExt extends IDomainAbilityExtension {

    /**
     * 呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_CREATE_EXT,
            name = "验证签名")
    ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo);

    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_RECREATE_EXT,
            name = "验证签名")
    ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam);

    /**
     * 取消运力
     *
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_CANCEL_EXT,
            name = "取消运力")
    ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam);

    /**
     * 运力供应商状态回传转换
     *
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_CALLBACK_PARSE_EXT,
            name = "运力供应商状态回传转换")
    ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap);

    /**
     * 查询骑手轨迹
     * @return
     */
    DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam);

    /**
     * 查询运单详情
     *
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_SHIP_ORDER_DETAIL_EXT,
            name = "查询运单订单详情")
    ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest);
}
