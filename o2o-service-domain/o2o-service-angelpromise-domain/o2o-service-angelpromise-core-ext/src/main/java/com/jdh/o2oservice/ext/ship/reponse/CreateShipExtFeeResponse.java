package com.jdh.o2oservice.ext.ship.reponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName CreateShipExtFeeResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/8 17:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateShipExtFeeResponse {

    /**
     * 实付金额（元）
     */
    private BigDecimal amount;

    /**
     * 运单总费用（元）
     */
    private BigDecimal totalAmount;

    /**
     * 运单费用明细
     */
    private String totalAmountDetail;

    /**
     * 优惠额度（元）
     */
    private BigDecimal discountAmount;

    /**
     * 增值服务明细
     */
    private String interestList;
}
