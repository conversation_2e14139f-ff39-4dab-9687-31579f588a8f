package com.jdh.o2oservice.ext.ship.reponse;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jdh.o2oservice.common.result.response.Response;
import lombok.Data;

/**
 * @ClassName ShunFengResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/20 4:39 PM
 * @Version 1.0
 **/
@Data
public class ShunFengResponse {

    private static Integer SUCCESS_CODE = 0;

    private static String SUCCESS_MSG = "success";

    @JSONField(name="error_code")
    @JsonProperty("error_code")
    private Integer errorCode;//错误码0成功

    @JSONField(name="error_msg")
    @JsonProperty("error_msg")
    private String errorMsg;//错误描述

    private Object result;//返回数据

    private Object errorData;//错误数据

    public static ShunFengResponse buildSuccessResult(Object data) {
        return buildNewResult(SUCCESS_CODE, SUCCESS_MSG, data);
    }

    public static ShunFengResponse buildErrorResult(Integer errorCode, String errorMsg) {
        return buildNewResult(errorCode, errorMsg, null);
    }

    private static ShunFengResponse buildNewResult(Integer errorCode, String errorMsg, Object data) {
        ShunFengResponse r = new ShunFengResponse();
        r.setErrorCode(errorCode);
        r.setErrorMsg(errorMsg);
        r.setResult(data);
        return r;
    }
}
