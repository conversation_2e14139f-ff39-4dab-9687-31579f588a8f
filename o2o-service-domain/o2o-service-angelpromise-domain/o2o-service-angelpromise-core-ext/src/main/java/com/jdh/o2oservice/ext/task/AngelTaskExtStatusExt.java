package com.jdh.o2oservice.ext.task;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jd.matrix.sdk.base.IDomainAbilityExtension;
import com.jdh.o2oservice.common.ext.ExtBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;

/**
 * @InterfaceName:AngelTaskExtStatusExt
 * @Description: 任务单扩展状态扩展点
 * @Author: yaoqinghai
 * @Date: 2024/5/19 21:08
 * @Vserion: 1.0
 **/
public interface AngelTaskExtStatusExt extends IDomainAbilityExtension {

    /**
     * 验证码验证成功状态扩展点
     *
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_TASK_START_EXT_STATUS_HANDLE_EXT,
            name = "验证码验证完成垂直状态扩展点"
    )
    ExtResponse<Integer> getStartVerticalStatus();
}
