package com.jdh.o2oservice.ext.ship.reponse;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 无人机
 */
@Data
public class UavResponse {

    private Integer errcode;//code码

    private String msg;//描述

    private Object result;//返回数据

    private static Integer SUCCESS_CODE = 0;

    private static String SUCCESS_MSG = "success";

    public static UavResponse buildSuccessResult(Object data) {
        return buildNewResult(SUCCESS_CODE, SUCCESS_MSG, data);
    }

    public static UavResponse buildErrorResult(Integer errorCode, String errorMsg) {
        return buildNewResult(errorCode, errorMsg, null);
    }

    private static UavResponse buildNewResult(Integer errorCode, String errorMsg, Object data) {
        UavResponse r = new UavResponse();
        r.setErrcode(errorCode);
        r.setMsg(errorMsg);
        r.setResult(data);
        return r;
    }
}
