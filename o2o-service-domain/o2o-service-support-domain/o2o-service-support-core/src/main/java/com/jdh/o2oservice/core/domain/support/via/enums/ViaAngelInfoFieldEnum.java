package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 服务者信息字段enum
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaAngelInfoFieldEnum {

    /**
     * 服务者信息字段
     */
    HEAD_IMG("angelHeadImg","头像"),

    HOME_DATE("angelHomeDate","护士上门时间"),

    ANGEL_NAME("angelName","姓名"),

    ANGEL_HEAD_LABEL("angelHeadLabel","平台认证"),
    /**
     *
     */
    ANGEL_PHONE("angelPhone","服务者电话"),

    CONTACT_ANGEL("contactAngel","联系护士"),

    ANGEL_MAP_INFO("promiseMapConfig","服务者地图"),

    PROMISE_CODE("promiseCode","上门服务码"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaAngelInfoFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
