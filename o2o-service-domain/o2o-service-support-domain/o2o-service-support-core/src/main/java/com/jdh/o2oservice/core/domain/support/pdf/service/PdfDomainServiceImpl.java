package com.jdh.o2oservice.core.domain.support.pdf.service;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateResultBo;
import com.jdh.o2oservice.core.domain.support.pdf.facotry.PdfCreateFactory;
import com.jdh.o2oservice.core.domain.support.pdf.repository.PdfCreateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description: pdf
 * @Author: wangpengfei144
 * @Date: 2024/6/27
 */
@Slf4j
@Service
public class PdfDomainServiceImpl implements PdfDomainService{
    /**
     * pdfCreateFactory
     */
    @Autowired
    private PdfCreateFactory pdfCreateFactory;

    /**
     * 生成pdf
     *
     * @param pdfCreateBo
     * @return
     */
    @Override
    public PdfCreateResultBo createPdf(PdfCreateBo pdfCreateBo) {
        PdfCreateProcessor pdfCreateProcessor = pdfCreateFactory.pdfCreateProcessor(pdfCreateBo.getTemplate());
        if (Objects.isNull(pdfCreateProcessor)){
            throw new BusinessException(SupportErrorCode.PDF_CREATE_TEMPLATE_PROCESSOR_EMPTY);
        }
        return pdfCreateProcessor.createPdf(pdfCreateBo);
    }
}
