package com.jdh.o2oservice.core.domain.support.rpc.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @ClassName SoundRecordingParam
 * @Description
 * <AUTHOR>
 * @Date 2025/6/17 14:11
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SoundRecordingParam {

    /**
     * 业务主键
     */
    private String outBusinessId;

    /**
     * 履约单号
     */
    private Long promiseId;

    /**
     * 工单号
     */
    private Long workId;

    /**
     * 查询要素集合
     */
    private Set<String> queryElement;


    /**
     * 场景值
     */
    private String scene;
}