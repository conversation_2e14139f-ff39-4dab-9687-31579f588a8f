package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * tab
 *
 * @author: yang<PERSON>yu
 * @date: 2024/1/4 2:27 下午
 * @version: 1.0
 */
@Getter
public enum ViaTabCodeEnum {

    /**
     * TAB编码
     */
    JM_PROMISE_PAGE_VACCINE_TAB("jmPromisePageVaccine","京麦履约单列表页疫苗tab"),
    JM_PROMISE_PAGE_ORAL_CAVITY_TAB("jmPromisePageOralCavity","京麦履约单列表页口腔tab"),
    JM_PROMISE_PAGE_PHYSICAL_TAB("jmPromisePagePhysical","京麦履约单列表页体检tab"),
    JM_PROMISE_PAGE_PET_TAB("jmPromisePagePet","京麦履约单列表页宠物tab"),
    JM_PROMISE_PAGE_COSMETIC_TAB("jmPromisePageCosmetic","京麦履约单列表页医美tab"),


    /**
     * 到店小程序TAB编码
     */
    STORE_PROGRAM_PROMISE_PAGE_VACCINE_TAB("storeProgramPromisePageVaccine","到店小程序履约单列表页疫苗tab"),
    STORE_PROGRAM_PROMISE_PAGE_ORAL_CAVITY_TAB("storeProgramPromisePageOralCavity","到店小程序履约单列表页口腔tab"),
    STORE_PROGRAM_PROMISE_PAGE_PHYSICAL_TAB("storeProgramPromisePagePhysical","到店小程序履约单列表页体检tab"),
    STORE_PROGRAM_PROMISE_PAGE_PET_TAB("storeProgramPromisePagePet","到店小程序履约单列表页宠物tab"),
    STORE_PROGRAM_PROMISE_PAGE_COSMETIC_TAB("storeProgramPromisePageCosmetic","到店小程序履约单列表页医美tab"),

    ;

    ViaTabCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;
}
