package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 动态字段列举
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaDynamicFieldEnum {

    /**
     * 枚举
     */
    REFUND_AMOUNT("refundAmount","退款金额"),

    ANGEL_WORK_RECEIVE_TIME("angelWorkReceiveTime","预估服务者接单时间"),

    ANGEL_HOME_DATE("angelHomeDate","预估服务者上门日期"),
    ANGEL_HOME_START_TIME("angelHomeStartTime","预估服务者上门开始时间"),
    ANGEL_HOME_END_TIME("angelHomeEndTime","预估服务者上门结束时间"),

    ANGEL_SERVICE_COMPLETE_DATE("angelServiceCompleteDate","预估服务者服务完成日期"),
    ANGEL_SERVICE_COMPLETE_TIME("angelServiceCompleteTime","预估服务者服务完成时间"),


    SUBMIT_TEST_TIME("submitTestTime","预估送检时间"),

    REPORT_TIME_TIME("reportTime","预估出报告时间"),
    PAY_EXPIRE_TIME("patExpireTime","支付过期时间"),

    ;


    ViaDynamicFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * 场景
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
