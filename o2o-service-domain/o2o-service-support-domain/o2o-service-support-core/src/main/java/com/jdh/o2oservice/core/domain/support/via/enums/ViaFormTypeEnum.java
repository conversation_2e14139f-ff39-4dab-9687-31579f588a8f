package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 聚合页面类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Getter
public enum ViaFormTypeEnum {
    /**
     *
     */
    COMMON_APPOINT_USER("commonAppointUser","常用预约人"),
    USER_NAME("userName","姓名"),
    TELEPHONE("telephone","手机号"),
    SMS_CODE("smsCode","验证码"),
    ID_CARD_NO("idCardNo","证件号"),
    GENDER("gender","性别"),
    MARRIAGE("marriage","婚否"),
    RELATION("relation","与本人关系"),
    STORE("store","服务门店"),
    PACKAGE("package","套餐"),
    AREA("area","服务区域"),
    DATE("date","服务时间"),
    SPECIMEN_CODE("specimenCode","样本条码"),
    BIRTHDAY("birthday","生日"),
    APPOINTMENT_TIME("appointmentTime","预约日历"),
    APPOINTMENT_USER("appointmentUser","预约人"),
    INTENDED_NURSE("intendedNurse","意向护士"),
    APPOINTMENT_REMARK("appointmentRemark","备注"),


    /**
     * 京麦端
     */
    JM_PROMISE_PAGE_CONDITION_PROMISE_STATUS("jmPromisePageConditionPromiseStatus","筛选条件履约单状态"),
    JM_PROMISE_PAGE_HEADER_PROMISE_STATUS("jmPromisePageHeaderPromiseStatus","表头履约单状态"),

    ;


    ViaFormTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private String type;

    private String desc;
}
