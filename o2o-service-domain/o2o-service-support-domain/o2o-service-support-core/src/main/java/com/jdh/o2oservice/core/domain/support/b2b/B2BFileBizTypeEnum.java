package com.jdh.o2oservice.core.domain.support.b2b;

import com.jdh.o2oservice.base.model.FileBizType;

/**
 * 商品文件管理
 * @date 2024-06-12 09:30
 * <AUTHOR>
 */
public enum B2BFileBizTypeEnum implements FileBizType {

    /**
     * 指标业务分类导入失败
     */
    VOUCHER_IMPORT("voucherImport"),
    ;

    /**
     *
     * @param fileBizType
     */
    B2BFileBizTypeEnum(String fileBizType) {
        this.fileBizType = fileBizType;
    }

    /**
     * 文件类型
     */
    private String fileBizType;

    @Override
    public String getBizType() {
        return fileBizType;
    }
}
