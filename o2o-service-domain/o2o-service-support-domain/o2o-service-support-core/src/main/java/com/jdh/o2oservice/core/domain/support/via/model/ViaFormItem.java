package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * ViaFormItem
 */
@Data
@Slf4j
public class ViaFormItem {

    /**
     * 表单名称
     */
    private String formName;

    /**
     * 表格类型
     */
    private String formType;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 参数字段
     */
    private String paramField;

    /**
     * 释放不可点击
     */
    private Boolean disabled;

    /**
     * 占位符
     */
    private String placeholder;

    /**
     * 默认文案的 icon
     */
    private String placeholderIcon;

    /**
     * errMsg
     */
    private String errMsg;

    /**
     * ext map
     * 表单项扩展参数：
     */
    private Map<String,Object> extMap;

    /**
     * value
     */
    private String value;

    /**
     * action
     */
    private ViaActionInfo action;

    /**
     * 为false时不会触发初始化
     */
    private Boolean initSwitch;
    /**
     * 初始化item值的方法
     */
    private ViaItemParse parse;

    /**
     * 初始化icon item方法
     */
    private ViaItemParse iconParse;

    /**
     * 是否隐藏（有的数据需要返回给前端，但是这个item不展示）
     */
    private Boolean hidden;

    /**
     * 状态映射规则
     */
    private List<ViaStatusMapping> statusMapping;

    /**
     *
     */
    private String inputType;

    /**
     * 提示
     */
    private String tips;

    /**
     * 提示图标
     */
    private String tipsIcon;

    /**
     * 初始化value
     * @param map
     * @return
     */
    public void initValue(Map<String,Object> map){
        if (Objects.isNull(initSwitch) || !initSwitch){
            return;
        }
        if (Objects.isNull(parse)) {
            return;
        }
        Object value = parse.parse(map);
        this.value = Objects.toString(value, "");
    }

}
