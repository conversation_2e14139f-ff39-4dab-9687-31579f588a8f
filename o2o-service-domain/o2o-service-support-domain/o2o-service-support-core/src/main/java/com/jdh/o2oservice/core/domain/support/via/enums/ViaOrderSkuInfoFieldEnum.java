package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 订单商品信息字段枚举
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaOrderSkuInfoFieldEnum {

    /**
     * 订单商品信息枚举字段
     */
    ORDER_SKU_IMG("skuImage","商品图片"),
    ORDER_SKU_NAME("skuName","商品名称"),
    ORDER_ITEM_AMOUNT("itemAmount","商品价格"),
    ORDER_ITEM_TOTAL_AMOUNT("itemTotalAmount","商品总价格"),
    ORDER_SKU_NUM("skuNum","商品数量"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaOrderSkuInfoFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
