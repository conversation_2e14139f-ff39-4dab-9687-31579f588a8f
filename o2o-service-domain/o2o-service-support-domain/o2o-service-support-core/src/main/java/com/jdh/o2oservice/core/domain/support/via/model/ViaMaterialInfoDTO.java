package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * @Project : 样本专业信息
 * <AUTHOR> maoxianglin1
 * @create 2025/7/10 下午8:24
 */
@Data
public class ViaMaterialInfoDTO {

    /**
     * 样本检测用户信息，根据样本档案id进行归堆
     */
    private List<String> patientInfoList;

    /**
     * 样本检测具体信息
     */
    private List<ViaMaterialDetailInfoDTO> viaMaterialDetailInfoList;
}
