package com.jdh.o2oservice.core.domain.support.via.enums;

/**
 * 采样教程楼层属性
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/2 16:32
 */
public enum ViaShipInfoFiledEnum {
    /**
     * 采样教程楼层是属性
     */
    SEND_INFO("sendInfo", "寄件信息"),
    SHIP_INFO("shipInfo", "配送信息"),
    ACCEPT_INFO("acceptInfo", "收件人信息"),

    SUB_TITLE("subTitle", "副标题"),
    ;


    ViaShipInfoFiledEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }
}
