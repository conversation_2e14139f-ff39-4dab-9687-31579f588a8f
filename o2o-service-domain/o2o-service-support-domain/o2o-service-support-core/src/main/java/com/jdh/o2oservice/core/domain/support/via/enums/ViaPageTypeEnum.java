package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 聚合页面类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Getter
public enum ViaPageTypeEnum {
    /**
     *
     */
    MODIFY_APPOINT("modifyAppoint","修改预约"),

    BUY_FIRST("buyFirst","先买后约"),

    BUY_APPOINT("buyAppoint","买约一体"),

    RE_APPOINT("reAppoint","重新预约"),
    ;


    ViaPageTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 类型
     */
    private String type;

    /**
     * desc
     */
    private String desc;

    /**
     * contain
     *
     * @param type 类型
     * @return {@link Boolean}
     */
    public static Boolean contain(String type){
        for (ViaPageTypeEnum value : ViaPageTypeEnum.values()) {
            if(value.getType().equals(type)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
