package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * ViaAngelInfoBtnEnum
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaAngelInfoBtnEnum {

    /**
     * 服务者信息 按钮
     */
    VIEW_HOME("viewHomeBtn","查看主页"),

    VIEW_POSITION("viewPositionBtn","查看位置"),
    CONTACT_ANGEL("contactAngelBtn","联系Ta"),

    ;


    /**
     * 字段
     *
     * @param btn btn
     * @param desc   desc
     */
    ViaAngelInfoBtnEnum(String btn, String desc) {
        this.btn = btn;
        this.desc = desc;
    }

    /**
     * btn
     */
    private final String btn;

    /**
     * desc
     */
    private final String desc;
}
