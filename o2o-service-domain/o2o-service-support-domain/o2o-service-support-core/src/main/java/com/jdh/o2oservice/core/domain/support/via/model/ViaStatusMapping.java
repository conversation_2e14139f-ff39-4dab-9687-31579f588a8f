package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 视图Promise状态映射
 *
 * <AUTHOR>
 * @date 2024/01/17
 */
@Data
public class ViaStatusMapping {

    /**
     * 状态表达式
     */
    private String statusExpression;

    /**
     * 状态集合
     */
    private List<Integer> statusList;

    /**
     * 展示的状态值（比如展示状态为预约成功，对应的statusList包含预约成功，修改预约成功，修改预约失败，取消预约失败）
     */
    private Integer viaStatus;

    /**
     * 状态展示文案
     */
    private String statusDesc;

    /**
     * 附加提示信息
     */
    private String noticeTip;


    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 展示icon
     */
    private String mainIcon;

    /**
     * 标题
     */
    private String title;
    /**
     * 异常文案说明
     */
    private String warmTip;
    /**
     * 主标题动态字段
     */
    private List<String> mainTitleDynamicField;

    /**
     * 标题动态字段
     */
    private List<String> titleDynamicField;

    /**
     * 动态时间
     */
    private Integer dynamicCursorMinutes;

    /**
     * 履约标题
     */
    private String promiseTitle;



    /**
     * 履约标题动态字段
     */
    private List<String> promiseTitleDynamicField;
    /**
     * 动态属性字段
     */
    private List<String> dynamicField;

    /**
     * 二维码是否打小标
     */
    private Boolean promiseCodeMark;

    /**
     * 二维码打小标icon
     */
    private String promiseCodeMarkIcon;

    /**
     * 是否露出 截屏保存按钮
     */
    private Boolean promiseCodeSaveBtn;

    /**
     * 要隐藏的楼层代码
     * 新逻辑使用showFloorCode替换，支持新增组件扩展性
     */
    @Deprecated
    private List<String> hiddenFloorCode = new ArrayList<>();

    /**
     * 隐藏护士按钮 列表
     */
    @Deprecated
    private List<String> hiddenAngelBtnList;

    /**
     * 隐藏护士信息 列表
     * 新逻辑使用showFloorCode替换，支持新增组件扩展性
     */
    @Deprecated
    private List<String> hiddenAngelFieldList;

    /**
     * 隐藏用户信息
     */
    @Deprecated
    private List<String> hiddenPatientFieldList;

    /**
     * 按钮代码列表,包含整个页面的全部按钮，底部按钮和每个楼层特有的按钮
     */
    private List<String> footerButtonCodeList;

    /**
     * 步骤条 完成列表
     */
    private List<String> stepGuideFinishCodeList;

    /**
     * 步骤条 完成图标
     */
    private String stepGuideFinishIcon;

    /**
     * 步骤条 进行中列表
     */
    private List<String> stepGuideProcessCodeList;

    /**
     * 步骤条 进行中图标
     */
    private String stepGuideProcessIcon;

    /**
     * 步骤条 等待列表
     */
    private List<String> stepGuideWaitCodeList;

    /**
     * 步骤条 等待icon
     */
    private String stepGuideWaitIcon;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 按钮
     */
    private List<ViaBtnInfo> btnList;

    //============================[之前是根据配置的hidden来判断是否展示楼层和按钮，如果心中楼层需修改历史配置，新的业务介入调整为show]============================//
    /**
     * 要展示的的楼层代码
     */
    private List<String> showFloorCode = new ArrayList<>();


    /**
     * 需要展示的属性，安装floor分组
     */
    private Map<String, List<String>> showFiledGroup;

    /**
     * viaActionInfo
     */
    private ViaActionInfo viaActionInfo;
    /**
     * 阶段
     */
    private String aggregateStatus;
    /**
     *
     */
    private Map<String, Object> container;


      /**
     * 当前状态支持的楼层
     * @param floorCode 楼层编码
     * @return
     */
    public boolean supportFloor(String floorCode) {
        if (CollectionUtils.isEmpty(showFloorCode)) {
            return Boolean.FALSE;
        }
        return showFloorCode.contains(floorCode);
    }
    /**
     * 当前状态的floorEnum楼层是否需要展示对应的file
     * @param floorCode 楼层编码
     * @param filedKey 属性字段
     * @return
     */
    public boolean supportFiled(String floorCode, String filedKey) {
        if (MapUtils.isEmpty(showFiledGroup)) {
            return Boolean.FALSE;
        }

        List<String> fileds = showFiledGroup.get(floorCode);
        if (CollectionUtils.isEmpty(fileds)) {
            return Boolean.FALSE;
        }
        return fileds.contains(filedKey);
    }

}
