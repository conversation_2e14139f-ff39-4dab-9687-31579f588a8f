package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * 页面配置dto
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class ManViaFloorElementConfig {

    /**
     * 允许访问的erp
     */
    private List<String> accessErp;

    /**
     * 允许访问的角色
     */
    private List<String> accessRoleCode;

    /**
     * 所属楼层唯一key
     */
    private String floorKey;

    /**
     * 元素唯一key
     */
    private String key;

    /**
     * 元素描述
     */
    private String name;

    /**
     * 元素类型 button、input、picker、radio等
     */
    private String type;

    /**
     * 元素类型 drawer、popup
     */
    private String clickAction;

    /**
     * 元素类型 drawer、
     */
    private String clickUrl;

    /**
     * 楼层展示条件
     */
    private String showExpression;
}
