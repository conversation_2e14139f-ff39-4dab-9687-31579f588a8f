package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 服务单楼层信息
 */
@Data
public class ViaPromiseFloorInfoDto implements Serializable {
    /**
     * 服务单楼层编码
     */
    private String promiseFloorCode;
    /**
     * 服务单楼层名称
     */
    private String promiseFloorName;

    /**
     * ViaPromiseFloorConfig
     */
    private List<ViaPromiseFloorConfig> viaPromiseFloorConfigs;

    /**
     * 服务单展示状态
     */
    private Integer promiseViaStatus;


}
