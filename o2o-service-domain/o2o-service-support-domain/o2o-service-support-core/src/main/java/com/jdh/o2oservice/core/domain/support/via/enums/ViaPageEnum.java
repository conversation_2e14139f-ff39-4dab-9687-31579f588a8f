package com.jdh.o2oservice.core.domain.support.via.enums;

/**
 * 动态页面枚举
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
public enum ViaPageEnum {
    /**
     * 页面枚举
     */
    APPOINT_GETHER("appointGether","预约聚合页"),
    APPOINT_DETAIL("appointDetail","预约详情页"),
    HOME_ORDER_DETAIL("homeOrderDetail","到家订单详情页"),
    JM_PROMISE_PAGE("jmPromisePage","京麦预约单列表页"),
    STORE_PROGRAM_PROMISE_PAGE("storeProgramPromisePage","门店小程序预约单列表页"),
    VTP_ORDER_DETAIL("vtpOrderDetail", "VTP订单详情页"),
    HOME_PROMISE_DETAIL("homePromiseDetail","到家履约详情页"),
    HOME_PROMISE_PAGE("homePromisePage","到家履约单列表页"),
    HOME_PROMISE_DETAIL_V2("homePromiseDetail_V2","到家履约详情页"),
    ;


    ViaPageEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    /**
     * 场景
     */
    private String scene;

    /**
     * desc
     */
    private String desc;

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
