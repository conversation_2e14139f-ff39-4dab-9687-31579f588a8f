package com.jdh.o2oservice.core.domain.support.pdf.facotry;

import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.pdf.repository.PdfCreateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.StringJoiner;

/**
 * @Description: pdf生成工厂类
 * @Author: wangpengfei144
 * @Date: 2024/6/27
 */
@Slf4j
@Component
public class PdfCreateFactory {

    /**
     * 注入serviceMap
     */
    @MapAutowired
    private Map<String, PdfCreateProcessor> pdfCreateProcessorMap;

    /**
     * 生成路由key
     * @return
     */
    public  static String createRouteKey(Object... args) {
        StringJoiner joiner = new StringJoiner(CommonConstant.CHARACTER_MIDDLE_BAR);
        Arrays.stream(args).forEach(s -> joiner.add(String.valueOf(s)));
        return joiner.toString();
    }

    /**
     * router
     * @param args
     * @return
     */
    public PdfCreateProcessor pdfCreateProcessor(Object... args) {
        log.info("DispatchProcessorFactory -> createDispatchRuleProcessor, args={}", args);
        if (pdfCreateProcessorMap == null || args == null) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        String routeKey = createRouteKey(args);
        return pdfCreateProcessorMap.get(routeKey);
    }

    /**
     * 根据路由获取分派方法
     * @param key
     * @return
     */
    public PdfCreateProcessor pdfCreateProcessor(String key){
        log.info("DispatchProcessorFactory -> createDispatchRuleProcessor, key={}", key);
        if (pdfCreateProcessorMap == null || key == null) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        String routeKey = createRouteKey(key);
        return pdfCreateProcessorMap.get(routeKey);
    };

}
