package com.jdh.o2oservice.core.domain.support.via.model;

import cn.hutool.core.collection.CollectionUtil;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 页面配置dto
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class ViaConfig implements Aggregate<ViaConfigIdentifier> {

    /**
     * 页面 ViaPageEnum 类
     */
    private String page;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 业务模式
     */
    private String businessModeCode;

    /**
     * 支持的服务类型
     */
    private String serviceType;

    /**
     * 场景
     */
    private String scene;

    /**
     * 详情页配置场景
     */
    private String detailScene;

    /**
     * 主题
     */
    private ViaThemeInfo theme;

    /**
     * 楼层列表
     */
    private List<ViaFloorInfo> floorList;

    /**
     * 状态映射规则
     */
    private List<ViaStatusMapping> statusMapping;

    /**
     * 检测单状态 映射规则
     */
    private List<ViaStatusMapping> medicalStatusMapping;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 商祥链接
     */
    private String skuDetailUrl;

    /**
     * tab 标签集合
     */
    private List<ViaTabInfo> tabs;

      /**
     * 服务单状态映射规则
     */
    private List<ViaStatusMapping> promiseStatusMapping;

     /**
     * 服务单楼列表
     */
    private List<ViaPromiseFloorInfoDto> promiseFloorList;

    /**
     * 服务凭证状态映射规则
     */
    private List<ViaStatusMapping> voucherStatusMapping;

    /**
     * 聚合状态
     */
    private String aggregateStatus;

    /**
     * 1-骑手上门检测
     * 2-护士上门检测
     * 3-护士上门护理
     * 4-快递寄送模式
     * 5-康复师
     */
    private Integer skuServiceType;

    /**
     *
     */
    private Map<String, Object> eventTracing;
    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.BASE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return SupportAggregateEnum.VIA_CONFIG;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ViaConfigIdentifier}
     */
    @Override
    public ViaConfigIdentifier getIdentifier() {
        return ViaConfigIdentifier.builder().scene(scene).build();
    }

    /**
     *
     * @param obj
     */
    @SuppressWarnings("JdLocalVariableDuplication")
    public void init(Map<String, Object> obj){
        if (CollectionUtil.isNotEmpty(obj)){
            return;
        }
        List<ViaFloorInfo> floorList = getFloorList();
        if (CollectionUtil.isNotEmpty(floorList)){
            for (ViaFloorInfo floorInfo : floorList) {
                List<ViaFloorConfig> floorConfigList = floorInfo.getFloorConfigList();
                for (ViaFloorConfig viaFloorConfig : floorConfigList) {
                    viaFloorConfig.init(obj);
                }
            }
        }
    }
}
