package com.jdh.o2oservice.core.domain.support.ocr.result;

import lombok.Data;

/**
 * @ClassName AngelIdCardResult
 * @Description
 * <AUTHOR>
 * @Date 2025/7/26 17:39
 */
@Data
public class AngelIdCardResult {

    /**
     * 地址
     */
    private String address;

    /**
     * 民族
     */
    private String nation;

    /**
     * 性别
     */
    private String sex;

    /**
     * 姓名
     */
    private String name;

    /**
     * 证件号
     */
    private String cardNo;

    /**
     * 签发机关
     */
    private String issuingAuthority;

    /**
     * 签发日期
     */
    private String singDateStr;

    /**
     * 过期日期
     */
    private String expirationDateStr;
}
