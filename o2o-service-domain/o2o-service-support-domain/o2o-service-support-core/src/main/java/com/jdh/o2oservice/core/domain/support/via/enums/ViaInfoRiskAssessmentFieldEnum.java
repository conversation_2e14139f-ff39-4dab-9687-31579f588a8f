package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

@Getter
public enum ViaInfoRiskAssessmentFieldEnum {

    TITLE("title","标题"),

    CONTENT("content","内容"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaInfoRiskAssessmentFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
