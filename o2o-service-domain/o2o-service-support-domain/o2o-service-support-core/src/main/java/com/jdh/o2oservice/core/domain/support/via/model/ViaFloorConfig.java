package com.jdh.o2oservice.core.domain.support.via.model;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 楼层配置
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViaFloorConfig {

    /**
     * 背景图像
     */
    private String backGroundImage;

    /**
     * 主icon
     */
    private String mainIcon;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 标题
     */
    private String title;
    /**
     * 履约时效异常文案
     */
    private String warmTip;

    /**
     * icon
     */
    private String icon;

    /**
     * 卡片
     */
    private ViaCardInfo card;

    /**
     * 字段key
     */
    private String fieldKey;

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 字段标签
     */
    private String fieldLabel;

    /**
     * 字段ID（前端使用）
     */
    private String fieldId;

    /**
     * 字段单位（前端使用）
     */
    private String fieldUnit;

    /**
     * 字段扩展类（前端使用）
     */
    private String fieldExtraClass;

    /**
     * viaStatus 展示的状态值
     */
    private Integer viaStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 跳转链接
     */
    private String targetUrl;

    /**
     * 步骤状态
     */
    private String stepStatus;

    /**
     * 步骤条 编码
     */
    private String stepCode;

    /**
     * 步骤 描述
     */
    private String stepDesc;

    /**
     * 步骤状态
     */
    private String stepIcon;

    /**
     * 步骤标题
     */
    private String stepTitle;

    /**
     * 楼层编码
     */
    private String floorCode;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 通知提示
     */
    private String noticeTip;

    /**
     * 二维码生成要素
     */
    private String promiseQrCode;

    /**
     * 消费码
     */
    private String promiseCode;

    /**
     * 消费码密码
     */
    private String promiseCodePwd;

    /**
     * 消费码是否打小标
     */
    private Boolean promiseCodeMark;

    /**
     * 打标的icon
     */
    private String promiseCodeMarkIcon;

    /**
     * 消费码截屏保存按钮
     */
    private Boolean promiseCodeSaveBtn;

    /**
     * 表格类型
     */
    private String formType;

    /**
     * 字段入参
     */
    private String paramField;

    /**
     * 回显默认值
     */
    private String value;

    /**
     * 是否可点击
     */
    private Boolean disabled;

    /**
     * 动作
     */
    private ViaActionInfo action;

    /**
     * 表单项
     */
    private List<ViaFormItem> formItemList;

    /**
     * 分组数据
     */
    private List<ViaGroupInfo> groupInfoList;

    /**
     * 按钮集合
     */
    private List<ViaBtnInfo> btnList;

    /**
     * 协议
     */
    private ViaAgreementInfo agreeInfo;

    /**
     * 复制按钮
     */
    private Boolean copyBtn;

    /**
     * 展示详情
     */
    private Boolean showDetail;
    /**
     * 字段组合 暂时用于 卡劵信息和服务信息的聚合
     */
    private List<ViaVoucherInfoDto> voucherInfoDtos;

    /**
     * 子配置列表
     */
    private List<ViaFloorSubConfig> subConfigList;

    /**
     * 状态表达式
     */
    private String statusExpression;
    /**
     * 样本信息
     */
    private ViaMaterialInfoDTO viaMaterialInfo;


    /**
     * 微信卡片按钮文案
     */
    private String weChatButtonName;

    /**
     * 微信卡片sku配置
     */
    private String weChatSkus;

    /**
     * 微信活码链接
     */
    private String dynamicQRCodeUrl;

    /**
     * 微信助手链接
     */
    private String weChatAssistantUrl;

    /**
     * AB是实验标记
     */
    private String abLabel;

    /**
     * 微信卡片sku
     */
    private List<String> skuIds;

    /**
     * 填充参数
     * （1）初始化action中的params，action的params作为触发时的必传参数；
     * （2）初始化formItemList中的value；
     * （3）初始化btn的action
     * @param obj
     */
    public void init(Map<String, Object> obj){

        if (Objects.nonNull(action)){
            action.init(obj);
        }

        if (CollectionUtil.isNotEmpty(formItemList)){
            List<ViaFormItem> showItems = Lists.newArrayList();
            for (ViaFormItem viaFormItem : formItemList) {
                if (Objects.isNull(viaFormItem.getHidden()) || !viaFormItem.getHidden()){
                    viaFormItem.initValue(obj);
                    showItems.add(viaFormItem);
                }
            }
            formItemList = showItems;
        }

        if (CollectionUtil.isNotEmpty(btnList)){
            for (ViaBtnInfo viaBtnInfo : btnList) {
                viaBtnInfo.getAction().init(obj);
            }
        }
    }
}
