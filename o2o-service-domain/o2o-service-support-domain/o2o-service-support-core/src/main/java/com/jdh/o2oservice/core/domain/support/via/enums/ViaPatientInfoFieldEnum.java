package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 被服务者信息字段
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaPatientInfoFieldEnum {

    /**
     * 被服务者信息字段
     */
    APPOINTMENT_TIME("appointmentTime","预约时间"),

    PATIENT_LIST("patientList","预约人列表"),

    PATIENT_ADDRESS("patientAddress","履约地址"),

    REMARK("remark","备注"),

    PATIENT_NAME("patientName","用户姓名"),

    PATIENT_SEX("patientSex","性别"),

    PATIENT_AGE("patientAge","年龄"),

    PATIENT_RELATION_TYPE("patientRelationType","家人关系"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaPatientInfoFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
