package com.jdh.o2oservice.core.domain.support.via.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViaConfigIdentifier implements Identifier {

    private String scene;

    @Override
    public String serialize() {
        return scene;
    }
}
