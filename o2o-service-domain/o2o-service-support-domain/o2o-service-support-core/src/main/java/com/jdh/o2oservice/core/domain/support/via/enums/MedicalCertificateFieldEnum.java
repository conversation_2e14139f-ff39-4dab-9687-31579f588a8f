package com.jdh.o2oservice.core.domain.support.via.enums;
import lombok.Getter;

@Getter
public enum MedicalCertificateFieldEnum {

    TITLE("title","标题"),

    MAX_UPLOAD_NUM("maxUploadNum","最大上传数量"),

    DESC("desc","描述"),

    MEDICAL_CERTIFICATE_FILES("medicalCertificateFiles","就医证明图片"),

    UPLOAD_MEDICAL_CERTIFICATE("uploadMedicalCertificate","上传就医证明"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    MedicalCertificateFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
