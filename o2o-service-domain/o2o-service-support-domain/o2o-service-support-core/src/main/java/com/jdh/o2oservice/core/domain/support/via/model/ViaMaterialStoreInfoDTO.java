package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * @Project : 样本检测实验室信息
 * <AUTHOR> maoxianglin1
 * @create 2025/7/11 上午10:08
 */
@Data
public class ViaMaterialStoreInfoDTO {

    /**
     * 实验室名称
     */
    private String storeName;

    /**
     * 实验室主图：需要用id换链接
     */
    private String storeIcon;

    /**
     * 实验室标签列表
     */
    private List<String> storeLabelList;

    /**
     * 资质信息列表
     */
    private List<ViaMaterialStoreLicenseInfoDTO> licenseInfoList;


}
