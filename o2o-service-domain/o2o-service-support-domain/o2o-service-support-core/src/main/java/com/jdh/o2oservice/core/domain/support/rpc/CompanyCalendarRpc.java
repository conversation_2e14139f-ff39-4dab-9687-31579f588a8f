package com.jdh.o2oservice.core.domain.support.rpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.CompanyCalendarBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.QueryCompanyCalendarBo;
import java.util.List;

public interface CompanyCalendarRpc {

    /**
     * 根据考勤组、工作计划、日期查询工作日程表
     * @param bo
     * @return
     */
    List<CompanyCalendarBo> getCalendarByGroupAndTime(QueryCompanyCalendarBo bo);
}
