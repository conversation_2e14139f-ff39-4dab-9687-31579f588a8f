package com.jdh.o2oservice.core.domain.support.via.enums;

import cn.hutool.core.bean.BeanUtil;

/**
 * ViaFormItem解析的类型
 * @author: yang<PERSON>yu
 * @date: 2024/3/12 10:47 上午
 * @version: 1.0
 */
public enum DynamicParseTypeEnum {

    /** 默认文案 */
    DEFAULT(0),
    /** 表达式语法 "Object.filed" 实现原理 {@link BeanUtil#getProperty}*/
    FIELD(1),
    /** 表达式语法 "Object#formatMethod" 实现原理 {@link BeanUtil#getProperty} && {@link java.lang.invoke.MethodHandle}
     * 方法返回值必须是String，无入参，适用配置实体的方法
     * */
    FORMAT_METHOD(2),
    /**
     * 使用Map进行映射，主要是面向一些状态类型字段的映射
     */
    MAPPING(3),

    /**
     * 嵌套结构（可以组装链接等复杂参数）
     */
    NEST_PARSE(4),
    /**
     * 短链
     * 【注意】并不是所有链接都适用于短链，比如短链平台不支持openApp协议，此时可以用NEST_PARSE
     */
    SHORT_URL(5),
    /**
     * 调用静态方法
     */
    INVOKE_STATIC_METHOD(6),
    ;

    DynamicParseTypeEnum(Integer type) {
        this.type = type;

    }
    /** */
    private Integer type;

    public Integer getType() {
        return type;
    }
}
