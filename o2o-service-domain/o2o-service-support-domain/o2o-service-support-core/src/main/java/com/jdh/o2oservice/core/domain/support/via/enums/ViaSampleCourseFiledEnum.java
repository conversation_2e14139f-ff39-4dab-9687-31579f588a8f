package com.jdh.o2oservice.core.domain.support.via.enums;

/**
 * 采样教程楼层属性
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/2 16:32
 */
public enum ViaSampleCourseFiledEnum {
    /**
     * 采样教程楼层是属性
     */
    VIDEO_HEADER_IMAGE_URL("tutorialVideoThumbnailUrl", "采样视频首图"),
    VIDEO_URL("tutorialVideoUrl", "采样视频文件地址"),
    IMAGES_URL("tutorialCarouselUrl", "图文采样教程文件"),
    ;


    ViaSampleCourseFiledEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }
}
