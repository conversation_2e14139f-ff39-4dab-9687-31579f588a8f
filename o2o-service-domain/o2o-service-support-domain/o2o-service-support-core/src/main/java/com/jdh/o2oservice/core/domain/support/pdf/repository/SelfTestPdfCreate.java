package com.jdh.o2oservice.core.domain.support.pdf.repository;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.ContentTypeEnum;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.pdf.bo.IndicatorPdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.MedicalPromisePdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateResultBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 自检测pdf生成
 * @Author: wangpengfei144
 * @Date: 2024/6/27
 */
@Component
@Slf4j
public class SelfTestPdfCreate implements PdfCreateProcessor, MapAutowiredKey {

    // 定义全局的字体静态变量
    private static Font titlefont;
    private static Font headfont;
    private static Font keyfont;
    private static Font textfont;
    private static Font textfontAbnormal;
    private static Font headTextfont;
    // 最大宽度
    private static int maxWidth = 520;
    // 静态代码块
    static {
        try {
            // 不同字体（这里定义为同一种字体：包含不同字号、不同style）
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            titlefont = new Font(bfChinese, 16, Font.BOLD);
            headfont = new Font(bfChinese, 14, Font.BOLD);
            keyfont = new Font(bfChinese, 10, Font.BOLD);
            textfont = new Font(bfChinese, 10, Font.NORMAL);
            headTextfont = new Font(bfChinese, 8, Font.NORMAL);
            textfontAbnormal = new Font(bfChinese, 10, Font.NORMAL,BaseColor.RED);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Autowired
    private FileManageService fileManageService;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return "pdfCreateSelfTest";
    }

    /**
     * 创建pdf
     *
     * @param pdfCreateBo
     * @return
     */
    @Override
    public PdfCreateResultBo createPdf(PdfCreateBo pdfCreateBo) {
        log.info("SelfTestPdfCreate->createPdf,pdfCreateBo = {}",JsonUtil.toJSONString(pdfCreateBo));
        String pdfInfo = pdfCreateBo.getPdfInfo();
        MedicalPromisePdfBo medicalPromisePdfBo = JsonUtil.parseObject(pdfInfo, MedicalPromisePdfBo.class);
        if (Objects.isNull(medicalPromisePdfBo)){
            return null;
        }
        try {
            // 1.新建document对象
            Document document = new Document(PageSize.A4);// 建立一个Document对象
            // 2.建立一个书写器(Writer)与document对象关联
            File file = new File(pdfCreateBo.getPdfName());
            file.createNewFile();
            PdfWriter writer = PdfWriter.getInstance(document, Files.newOutputStream(file.toPath()));

            // 3.打开文档
            document.open();
            document.newPage();
            log.info("SelfTestPdfCreate->createPdf,paragraph start ");
            // 段落
            Paragraph paragraph = new Paragraph();
            Paragraph paragraphPatient = new Paragraph();
//            Paragraph paragraphCheckTime = new Paragraph();
//            Paragraph paragraphItem = new Paragraph();

            PdfPTable tablePatient = new PdfPTable(3);
            //检测人 性别 年龄 检测项目 检测时间 样本编号

            String userName = medicalPromisePdfBo.getUserName();
            String checkTime = medicalPromisePdfBo.getCheckTime();
            String serviceItemName = medicalPromisePdfBo.getServiceItemName();
            Integer age = medicalPromisePdfBo.getAge();
            String userGenderStr = medicalPromisePdfBo.getUserGenderStr();
            String specimenCode = medicalPromisePdfBo.getSpecimenCode();


            tablePatient.addCell(createCell("检测人:"+userName, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCell("性别:"+userGenderStr, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCell("年龄:"+age, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCell("检测项目:"+serviceItemName, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCell("检测时间:"+checkTime, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCell("检测条码:"+specimenCode, headTextfont, Element.ALIGN_LEFT, 1, false));

            int cellCount = 6;

            if (StringUtil.isNotBlank(medicalPromisePdfBo.getTestUser())){
                cellCount++;
                tablePatient.addCell(createCell("检测人:"+medicalPromisePdfBo.getTestUser(), headTextfont, Element.ALIGN_LEFT, 1, false));
            }
            if (StringUtil.isNotBlank(medicalPromisePdfBo.getCheckUser())){
                cellCount++;
                tablePatient.addCell(createCell("审核人:"+medicalPromisePdfBo.getCheckUser(), headTextfont, Element.ALIGN_LEFT, 1, false));
            }
            if (cellCount%3 != 0){
                int num = 3-cellCount%3;
                for (int i = 0; i < num; i++) {
                    tablePatient.addCell(createCell("", headTextfont, Element.ALIGN_LEFT, 1, false));
                }
            }

            paragraphPatient.add(tablePatient);


            // 表格
            PdfPTable table = new PdfPTable(4);

            table.setSplitLate(false); // 表示在当前页放不下表格时，强制换页
            table.setSplitRows(true); // 表示跨行时允许自动换页

            table.addCell(createCellTopBot("指标名称", textfont));
            table.addCell(createCellTopBot("结果值", textfont));
            table.addCell(createCellTopBot("指标正常范围", textfont));
            table.addCell(createCellTopBot("指标结果描述", textfont));


            List<IndicatorPdfBo> indicatorPdfBos = medicalPromisePdfBo.getIndicatorPdfBos();


            for (int i = 0; i < indicatorPdfBos.size(); i++) {
                Font font = null;
                IndicatorPdfBo indicatorPdfBo = indicatorPdfBos.get(i);
                if (!StringUtil.equals(indicatorPdfBo.getAbnormalType(), CommonConstant.ZERO_STR)){
                    font = textfontAbnormal;
                }else {
                    font = textfont;
                }
                String valueDescription = StringUtil.equals(CommonConstant.ZERO_STR,indicatorPdfBo.getAbnormalType()) ? "正常" : "异常";
                if (Objects.equals(i, indicatorPdfBos.size() - 1)) {
                    table.addCell(createCellBot(indicatorPdfBo.getIndicatorName(), font));
                    table.addCell(createCellBot(indicatorPdfBo.getValue(), font));
                    table.addCell(createCellBot(indicatorPdfBo.getNormalRangeValue(), font));
                    table.addCell(createCellBot(valueDescription, font));
                    continue;
                }

                table.addCell(createCell(indicatorPdfBo.getIndicatorName(), font));
                table.addCell(createCell(indicatorPdfBo.getValue(), font));
                table.addCell(createCell(indicatorPdfBo.getNormalRangeValue(), font));
                table.addCell(createCell(valueDescription, font));
            }
            paragraph.add(table);

            PdfPTable tableCheckTime = new PdfPTable(1);
            Paragraph paragraphCheckTime = new Paragraph();
            tableCheckTime.addCell(createCellS("声明：本报告仅对该检测样本负责", headTextfont, Element.ALIGN_LEFT, 1, false));
            paragraphCheckTime.add(tableCheckTime);

            document.add(paragraphPatient);
            document.add(paragraph);
            document.add(paragraphCheckTime);
            log.info("SelfTestPdfCreate->createPdf,paragraph document add end ");

            // 5.关闭文档
            document.close();
            writer.close();
//
            PdfCreateResultBo pdfCreateResultBo = new PdfCreateResultBo();
            pdfCreateResultBo.setResult(Boolean.TRUE);
            pdfCreateResultBo.setPdfName(pdfCreateBo.getPdfName());
            if (pdfCreateBo.getUploadToOss()){
                PutFileResult put = fileManageService.put(pdfCreateBo.getPdfName(), Files.newInputStream(file.toPath()), FileManageServiceImpl.FolderPathEnum.REPORT, ContentTypeEnum.PDF.getValue(),Boolean.FALSE);
                pdfCreateResultBo.setOssPath(put.getFilePath());
            }
            file.delete();
            return pdfCreateResultBo;
        }catch (Exception e){
            log.info("SelfTestPdfCreate,createPdfError,pdfCreateBo={}",JsonUtil.toJSONString(pdfCreateBo),e);
        }
        return null;
    }



    /**------------------------创建表格单元格的方法start----------------------------*/
    /**
     * 创建单元格(指定字体)
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }

        /**
     * 创建单元格(指定字体),上下边框
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createCellTopBot(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.TOP | PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }
           /**
     * 创建单元格(指定字体),下边框
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createCellBot(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }

    /**
     * 创建单元格（指定字体、水平居..、单元格跨x列合并、设置单元格内边距）
     * @param value
     * @param font
     * @param align
     * @param colspan
     * @param boderFlag
     * @return
     */
    public PdfPCell createCell(String value, Font font, int align, int colspan, boolean boderFlag) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(align);
        cell.setColspan(colspan);
        cell.setPhrase(new Phrase(value, font));
        cell.setPadding(3.0f);
        if (!boderFlag) {
            cell.setBorder(0);
            cell.setPaddingTop(0.0f);
//            cell.setPaddingBottom(8.0f);
        } else if (boderFlag) {
            cell.setBorder(0);
            cell.setPaddingTop(0.0f);
//            cell.setPaddingBottom(15.0f);
        }
        return cell;
    }

    public static PdfPCell createCellS(String value, Font font, int align, int colspan, boolean boderFlag) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(align);
        cell.setColspan(colspan);
        cell.setPhrase(new Phrase(value, font));
        cell.setPadding(3.0f);
        if (!boderFlag) {
            cell.setBorder(0);
            cell.setPaddingTop(0.0f);
//            cell.setPaddingBottom(8.0f);
        } else if (boderFlag) {
            cell.setBorder(0);
            cell.setPaddingTop(0.0f);
//            cell.setPaddingBottom(15.0f);
        }
        return cell;
    }

    public static PdfPCell createCellS(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }

            /**
     * 创建单元格(指定字体),上下边框
     * @param value
     * @param font
     * @return
     */
    public static PdfPCell createCellTopBotS(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.TOP | PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }
           /**
     * 创建单元格(指定字体),下边框
     * @param value
     * @param font
     * @return
     */
    public static PdfPCell createCellBotS(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        return cell;
    }

    public static void main(String[] args) {
        String s = "{\"pdfInfo\":\"{\\\"checkTime\\\":\\\"2024-08-17 20:40:31\\\",\\\"specimenCode\\\":\\\"JD123456\\\",\\\"age\\\":\\\"10\\\",\\\"userGenderStr\\\":\\\"男\\\",\\\"indicatorPdfBos\\\":[{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"新型冠状病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"人腺病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"肺炎衣原体\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"人冠状病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"3\\\",\\\"indicatorName\\\":\\\"甲流\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阳性\\\",\\\"valueDescription\\\":\\\"阳性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"人鼻病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"肺炎链球菌\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"人副流感病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"乙流\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"肺炎支原体\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"流感嗜血杆菌\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"indicatorName\\\":\\\"合胞病毒\\\",\\\"normalRangeValue\\\":\\\"阴性\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"}],\\\"medicalPromiseId\\\":159303337509289,\\\"serviceItemName\\\":\\\"呼吸道病毒细菌12联检\\\",\\\"userName\\\":\\\"陈历锋\\\"}\",\"pdfName\":\"159303337509289_240817220229689.pdf\",\"template\":\"pdfCreateSelfTest\",\"uploadToOss\":true}";
        PdfCreateBo pdfCreateBo = JsonUtil.parseObject(s, PdfCreateBo.class);
        log.info("SelfTestPdfCreate->createPdf,pdfCreateBo = {}", JsonUtil.toJSONString(pdfCreateBo));
        String pdfInfo = pdfCreateBo.getPdfInfo();
        MedicalPromisePdfBo medicalPromisePdfBo = JsonUtil.parseObject(pdfInfo, MedicalPromisePdfBo.class);

        try {
            // 1.新建document对象
            Document document = new Document(PageSize.A4);// 建立一个Document对象
            // 2.建立一个书写器(Writer)与document对象关联
            File file = new File("testFile.pdf");
            file.createNewFile();
            PdfWriter writer = PdfWriter.getInstance(document, Files.newOutputStream(file.toPath()));

            // 3.打开文档
            document.open();
            document.newPage();
            log.info("SelfTestPdfCreate->createPdf,paragraph start ");
            //            Paragraph paragraphCheckTime = new Paragraph();
//            Paragraph paragraphItem = new Paragraph();
            Paragraph paragraph = new Paragraph();
            Paragraph paragraphPatient = new Paragraph();
            PdfPTable tablePatient = new PdfPTable(3);
            //检测人 性别 年龄 检测项目 检测时间 样本编号

            String userName = medicalPromisePdfBo.getUserName();
            String checkTime = medicalPromisePdfBo.getCheckTime();
            String serviceItemName = medicalPromisePdfBo.getServiceItemName();
            Integer age = medicalPromisePdfBo.getAge();
            String userGenderStr = medicalPromisePdfBo.getUserGenderStr();
            String specimenCode = medicalPromisePdfBo.getSpecimenCode();


            tablePatient.addCell(createCellS("检测人:"+userName, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCellS("性别:"+userGenderStr, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCellS("年龄:"+age, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCellS("检测项目:"+serviceItemName, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCellS("检测时间:"+checkTime, headTextfont, Element.ALIGN_LEFT, 1, false));
            tablePatient.addCell(createCellS("检测条码:"+specimenCode, headTextfont, Element.ALIGN_LEFT, 1, false));
            paragraphPatient.add(tablePatient);

            // 表格
            PdfPTable table = new PdfPTable(4);

            table.setSplitLate(false); // 表示在当前页放不下表格时，强制换页
            table.setSplitRows(true); // 表示跨行时允许自动换页
//
//            table.addCell(createCell("检测人:"+medicalPromisePdfBo.getUserName(), textfont, Element.ALIGN_LEFT, 1, false));
//            table.addCell(createCell("检测时间:"+medicalPromisePdfBo.getCheckTime(), textfont, Element.ALIGN_LEFT, 1, false));
//            table.addCell(createCell("检测项目:"+medicalPromisePdfBo.getServiceItemName(), textfont, Element.ALIGN_LEFT, 1, false));
//            table.addCell(createCell("", textfont, Element.ALIGN_LEFT, 1, false));

            table.addCell(createCellTopBotS("指标名称", textfont));
            table.addCell(createCellTopBotS("结果值", textfont));
            table.addCell(createCellTopBotS("指标正常范围", textfont));
            table.addCell(createCellTopBotS("指标结果描述", textfont));


            List<IndicatorPdfBo> indicatorPdfBos = medicalPromisePdfBo.getIndicatorPdfBos();


            for (int i = 0; i < indicatorPdfBos.size(); i++) {
                Font font = null;
                IndicatorPdfBo indicatorPdfBo = indicatorPdfBos.get(i);
                if (!StringUtil.equals(indicatorPdfBo.getAbnormalType(), CommonConstant.ZERO_STR)){
                    font = textfontAbnormal;
                }else {
                    font = textfont;
                }
                String valueDescription = StringUtil.equals(CommonConstant.ZERO_STR,indicatorPdfBo.getAbnormalType()) ? "正常" : "异常";
                if (Objects.equals(i, indicatorPdfBos.size() - 1)) {
                    table.addCell(createCellBotS(indicatorPdfBo.getIndicatorName(), font));
                    table.addCell(createCellBotS(indicatorPdfBo.getValue(), font));
                    table.addCell(createCellBotS(indicatorPdfBo.getNormalRangeValue(), font));
                    table.addCell(createCellBotS(valueDescription, font));
                    continue;
                }

                table.addCell(createCellS(indicatorPdfBo.getIndicatorName(), font));
                table.addCell(createCellS(indicatorPdfBo.getValue(), font));
                table.addCell(createCellS(indicatorPdfBo.getNormalRangeValue(), font));
                table.addCell(createCellS(valueDescription, font));
            }
            paragraph.add(table);


            PdfPTable tableCheckTime = new PdfPTable(1);
            Paragraph paragraphCheckTime = new Paragraph();
            tableCheckTime.addCell(createCellS("声明：本报告仅对该检测样本负责", headTextfont, Element.ALIGN_LEFT, 1, false));
            paragraphCheckTime.add(tableCheckTime);



            document.add(paragraphPatient);
            document.add(paragraph);
            document.add(paragraphCheckTime);
            log.info("SelfTestPdfCreate->createPdf,paragraph document add end ");

            // 5.关闭文档
            document.close();
            writer.close();
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
    }
