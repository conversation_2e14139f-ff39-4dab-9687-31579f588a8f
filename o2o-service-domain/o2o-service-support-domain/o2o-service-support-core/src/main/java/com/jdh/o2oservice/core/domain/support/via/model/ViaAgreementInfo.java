package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * 授权协议说明，举例：
 * {
 *    "agreeText":" 同意协议的提示文本（占位符 %）",
 *    "name":"用户授权协议",
 *    "url":" 协议链接",
 *    "content":"1.免疫功能缺陷人群，不宜接种；<br />2.怀孕、哺乳期、半年内有怀孕计划的女性不宜接种；<br />3.接种后应在正规接种单位停留观察30分钟。"
 *  }
 */
@Data
public class ViaAgreementInfo {

    /**
     * 授权协议文本
     */
    private String agreeText;

    /**
     * agreeList
     */
    List<ViaAgreementContentInfo> agreeList;

}
