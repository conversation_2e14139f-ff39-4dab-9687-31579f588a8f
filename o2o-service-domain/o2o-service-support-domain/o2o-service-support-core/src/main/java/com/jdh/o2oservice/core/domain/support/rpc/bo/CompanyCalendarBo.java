package com.jdh.o2oservice.core.domain.support.rpc.bo;
import lombok.Data;

import java.io.Serializable;
@Data
public class CompanyCalendarBo implements Serializable {

    /**
     * yyyy-MM-dd或MM-dd
     * 需要添加上年
     * 比如取2024年1月1-2024年12月31 返回需要是2024年10月1
     */
    private String date;

    /**
     * ("public_holiday", "法定节假日"),
     * ("rest_day", "休息日"),
     * ("workday", "工作日"),
     */
    private String type;

    /**
     * 节假日名称
     */
    private String holidayName;

    /**
     * 班次名称
     */
    private String shiftName;
}
