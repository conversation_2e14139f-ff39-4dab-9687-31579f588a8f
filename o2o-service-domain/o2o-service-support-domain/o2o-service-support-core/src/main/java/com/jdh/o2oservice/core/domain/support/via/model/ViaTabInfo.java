package com.jdh.o2oservice.core.domain.support.via.model;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

import java.util.Map;

/**
 * @author: yang<PERSON>yu
 * @date: 2024/3/13 4:27 下午
 * @version: 1.0
 */
@Data
public class ViaTabInfo {
    /**
     * 标签
     */
    private String name;

    /**
     * 按钮唯一标识
     */
    private String code;


    /**
     * 具体action
     */
    private ViaActionInfo action;

    /**
     *
     */
    private String scene;

    public void init(Map<String, Object> data){
        if (CollectionUtil.isEmpty(data)){
            return;
        }
        action.init(data);
    }
}
