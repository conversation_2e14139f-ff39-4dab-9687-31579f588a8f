package com.jdh.o2oservice.core.domain.support.via.enums;
import lombok.Getter;

@Getter
public enum ViaStepGuideEnum {

    PAY("pay","下单支付"),

    RISK_ASSESSMENT("riskAssessment","风险评估"),

    DISPATCH_VISIT("dispatchVisit","派单上门"),

    SERVICE("service","提供服务"),

    COMPLETE("complete","服务完成"),
    ;


    ViaStepGuideEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * status
     */
    private final String code;

    /**
     * desc
     */
    private final String desc;
}
