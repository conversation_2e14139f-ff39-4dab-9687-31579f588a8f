package com.jdh.o2oservice.core.domain.support.rpc;

import com.jdh.o2oservice.core.domain.support.rpc.bo.SoundRecordingDetailBO;
import com.jdh.o2oservice.core.domain.support.rpc.param.SoundRecordingParam;

import java.util.List;

/**
 * @ClassName SoundRecordingRpc
 * @Description
 * <AUTHOR>
 * @Date 2025/6/17 13:27
 **/
public interface SoundRecordingRpc {

    /**
     *
     * @param param
     * @return
     */
    List<SoundRecordingDetailBO> querySoundRecordingDetail(SoundRecordingParam param);
}