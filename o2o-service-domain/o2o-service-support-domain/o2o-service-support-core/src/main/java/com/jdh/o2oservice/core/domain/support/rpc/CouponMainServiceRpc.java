package com.jdh.o2oservice.core.domain.support.rpc;

import com.jdh.o2oservice.core.domain.support.rpc.bo.QueryBindAccountInfoBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.QueryBindAccountInfoParam;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/8/17
 * @description 人资主数据
 */
public interface CouponMainServiceRpc {
    /**
     * 查询人资接口
     * @param queryBindAccountInfoParam
     * @return
     * @throws IOException
     */
    QueryBindAccountInfoBo queryBindAccountInfo(QueryBindAccountInfoParam queryBindAccountInfoParam) throws IOException;

}
