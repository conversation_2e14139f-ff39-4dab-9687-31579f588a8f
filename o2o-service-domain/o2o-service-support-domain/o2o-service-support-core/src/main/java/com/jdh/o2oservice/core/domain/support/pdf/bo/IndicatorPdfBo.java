package com.jdh.o2oservice.core.domain.support.pdf.bo;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 指标Bo
 * @Author: wangpengfei144
 * @Date: 2024/6/28
 */
@Data
public class IndicatorPdfBo {



    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 指标编值
     */
    private String value;
    /**
     * 单位
     */
    private String unit;
    /**
     * 异常标记 0-正常 1-偏低 2-偏高 3-异常
     * 12联呼吸道病毒检测：0、3
     */
    private String abnormalType;
    /**
     * 参考值范围
     */
    private String normalRangeValue;

    /**
     * 指标检测结果描述  定量检测（例：红细胞计数）描述：正常、偏低、偏高；
     * 定性检测（例：HPV11型基因分型）描述：阴性、阳性
     * 12联呼吸道病毒检测描述：阴性、阳性
     */
    private String valueDescription;
    /**
     * 异常指标建议
     */
    private String advice;

    /**
     * ct结果值，明确定义，用于浓度计算
     */
    private String ctValue;

    /**
     * 检测方法
     */
    private String testingMethod;

    /**
     * 参考范围值
     */
    private String referenceRangeValue;


}
