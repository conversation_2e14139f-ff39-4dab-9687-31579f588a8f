package com.jdh.o2oservice.core.domain.support.rpc;

import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelLocationRealParam;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelRealTrackParam;

/**
 * @ClassName AngelRealTrackRpc
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/1 01:30
 */
public interface AngelRealTrackRpc {

    /**
     * 查询服务者经纬度
     *
     * @param angelRealTrackParam
     * @return
     */
    AngelRealTrackBo queryAngelRealTrack(AngelRealTrackParam angelRealTrackParam);

    /**
     * 查询服务者经纬度
     *
     * @param angelLocationRealParam
     * @return
     */
    AngelLocationBo queryAngelLocationRealTrack(AngelLocationRealParam angelLocationRealParam);

}
