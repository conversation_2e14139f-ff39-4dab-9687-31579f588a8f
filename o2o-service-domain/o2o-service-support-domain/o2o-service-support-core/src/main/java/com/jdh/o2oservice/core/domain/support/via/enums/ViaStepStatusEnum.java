package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 步骤条状态
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaStepStatusEnum {
    /**
     * //stepStatus 指定状态。'wait' | 'process' | 'finish'
     */
    FINISH("finish","完成"),

    PROCESS("process","进行中"),

    WAIT("wait","等待"),
    ;


    /**
     * 步骤状态
     *
     * @param status 地位
     * @param desc   desc
     */
    ViaStepStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * status
     */
    private final String status;

    /**
     * desc
     */
    private final String desc;
}
