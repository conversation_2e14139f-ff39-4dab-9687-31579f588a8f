package com.jdh.o2oservice.core.domain.support.rpc.bo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName AngelLocationModel
 * @Description 服务者位置model
 *
 * <AUTHOR>
 * @Date 2025/8/25 11:13
 */
@Data
public class AngelLocationBo {

    /**
     * 服务者id
     */
    private String angelId;

    /**
     * 纬度
     */
    private double angelLat;

    /**
     * 经度
     */
    private double angelLng;

    /**
     * 用户家距离
     */
    private UserDistanceBo userDistanceBo;

    /**
     * 实验室距离
     */
    private List<StationDistanceBo> stationDistanceBoList;
}
