package com.jdh.o2oservice.core.domain.support.pdf.bo;
import lombok.Data;
import java.util.List;

/**
 * @Description: 检测单创建pdfBo
 * @Author: wangpengfei144
 * @Date: 2024/6/28
 */
@Data
public class MedicalPromisePdfBo {
    /**
     * medicalPromiseId
     */
    private Long medicalPromiseId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 检测时间
     */
    private String checkTime;
    /**
     * 检测项目
     */
    private String serviceItemName;
    /**
     * 指标list
     */
    private List<IndicatorPdfBo> indicatorPdfBos;
    /**
     * 性别
     */
    private String userGenderStr;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 样本编号
     */
    private String specimenCode;

    /**
     * 采样时间
     */
    private String sampleTime;

    /**
     * 报告生成时间
     */
    private String reportTime;

    /**
     * 样本性状
     */
    private String sampleCharacteristics;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 检测人
     */
    private String testUser;

    /**
     * 审核人
     */
    private String checkUser;

}
