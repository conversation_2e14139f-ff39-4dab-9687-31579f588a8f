package com.jdh.o2oservice.core.domain.support.pdf.service;

import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateResultBo;

/**
 * @Description: pdf
 * @Interface: PdfDomainService
 * @Author: wangpengfei144
 * @Date: 2024/6/27
 */
public interface PdfDomainService {

    /**
     * 生成pdf
     * @param pdfCreateBo
     * @return
     */
    PdfCreateResultBo createPdf(PdfCreateBo pdfCreateBo);

}
