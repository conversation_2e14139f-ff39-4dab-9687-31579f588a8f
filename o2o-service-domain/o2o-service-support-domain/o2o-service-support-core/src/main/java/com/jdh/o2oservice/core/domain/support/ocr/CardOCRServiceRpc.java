package com.jdh.o2oservice.core.domain.support.ocr;

import com.jdh.o2oservice.core.domain.support.ocr.request.AngelIdCardRequest;
import com.jdh.o2oservice.core.domain.support.ocr.result.AngelIdCardResult;

/**
 * @ClassName CardOCRServiceRpc
 * @Description
 * <AUTHOR>
 * @Date 2025/7/26 17:38
 */
public interface CardOCRServiceRpc {

    /**
     * 识别服务者身份证信息
     *
     * @param idCardRequest
     * @return
     */
    AngelIdCardResult queryAngelIdCardInfo(AngelIdCardRequest idCardRequest);

}
