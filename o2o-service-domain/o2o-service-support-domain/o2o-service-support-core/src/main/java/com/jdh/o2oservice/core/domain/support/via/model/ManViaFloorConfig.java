package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * 页面配置dto
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class ManViaFloorConfig {

    /**
     * 允许访问的erp
     */
    private List<String> accessErp;

    /**
     * 允许访问的角色
     */
    private List<String> accessRoleCode;

    /**
     * 楼层唯一key
     */
    private String key;

    /**
     * 楼层描述
     */
    private String name;

    /**
     * 楼层展示条件
     */
    private String showExpression;

    /**
     * 楼层元素列表
     */
    List<ManViaFloorElementConfig> floorElementConfigList;
}
