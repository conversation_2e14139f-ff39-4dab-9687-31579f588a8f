package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * ViaFloorInfo
 */
@Data
public class ViaFloorInfo {

    /**
     * 楼层代码
     */
    private String floorCode;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 楼层配置列表
     */
    private List<ViaFloorConfig> floorConfigList;

    /**
     * 是否给前端下发,且渲染出来
     */
    private Boolean remove=false;

}
