package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.util.List;

/**
 * 运营端页面配置
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class ManViaConfig {

    /**
     * 允许访问的erp
     */
    private List<String> accessErp;

    /**
     * 允许访问的角色
     */
    private List<String> accessRoleCode;

    /**
     * 页面楼层配置
     */
    private List<ManViaFloorConfig> floorConfigList;
}
