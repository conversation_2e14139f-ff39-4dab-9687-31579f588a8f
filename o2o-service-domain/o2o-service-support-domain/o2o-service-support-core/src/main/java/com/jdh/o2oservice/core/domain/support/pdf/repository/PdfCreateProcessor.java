package com.jdh.o2oservice.core.domain.support.pdf.repository;

import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateResultBo;

/**
 * @Description: pdf创建
 * @Interface: PdfCreateProcessor
 * @Author: wangpengfei144
 * @Date: 2024/6/27
 */
public interface PdfCreateProcessor {

    /**
     * 创建pdf
     * @param pdfCreateBo
     * @return
     */
    PdfCreateResultBo createPdf(PdfCreateBo pdfCreateBo);
    
}
