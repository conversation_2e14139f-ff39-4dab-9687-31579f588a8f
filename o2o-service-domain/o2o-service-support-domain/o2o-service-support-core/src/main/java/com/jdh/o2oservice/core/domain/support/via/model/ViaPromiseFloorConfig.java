package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 服务单楼层配置实体
 */
@Data
public class ViaPromiseFloorConfig implements Serializable {
    /**
     * 标题
     */
    private String title;
    /**
     * 消费码
     */
    private String promiseCode;
     /**
     * 字段key
     */
    private String fieldKey;
    /**
     * 字段值
     */
    private String fieldValue;
       /**
     * 按钮集合
     */
    private List<ViaBtnInfo> btnList;
    /**
     * viaStatus 展示的状态值
     */
    private Integer viaStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 具体action
     */
    private ViaActionInfo action;

    /**
     * 表单项
     */
    private List<ViaFormItem> formItemList;
}
