<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>o2o-service-domain</artifactId>
        <groupId>com.jdh.o2oservice</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>o2o-service-support-domain</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>o2o-service-support-core</module>
        <module>o2o-service-support-core-ext</module>
    </modules>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- 藏金阁Matrix -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>matrix2-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-common</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>
</project>