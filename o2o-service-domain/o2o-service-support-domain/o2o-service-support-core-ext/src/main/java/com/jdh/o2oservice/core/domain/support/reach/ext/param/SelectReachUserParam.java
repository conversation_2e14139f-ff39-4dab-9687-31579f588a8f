package com.jdh.o2oservice.core.domain.support.reach.ext.param;

import java.util.Map;

/**
 * 圈选触达人群，扩展点入参
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/17 9:35 下午
 * @version: 1.0
 */
public class SelectReachUserParam {


    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件类型编号 */
    private String eventCode;
    /** 事件主体 */
    private String aggregateId;

    /** 模版ID */
    private Long templateId;
    /**
     *
     */
    private Map<String,Object> eventBody;

    public String getDomainCode() {
        return domainCode;
    }

    public void setDomainCode(String domainCode) {
        this.domainCode = domainCode;
    }

    public String getAggregateCode() {
        return aggregateCode;
    }

    public void setAggregateCode(String aggregateCode) {
        this.aggregateCode = aggregateCode;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getAggregateId() {
        return aggregateId;
    }

    public void setAggregateId(String aggregateId) {
        this.aggregateId = aggregateId;
    }

    public Map<String, Object> getEventBody() {
        return eventBody;
    }

    public void setEventBody(Map<String, Object> eventBody) {
        this.eventBody = eventBody;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }
}
