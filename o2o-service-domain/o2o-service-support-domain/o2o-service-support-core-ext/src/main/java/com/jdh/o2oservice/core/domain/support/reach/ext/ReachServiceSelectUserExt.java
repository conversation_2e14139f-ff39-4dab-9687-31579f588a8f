package com.jdh.o2oservice.core.domain.support.reach.ext;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jdh.o2oservice.core.domain.support.reach.ext.dto.ReachUser;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachUserParam;

import java.util.List;

/**
 * 触达服务扩展点
 * @author: yangxiyu
 * @date: 2024/4/17 9:28 下午
 * @version: 1.0
 */
public interface ReachServiceSelectUserExt {



    /**
     * todao待定义
     * @param param
     */
    @DomainAbilityExtension(
            code = "",
            name = "圈定触达人员信息"
    )
    List<ReachUser> selectUsers(SelectReachUserParam param);


    /**
     * 返回当前能力点的唯一标识
     * domainCode开头，避免冲突
     * @return
     */
    String functionId();


}
