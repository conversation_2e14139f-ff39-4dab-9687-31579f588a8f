package com.jdh.o2oservice.core.domain.support.reach.ext;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachDataParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 触达服务扩展点
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/17 9:28 下午
 * @version: 1.0
 */
public interface ReachServiceSelectDataExt {

    String SERVICE_ITEM_NAME = "topServiceItemName";
    String SERVICE_ITEM_COUNT = "serviceItemCount";


    /**
     * todao待定义
     * @param param
     */
    @DomainAbilityExtension(
            code = "",
            name = "获取触达领域需要的实体数据"
    )
    Map<String, Object> selectData(SelectReachDataParam param);


    /**
     * 返回当前能力点的唯一标识
     * domainCode开头，避免冲突
     * @return
     */
    String functionId();


    default String buildTopServiceName(List<String> itemNames){
        if (CollectionUtils.isEmpty(itemNames)){
            return null;
        }
        String firstName = itemNames.get(0);
        StringBuilder builder = new StringBuilder(firstName);
        for (String itemName : itemNames) {
            if (StringUtils.isNotBlank(itemName) &&!StringUtils.equals(firstName, itemName)){
                builder.append("、");
                builder.append(itemName);
                break;
            }
        }
        // 超过两个项目追加...
        if (itemNames.size() > 2){
            builder.append("...");
        }
        return builder.toString();
    }

}
