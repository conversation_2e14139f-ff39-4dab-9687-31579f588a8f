package com.jdh.o2oservice.core.domain.support.reach.ext.param;

/**
 * 查询触达需要的数据接口
 * @author: yang<PERSON>yu
 * @date: 2024/4/17 9:35 下午
 * @version: 1.0
 */
public class SelectReachDataParam {

    private Long eventId;
    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件主体 */
    private String aggregateId;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getDomainCode() {
        return domainCode;
    }

    public void setDomainCode(String domainCode) {
        this.domainCode = domainCode;
    }

    public String getAggregateCode() {
        return aggregateCode;
    }

    public void setAggregateCode(String aggregateCode) {
        this.aggregateCode = aggregateCode;
    }

    public String getAggregateId() {
        return aggregateId;
    }

    public void setAggregateId(String aggregateId) {
        this.aggregateId = aggregateId;
    }
}
