package com.jdh.o2oservice.core.domain.product.bo;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐详情参数包装
 *
 * <AUTHOR>
 * @date 2024/8/1 17:29
 */
public class ProgramParseBizItemBo implements Serializable {

    /**
     * 核心数据
     */
    private List<RemarkExpandBo> remark;

    /**
     * 属性标识s
     */
    private String tcTag;
    /**
     * 属性名
     */
    private String attrName;


    public List<RemarkExpandBo> getRemark() {
        return remark;
    }

    public void setRemark(List<RemarkExpandBo> remark) {
        this.remark = remark;
    }

    public String getTcTag() {
        return tcTag;
    }

    public void setTcTag(String tcTag) {
        this.tcTag = tcTag;
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }
}
