package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

/**
 * 用户收获地址明细
 * @author: yang<PERSON>yu
 * @date: 2023/11/27 9:57 上午
 * @version: 1.0
 */
@Data
public class UserAddressDetailBO {



    /**
     * 用户pin
     */
    private String userPin;
    /**
     *
     */
//    private Long id;
    private Long addressId;

    /**
     * 默认详细地址
     */
    private String addressDetail;

    /**
     * 移动电话手机号
     */
    private String mobile;

    /**
     * 地址全称（包含级联地址）
     */
    private String fullAddress;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 名称
     */
    private String addressName;
    /**
     * provinceId
     */
    private Integer provinceId;
    /**
     * province 省
     */
    private String provinceName;

    /**
     * cityId
     */
    private Integer cityId;
    /**
     * city 市
     */
    private String cityName;

    /**
     * countyId
     */
    private Integer countyId;
    /**
     * county 县
     */
    private String countyName;

    /**
     *
     */
    private Integer townId;
    /**
     *
     */
    private String townName;
    /**
     * 默认地址
     */
    private boolean addressDefault;

    private boolean selected;

    private double longitude;

    private double latitude;

    private Integer coordType;
}
