package com.jdh.o2oservice.core.domain.product.enums;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @ClassName ProgramBizItemEnNameEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/5/6 16:43
 **/
public enum ProgramBizItemEnNameEnum {

    /**
     * 其他为商品属性
     */
    ITEM_NAME(1, "itemName", "套餐项目名称"),
    ITEM_CATEGORY_NAME(2, "itemCategoryName", "项目一级类目名称"),
    INDICATOR_LIST(3, "indicatorList", "检查项信息"),
    ITEM_PRICE(4, "itemPrice", "套餐项目价格"),
    IMPORTANT_ITEM(5, "importantItem", "是否重点项目"),
    ITEM_EXTRA(6, "itemExtra", "项目扩展字段"),
    ITEM_NO(7, "itemNo", "套餐项目编号"),
    ITEM_CATEGORY_ID(8, "itemCategoryId", "项目一级类目ID"),
    ATTR_VALUE_CONTENT_LIST(9, "attrValueContentList", "属性内容"),
    ITEM_GROUP_NO(10, "itemGroupNo", "分组编号"),
    ITEM_GROUP_NAME(11, "itemGroupName", "分组名称"),
    ITEM_GROUP_TOTAL_NUM(12, "itemGroupTotalNum", "项目组包含的项目数量"),
    ITEM_GROUP_OPTIONAL_NUM(13, "itemGroupOptionalNum", "项目组可选的项目数量"),

    SECOND_BIZCATEGORY_NAME(10, "secondBizCategoryName", "二级分类名称"),
    ITEM_INSPECTIONITEMID(11, "inspectionItemId", "检查项id"),
    ITEM_SIGNIFICANCE(11, "itemSignificance", "检查项意义"),
    ITEM_STANDARDINDICATOR(12, "standardIndicatorList", "指标"),
    ITEM_SUITABLE_LIST(13, "itemSuitableList", "适用人群"),
    ;

    private Integer index;
    private String value;
    private String desc;

    ProgramBizItemEnNameEnum(Integer index, String value, String desc) {
        this.index = index;
        this.value = value;
        this.desc = desc;
    }

    private static Map<String, ProgramBizItemEnNameEnum> ENUM_MAP = Maps.newHashMap();
    static {
        for (ProgramBizItemEnNameEnum value : values()) {
            ENUM_MAP.put(value.getValue(), value);
        }
    }

    public static ProgramBizItemEnNameEnum convertEnum(String value){
        if (StringUtils.isBlank(value)){
            return null;
        }
        return ENUM_MAP.get(value);
    }
    
    public Integer getIndex() {
        return index;
    }
    
    public void setIndex(Integer index) {
        this.index = index;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
}