package com.jdh.o2oservice.core.domain.product.bo.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 商品评价内容对象
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/28 21:56
 * @Vserion: 1.0
 **/
@Data
public class CommentUgcVO {
    /**
     * 	主键guid
     */
    private String guid;
    /**
     * 用户头像
     */
    private String userImageUrl;
    /**
     * 用户名称
     */
    private String nickName;
    /**
     * 等级  评分
     */
    private Integer score;
    /**
     * 评价日期
     */
    private Date creationTime;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 图片集合
     */
    private List<ImageVO> images;
    /**
     * 用户pin
     */
    private String pin;
}
