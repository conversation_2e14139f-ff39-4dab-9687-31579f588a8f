package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * 商品评价列表
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/22 22:52
 * @Vserion: 1.0
 **/
@Data
public class QueryQuesttionParamBO {
    /**
     *
     */
    private SiteBo site;
    /**
     * systemId
     */
    private String systemId = "20";

    /**
     * 提问类型
     */
    private String questionObjType = "sku";
    /**
     * 商品id
     */
    private String productId;
    /**
     * pin
     */
    private String landPin;
    /**
     * clientType
     */
    private Integer clientType = 4;
    /**
     * page
     */
    private Integer page = 1;
    /**
     * pageSize
     */
    private Integer pageSize = 6;
    /**
     * 回答列表
     */
    private String sourceName = "questionsoa.m";
    /**
     * 设备id
     */
    private String uuid;
}
