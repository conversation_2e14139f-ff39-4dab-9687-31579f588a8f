package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName ServiceIndicatorExportDto
 * @Description
 * <AUTHOR>
 * @Date 2024/9/9 20:31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceImagesExportBO {

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id", index = 0)
    private String orderId;

    /**
     * 服务站id
     */
    @ExcelProperty(value = "服务站id", index = 1)
    private String angelStationId;

    /**
     * 护士id
     */
    @ExcelProperty(value = "护士id", index = 2)
    private String angelId;

    /**
     * 服务开始时间
     */
    @ExcelProperty(value = "服务开始时间", index = 3)
    private String serviceStartTime;

    /**
     * 服务结束时间
     */
    @ExcelProperty(value = "服务结束时间", index = 4)
    private String serviceEndTime;

    /**
     * 服务记录
     */
    @ExcelProperty(value = "服务记录", index = 5)
    private String serviceRecordPicUrls;

    /**
     * 护士着装
     */
    @ExcelProperty(value = "护士着装", index = 6)
    private String clothingPicUrls;

    /**
     * 医疗废物处理
     */
    @ExcelProperty(value = "医疗废物处理", index = 7)
    private String wastePicUrls;

    /**
     * 服务状态
     */
    @ExcelProperty(value = "服务状态", index = 8)
    private String workStatus;
}