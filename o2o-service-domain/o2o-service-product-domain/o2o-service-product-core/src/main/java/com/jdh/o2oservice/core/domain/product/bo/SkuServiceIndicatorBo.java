package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.medicine.base.common.util.JsonUtil;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @ClassName:SkuCategoryBo
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/31 10:40
 * @Vserion: 1.0
 **/
@Data
public class SkuServiceIndicatorBo {

    /**
     * 7424：指标分类
     */
    private String indicatorCategoryAttrId;
    /**
     * 7444: 营业时间分类
     */
    private String businessHoursCategoryAttrId;
    /**
     * 6737：适用人群分类
     */
    private String suitableCategoryAttrId;
    /**
     * 439544：男
     */
    private List<String> suitableAttrIds;
    /**
     * 适用人群
     */
    private String suitable;
    /**
     * 439564：营业时间
     */
    private String businessHoursAttrId;
    /**
     * 营业时间
     */
    private String businessHours;

    public static void main(String[] args) {
        SkuServiceIndicatorBo skuServiceIndicatorBo = new SkuServiceIndicatorBo();
        skuServiceIndicatorBo.setIndicatorCategoryAttrId("7424");
        skuServiceIndicatorBo.setBusinessHoursCategoryAttrId("7444");
        skuServiceIndicatorBo.setSuitableCategoryAttrId("6737");
        skuServiceIndicatorBo.setSuitableAttrIds(Arrays.asList("439544", "439545", "439546"));
        skuServiceIndicatorBo.setBusinessHoursAttrId("439564");
        System.out.println(JsonUtil.toJSONString(skuServiceIndicatorBo));
    }
}
