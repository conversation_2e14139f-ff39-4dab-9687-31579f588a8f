package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName ReviseProductExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2024/9/1 16:07
 **/
@Data
public class ReviseProductExcelBO {

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID", index = 0)
    private String productId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 1)
    private String productName;

    /**
     * 商家ID
     */
    @ExcelProperty(value = "商家ID", index = 2)
    private String venderId;

    /**
     * 老项目ID
     */
    @ExcelProperty(value = "老项目ID", index = 3)
    private Long oldItemId;

    /**
     * 老项目名称
     */
    @ExcelProperty(value = "老项目名称", index = 4)
    private String oldItemName;

    /**
     * 老指标编码
     */
    @ExcelProperty(value = "老指标编码", index = 5)
    private Long oldIndicatorId;

    /**
     * 老指标名称
     */
    @ExcelProperty(value = "老指标名称", index = 6)
    private String oldIndicatorName;

    /**
     * 新项目名称
     */
    @ExcelProperty(value = "新项目名称", index = 7)
    private String newItemName;

    /**
     * <pre>
     * 新项目适用人群 1-男 2-女未婚 3-女已婚 [1,2,3]
     * </pre>
     */
    @ExcelProperty(value = "新项目适用人群", index = 8)
    private String newItemSuitable;

    /**
     * 新指标名称
     */
    @ExcelProperty(value = "新指标名称", index = 9)
    private String newIndicatorName;

    /**
     * <pre>
     * 京东健康侧标准指标编码
     * </pre>
     */
    @ExcelProperty(value = "京东健康侧标准指标编码", index = 10)
    private String healthIndicatorCode;

}