package com.jdh.o2oservice.core.domain.product.rpc.bo;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @Description 优惠券信息
 * @Date 2024/10/22 下午7:56
 * <AUTHOR>
 **/
@Data
public class CouponInfoBO {
    /**
     * 活动加密串
     */
    private String encryptedKey;

    /**
     * 活动id
     */
    private String roleId;

    /**
     * 活动开始时间
     */
    private Date activityBeginTime;

    /**
     * 活动结束时间
     */
    private Date activityEndTime;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 优惠券类型：0-京券，1-东券，2-运费券，10-快递立减券，200-讯联立减券，201-讯联满减券，202-讯联折扣券，203-讯联减至券，204-讯联买免券，300-拍拍直加券，301-拍拍满加券，302-拍拍比例券
     */
    private Integer couponType;

    /**
     * 优惠券分类：0-满减券，满XX减XX；3-折扣券，满XX XX折；28-每满减券，每满XX减XX
     */
    private Integer couponStyle;

    /**
     * 优惠券限制类型【0、全品类】【1、限品类】【2、限店铺】【3、店铺限商品】
     */
    private Integer couponKind;

    /**
     * 折扣金额[默认取整，如需不取整请联系研发同学进行配置]
     */
    private BigDecimal discount;

    /**
     * 折扣描述
     */
    private String discountDesc;

    /**
     * 限额 （满多少才折扣）[默认取整，如需不取整请联系研发同学进行配置]
     */
    private BigDecimal quota;

    /**
     * 全流程用户标签
     */
    private String userLabel;

    /**
     * 优惠券限品类信息[文字]
     */
    private String name;

    /**
     * 领取状态 1可领 2可用
     */
    private Integer useStatus;

    /**
     * 领取状态描述
     */
    private String useStatusDesc;

    /**
     * 组件化扩展信息
     */
    private Map<String, String> componentActiveMap;

}
