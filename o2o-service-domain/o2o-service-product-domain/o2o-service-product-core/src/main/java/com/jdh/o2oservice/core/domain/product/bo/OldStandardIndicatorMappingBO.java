package com.jdh.o2oservice.core.domain.product.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName OldStandardIndicatorMappingBO
 * @Description
 * <AUTHOR>
 * @Date 2024/7/24 11:01
 **/
@Data
public class OldStandardIndicatorMappingBO {

    /**
     * index
     */
    //@ExcelProperty(index = 0)
    //private String index;

    /**
     * 旧 一级
     */
    @ExcelProperty(value = "旧 一级", index = 0)
    private String oldFirstCategory;

    /**
     * 旧 二级
     */
    @ExcelProperty(value = "旧 二级", index = 1)
    //@NotNull(message = "老指标ID（必填）")
    private String oldSecondCategory;

    /**
     * 旧指标 ID
     */
    @ExcelProperty(value = "旧 ID", index = 2)
    private String oldIndicatorId;

    /**
     * 旧指标名称
     */
    @ExcelProperty(value = "旧 指标", index = 3)
    //@NotNull(message = "老指标名称（必填）")
    private String oldIndicatorName;


    /**
     * 旧项目名称
     */
    //@ExcelProperty(value = "旧项目名称", index = 5)
    //@NotNull(message = "老指标名称（必填）")
    //private String oldItemName;

    /**
     * 是否需新增
     */
    //@ExcelProperty(value = "是否需新增", index = 6)
    //private String needCreate;

    /**
     * 新指标名称
     */
    @ExcelProperty(value = "标准指标", index = 4)
    private String standardIndicatorName;

    /**
     * 新指标编码
     */
    @ExcelProperty(value = "标准 ID", index = 5)
    private String healthIndicatorCode;

    //-------------------------

    /**
     * 标准指标-2
     *//*
    @ExcelProperty(value = "标准指标-2", index = 9)
    private String standardIndicatorName2;

    *//**
     * 标准 ID-2
     *//*
    @ExcelProperty(value = "标准 ID-2", index = 10)
    private String healthIndicatorCode2;

    *//**
     * 标准指标-3
     *//*
    @ExcelProperty(value = "标准指标-3", index = 11)
    private String standardIndicatorName3;

    *//**
     * 标准 ID-3
     *//*
    @ExcelProperty(value = "标准 ID-3", index = 12)
    private String healthIndicatorCode3;

    *//**
     * 标准指标-4
     *//*
    @ExcelProperty(value = "标准指标-4", index = 13)
    private String standardIndicatorName4;

    *//**
     * 标准 ID-4
     *//*
    @ExcelProperty(value = "标准 ID-4", index = 14)
    private String healthIndicatorCode4;

    *//**
     * 标准指标-5
     *//*
    @ExcelProperty(value = "标准指标-5", index = 15)
    private String standardIndicatorName5;

    *//**
     * 标准 ID-5
     *//*
    @ExcelProperty(value = "标准 ID-5", index = 16)
    private String healthIndicatorCode5;*/

}