package com.jdh.o2oservice.core.domain.product.rpc;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.product.bo.JdhContentBO;
import com.jdh.o2oservice.core.domain.product.rpc.param.JdhContentContext;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/21 2:42 下午
 * @Description:
 */
public interface ContentExportRpc {

    Response<JdhContentBO> queryContentById(JdhContentContext contentContext);
}
