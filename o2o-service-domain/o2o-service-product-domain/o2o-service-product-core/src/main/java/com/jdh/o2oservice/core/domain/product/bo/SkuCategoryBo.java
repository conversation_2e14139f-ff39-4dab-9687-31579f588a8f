package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * @ClassName:SkuCategoryBo
 * @Description:
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/3/31 10:40
 * @Vserion: 1.0
 **/
@Data
@Builder
public class SkuCategoryBo {

    /**
     * 商品一级类目
     */
    private Long firstSkuCategory;

    /**
     * 商品二级类目
     */
    private Long secondSkuCategory;

    /**
     * 商品三级类目
     */
    private Long thirdSkuCategory;

    /**
     * 指标一级编码
     */
    private Set<Long> firstIndicatorCategory;

    /**
     * 指标二级编码
     */
    private Set<Long> secondIndicatorCategory;

}
