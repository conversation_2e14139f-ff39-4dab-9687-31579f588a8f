package com.jdh.o2oservice.core.domain.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName ItemDangerLevelEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/2 18:45
 */
@Getter
@AllArgsConstructor
public enum ItemDangerLevelEnum {

    /** */
    LOW_DANGER_LEVEL(1, "低风险"),

    MIDDLE_DANGER_LEVEL(2, "中风险"),

    HIGH_DANGER_LEVEL(3, "高风险"),

    ;
    private Integer level;

    private String desc;

    /**
     * 检查不是高风险项目
     *
     * @param level
     * @return
     */
    public static boolean isNotHighDangerLevel(Integer level) {
        if(level == null) {
            return true;
        }
        return LOW_DANGER_LEVEL.getLevel().equals(level) || MIDDLE_DANGER_LEVEL.getLevel().equals(level);
    }

    /**
     * 检查是高风险项目
     *
     * @param level
     * @return
     */
    public static boolean isHighDangerLevel(Integer level) {
        if(level == null) {
            return false;
        }
        return HIGH_DANGER_LEVEL.getLevel().equals(level);
    }
}
