package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

/**
 * <pre>
 *  服务表每天可预约时间段,[{"startTime": "08:00", "endTime": "12:00"}, {"startTime": "14:00", "endTime": "18:00"}]
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-07-15 22:16
 */
@Data
public class JdhProgramDayTimeFrameBO {
    /**
     * <pre>
     * 开始时间
     * </pre>
     */
    private String startTime;

    /**
     * <pre>
     * 结束时间
     * </pre>
     */
    private String endTime;
}