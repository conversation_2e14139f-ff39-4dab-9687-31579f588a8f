package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.health.medical.examination.export.base.dto.ProductServiceIntroduceContentDTO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ProductServiceIntroduce
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 15:38
 **/
@Data
public class ProductServiceIntroduceBO {

    /**
     * 标题
     */
    private String title;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 文本内容
     */
    private String text;
    /**
     *
     */
    private String hintText;

    /**
     * 服务须知
     */
    private List<ProductServiceIntroduceContentBO> introduceContent;
}