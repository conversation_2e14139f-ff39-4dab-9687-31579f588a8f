package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐详情参数包装
 *
 * <AUTHOR>
 * @date 2024/8/1 17:29
 */
@Data
public class ProgramItemExtraBo implements Serializable {

    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 二级商品类目
     */
    private String secondBizCategoryName;
    /**
     * 是否重点项目
     */
    private String importantItem;
    /**
     * 适用人群
     */
    private String itemSuitableList;
    /**
     * 二级分类及检查项列表
     */
    private List<ProgramBizIndicatorBo> indicatorCategoryList;

}
