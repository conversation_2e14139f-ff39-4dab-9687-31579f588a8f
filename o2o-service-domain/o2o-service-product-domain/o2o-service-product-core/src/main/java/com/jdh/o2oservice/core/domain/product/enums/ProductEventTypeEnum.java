package com.jdh.o2oservice.core.domain.product.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.product.event.ProductSkuSaleStatusEventBody;
import lombok.AllArgsConstructor;

/**
 * 商品事件枚举
 *
 * <AUTHOR>
 * @date 2024/06/03
 */
@AllArgsConstructor
public enum ProductEventTypeEnum implements EventType {

    /**
     *
     */
    UPDATE_SALE_STATUS(ProductAggregateEnum.PRODUCT_SKU, "updateSaleStatus", "修改售卖状态", ProductSkuSaleStatusEventBody.class),

    BASE_SAVE(ProductAggregateEnum.PRODUCT_SKU, "baseSave", "SKU基本信息保存", ProductSkuSaleStatusEventBody.class),
    ;

    /**
     * 事件所属的领域
     */


    private ProductAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;
    @Override
    public AggregateCode getAggregateType() {
        return ProductAggregateEnum.PRODUCT_SKU;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

}
