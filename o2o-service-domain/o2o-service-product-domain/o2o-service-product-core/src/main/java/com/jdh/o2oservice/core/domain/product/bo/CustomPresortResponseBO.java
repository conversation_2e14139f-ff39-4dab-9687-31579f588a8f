package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 根据地址查询站点返回DTO
 */
@Data
public class CustomPresortResponseBO implements Serializable {
    /**
     * 业务围栏类型
     */
    private String businessType;
    /**
     *站点ID
     */
    private String stationCode;
    /**
     *站点名称
     */
    private String stationName;
    /**
     *围栏ID
     */
    private String fenceId;
    /**
     *围栏名称
     */
    private String fenceName;
    /**
     *纬度
     */
    private Double lat;
    /**
     *经度
     */
    private Double lng;
}
