package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName:ProductLimitBuyStrategyVO
 * @Description: TODO
 * @Author: yaoqing<PERSON>
 * @Date: 2023/12/11 18:58
 * @Vserion: 1.0
 **/
@Data
public class ProductLimitBuyStrategyBO {

    /**
     * userPin
     */
    private String userPin;

    /**
     * 限购商品结果信息结合
     */
    private List<ProductLimitBuyBO> productLimitBuyBOS;

}
