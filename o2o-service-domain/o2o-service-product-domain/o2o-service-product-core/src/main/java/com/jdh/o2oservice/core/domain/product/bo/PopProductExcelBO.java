package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName PopProductExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2024/7/24 11:01
 **/
@Data
public class PopProductExcelBO {

    /**
     * POP商家公司编号
     */
    @ExcelProperty(value = "POP商家公司编号", index = 0)
    private String venderId;

    /**
     * POP商家公司名称
     */
    @ExcelProperty(value = "POP商家公司名称", index = 1)
    private String venderName;

    /**
     * 是否开通随时退
     */
    @ExcelProperty(value = "是否开通随时退", index = 2)
    private String immediateRefund;

    /**
     * 一级类目
     */
    @ExcelProperty(value = "一级类目", index = 3)
    private String firstCategoryName;

    /**
     * 二级类目
     */
    @ExcelProperty(value = "二级类目", index = 4)
    private String secondCategoryName;

    /**
     * 三级类目
     */
    @ExcelProperty(value = "三级类目", index = 5)
    private String thirdCategoryName;

    /**
     * 商品编码
     */
    @ExcelProperty(value = "商品编码", index = 6)
    private String productId;

    /**
     * SKUID
     */
    @ExcelProperty(value = "SKUID", index = 7)
    private String skuId;

    /**
     * 上下架状态
     */
    @ExcelProperty(value = "上下架状态", index = 8)
    private String onSaleStatusDesc;

    /**
     * SKU名称
     */
    @ExcelProperty(value = "SKU名称", index = 9)
    private String skuName;

    /**
     * 商品短标题
     */
    @ExcelProperty(value = "商品短标题", index = 10)
    private String skuShortTitle;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 11)
    private String productName;
}