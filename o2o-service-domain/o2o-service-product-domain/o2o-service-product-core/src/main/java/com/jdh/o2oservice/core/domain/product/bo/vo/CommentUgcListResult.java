package com.jdh.o2oservice.core.domain.product.bo.vo;

import lombok.Data;

import java.util.List;

/**
 * 商品评价列表结果
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/28 21:56
 * @Vserion: 1.0
 **/
@Data
public class CommentUgcListResult {

    /**
     * 商品评价数量
     */
    private CommentSummaryVO summary;
    /**
     * 商品评价内容对象列表
     */
    private List<CommentUgcVO> comments;
    /**
     * 商品评价标签
     */
    private List<TagStatisticsUgcVO> tagStatistics;
    /**
     *
     */
    private CommentExt ext;
    /**
     * 最大页数
     */
    private Integer maxPage;
    /**
     * 商品评价内容对象
     */
    private CommentUgcVO comment;
}
