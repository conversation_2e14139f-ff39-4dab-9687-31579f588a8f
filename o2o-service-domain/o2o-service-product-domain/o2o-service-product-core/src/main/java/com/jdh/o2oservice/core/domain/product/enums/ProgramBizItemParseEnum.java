package com.jdh.o2oservice.core.domain.product.enums;

/**
 * 商品类型枚举 1：套餐，2：代金券
 *
 * <AUTHOR>
 * @date 2022/10/11
 */
public enum ProgramBizItemParseEnum {
    /**
     * 其他为商品属性
     */
    GROUP(1,"type","分组"),
    SELECT(2,"option","选择"),
    NAME(3,"name","单品"),
    PRICE(4,"price","价格"),
    NUM(5,"num","数量"),
    UNIT(6,"unit","单位"),
    ;
    private Integer index;
    private String value;
    private String desc;

    ProgramBizItemParseEnum(Integer index, String value, String desc) {
        this.index = index;
        this.value = value;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
