package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName StandardItemSuitableExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2024/9/4 16:05
 **/
@Data
public class StandardItemSuitableExcelBO {

    /**
     * 体检项目
     */
    @ExcelProperty(value = "体检项目", index = 0)
    private String itemName;

    /**
     * 项目适用人群
     */
    @ExcelProperty(value = "项目适用人群-洗数据用", index = 1)
    private String itemSuitable;
}