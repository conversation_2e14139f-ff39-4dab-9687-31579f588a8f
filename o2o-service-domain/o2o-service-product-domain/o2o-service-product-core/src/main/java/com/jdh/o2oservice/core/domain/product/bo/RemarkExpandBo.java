package com.jdh.o2oservice.core.domain.product.bo;

import java.io.Serializable;
import java.util.Map;

/**
 * 接收以下结构： { "expand":"火锅类", "prefix":"服务类型", "suffix":"", "features":{ "enName":"type", "groupId":"1" } }
 *
 * <AUTHOR>
 * @date 2024/8/5 17:32
 */
public class RemarkExpandBo implements Serializable {

    /**
     * 显示内容
     */
    private String expand;

    /**
     * 扩展字段
     */
    private Map<String, Object> features;

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public Map<String, Object> getFeatures() {
        return features;
    }

    public void setFeatures(Map<String, Object> features) {
        this.features = features;
    }

}
