package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName CleanPopProductItemExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2024/9/5 21:59
 **/
@Data
public class CleanPopProductItemExcelBO {

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID", index = 0)
    private String productId;

    /**
     * 商家ID
     */
    @ExcelProperty(value = "商家ID", index = 1)
    private String venderId;

    /**
     * 业务模式
     */
    @ExcelProperty(value = "业务模式", index = 2)
    private String businessMode;

    /**
     * SKUID
     */
    @ExcelProperty(value = "SKUID", index = 3)
    private String skuId;

    /**
     * 业务项目ID
     */
    @ExcelProperty(value = "业务项目ID", index = 4)
    private Long bizItemId;

    /**
     * 业务项目名称
     */
    @ExcelProperty(value = "业务项目名称", index = 5)
    private String bizItemName;

    /**
     * 业务项目适用人群
     */
    @ExcelProperty(value = "业务项目适用人群", index = 6)
    private String bizItemSuitable;

    /**
     * 标准项目ID
     */
    @ExcelProperty(value = "标准项目ID", index = 7)
    private Long standardItemId;

    /**
     * 标准项目名称
     */
    @ExcelProperty(value = "标准项目名称", index = 8)
    private String standardItemName;

    /**
     * 标准指标ID
     */
    @ExcelProperty(value = "标准指标ID", index = 9)
    private Long standardIndicatorId;

    /**
     * 标准指标名称
     */
    @ExcelProperty(value = "标准指标名称", index = 10)
    private String standardIndicatorName;
}