package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * 商品评价列表
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/22 22:52
 * @Vserion: 1.0
 **/
@Data
public class EvaluateInfoBO {

    /**
     * 总数
     */
    private String count;

    /**
     * 好评率
     */
    private String positiveComment;

    /**
     * 好评数：模糊展示，显示 10+，100+，1100+，10万+ 字符串
     */
    private String goodCountStr;
    /**
     * 中评数：模糊展示，显示 10+，100+，1100+，10万+ 字符串
     */
    private String generalCountStr;
    /**
     * 差评数：模糊展示，显示 10+，100+，1100+，10万+ 字符串
     */
    private String poorCountStr;

    /**
     * 中评数：模糊展示，显示 10+，100+，1100+，10万+ 字符串
     */
    private List<TagStatisticsBO> commentSummaryList;

    /**
     * 评论标签
     */
    private List<TagStatisticsBO> commentTags;

    /**
     * 评论列表
     */
    private List<CommentUgcBO> list;

    /**
     * 最大页数
     */
    private Integer maxPage;
    /**
     * 最终排序方式 5 推荐排序 ，6 默认置顶时间排序
     */
    private Integer soType;
    /**
     * 当前页数
     */
    private Integer pageNum;
}
