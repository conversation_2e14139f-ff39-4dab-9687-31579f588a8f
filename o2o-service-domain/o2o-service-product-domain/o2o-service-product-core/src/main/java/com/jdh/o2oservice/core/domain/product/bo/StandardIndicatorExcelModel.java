package com.jdh.o2oservice.core.domain.product.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jdh.o2oservice.common.result.excel.BaseExcelModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName StandardIndicatorCateExcelModel
 * @Description
 * <AUTHOR>
 * @Date 2024/7/15 17:28
 **/
@Data
public class StandardIndicatorExcelModel extends BaseExcelModel implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 7570897164345400512L;

    /**
     * 一级类别
     */
    @ExcelProperty(value = "一级类别", index = 0)
    //@NotNull(message = "一级类别（必填）")
    private String firstBizCategoryName;


    /**
     * 二级类别
     */
    @ExcelProperty(value = "二级类别", index = 1)
    //@NotNull(message = "二级类别（必填）")
    private String secondBizCategoryName;

    /**
     * 三级类别
     */
    @ExcelProperty(value = "三级类别", index = 2)
    //@NotNull(message = "三级类别（必填）")
    private String thirdBizCategoryName;

    /**
     * 体检项目
     */
    @ExcelProperty(value = "体检项目", index = 3)
    //@NotNull(message = "体检项目（必填）")
    private String itemName;

    /**
     * 项目意义
     */
    @ExcelProperty(value = "项目意义", index = 4)
    //@NotNull(message = "体检项目（必填）")
    private String itemMean;

    /**
     * 指标数量
     */
    @ExcelProperty(value = "指标数量", index = 5)
    //@NotNull(message = "指标数量（必填）")
    private String indicatorNum;

    /**
     * 体检项目
     */
    @ExcelProperty(value = "应用场景", index = 6)
    //@NotNull(message = "体检项目（必填）")
    private String applySceneDesc;

    /**
     * 检查项目（指标）-整合/备注
     */
    @ExcelProperty(value = "检查项目（指标）-整合/备注", index = 7)
    //@NotNull(message = "检查项目（指标）-整合/备注（必填）")
    private String indicatorRemark;

    /**
     * 编码
     */
    @ExcelProperty(value = "编码", index = 8)
    //@NotNull(message = "编码（必填）")
    private String healthIndicatorCode;

    /**
     * 指标名称（自定义）
     */
    @ExcelProperty(value = "指标名称（自定义）", index = 9)
    //@NotNull(message = "指标名称（自定义）（必填）")
    private String indicatorName;

    /**
     * 指标意义
     */
    @ExcelProperty(value = "指标意义", index = 10)
    //@NotNull(message = "指标名称（自定义）（必填）")
    private String indicatorMean;
}