package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.core.domain.product.model.BizItemCategory;
import com.jdh.o2oservice.core.domain.product.model.IndicatorItemCategory;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ProductServiceGoods
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 16:01
 **/
@Data
public class ServiceGoodsContrastAttrBo {

    /**
     * 服务id
     */
    private String serviceId;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 商家套餐编号
     */
    private String goodsNo;
    /**
     * 是否是pop商品
     */
    private Boolean isPopSku;
    /**
     * 商品价格
     */
    private String price;

    /**
     * 适用人群 1男 2未婚女 3已婚女 通用
     */
    private String serviceSuitable;

    /**
     * 服务保障
     */
    private String serviceGuarantee;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 检查项目数量
     */
    private Integer indicatorCount;
    /**
     * 重点项目
     */
    private String importantItem;
    /**
     * 重点项目
     */
    private List<String> importantItemList;

    /**
     * 检查项目
     */
    private List<IndicatorItemCategory> indicatorCategoryList;

    /**
     * Biz项目分类集合
     */
    private List<BizItemCategory> bizItemCategoryList;

}