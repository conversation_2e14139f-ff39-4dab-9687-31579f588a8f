package com.jdh.o2oservice.core.domain.product.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <pre>
 *  服务表服务须知
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-07-15 22:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhProgramExtJsonBO {
    /**
     * <pre>
     * 客户确认信息类型（多选）
     * </pre>
     */
    private List<Integer> customerConfirmType;

    /**
     * <pre>
     * 服务记录类型（多选）,1-废料处理 2-服务记录 3-上传着装照片
     * </pre>
     */
    private List<Integer> serviceRecordType;

    /**
     * <pre>
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     * </pre>
     */
    private List<Integer> stationAssignType;

    /**
     * <pre>
     * 是否需要投保 0-不需要 1-需要
     * </pre>
     */
    private Integer requiredInsure;

    /**
     * <pre>
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师
     * </pre>
     */
    private List<Integer> serviceResourceType;

    /**
     * 老服务ID
     */
    private String oldServiceId;
}