package com.jdh.o2oservice.core.domain.product.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 标准项应用场景列举
 *
 * <AUTHOR>
 * @date 2024/07/16
 */
@Getter
public enum StandardItemApplySceneEnum {


    /**
     * 应用场景
     */
    TO_B("1","B端场景"),
    TO_C("2","C端场景"),
    ;


    private static final List<String> ALL_APPLY_SCENE = new ArrayList<>();

    static {
        for (StandardItemApplySceneEnum value : StandardItemApplySceneEnum.values()) {
            ALL_APPLY_SCENE.add(value.getScene());
        }
    }


    /**
     * StandardItemApplySceneEnum
     */
    StandardItemApplySceneEnum(String scene,String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    /**
     * 获取所有应用场景
     *
     * @return {@link List}<{@link String}>
     */
    public static List<String> getAllApplyScene(){
       return ALL_APPLY_SCENE;
    }

    /**
     * 按场景获取enum
     *
     * @param scene 场景
     * @return {@link StandardItemApplySceneEnum}
     */
    public static StandardItemApplySceneEnum getEnumByScene(String scene){
        for (StandardItemApplySceneEnum value : StandardItemApplySceneEnum.values()) {
            if (value.getScene().equals(scene)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 按场景获取enum
     *
     * @param sceneDesc 场景
     * @return {@link StandardItemApplySceneEnum}
     */
    public static StandardItemApplySceneEnum getEnumByDesc(String sceneDesc){
        for (StandardItemApplySceneEnum value : StandardItemApplySceneEnum.values()) {
            if (value.getDesc().equals(sceneDesc)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 服务类型名称
     */
    private String scene;

    /**
     * 描述
     */
    private String desc;
}
