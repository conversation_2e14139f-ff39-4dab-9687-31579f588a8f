package com.jdh.o2oservice.core.domain.product.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ProductServiceAreaContentDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 16:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductServiceAreaContentBO {

    private String desc;

    private Long addressId;
}