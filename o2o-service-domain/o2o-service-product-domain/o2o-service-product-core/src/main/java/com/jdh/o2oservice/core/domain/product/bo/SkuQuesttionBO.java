package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * 商品评价列表
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/22 22:52
 * @Vserion: 1.0
 **/
@Data
public class SkuQuesttionBO {

    /**
     * systemId
     */
    private String systemId;

    /**
     * productId 或者 extProductId下审核通过的提问数
     */
    private Integer passedCount;
    /**
     * 提问列表
     */
    private List<QuesttionBO> questionList;

}
