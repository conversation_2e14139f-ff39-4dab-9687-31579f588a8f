package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

/**
 * @ClassName ProductDetailBottomBanner
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 16:09
 **/
@Data
public class ProductDetailBottomBannerBO {

    private Boolean flag;

    private String text;

    private String btnText;

    /**
     * 限购场景
     * 1-地址围栏，2-无登录态，3-无地址，4-开关，5-其它
     */
    private Integer buyLimitType;

}