package com.jdh.o2oservice.core.domain.product.bo.vo;

import lombok.Data;

/**
 * 商品评价列表结果
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/28 21:56
 * @Vserion: 1.0
 **/
@Data
public class CommentUgcResult {

    /**
     * 图片ID
     */
    private String code;
    /**
     * 图片地址，地址格式( jfs/t22090/195/2508136515/7478/363c94f/5b5a96a6N9dfe9a84.jpg ) 图片地址不包含域名
     */
    private String result;
    /**
     * true 返回结果成功，false返回结果失败
     */
    private Boolean success;
    /**
     * success为false时返回 错误码，详见 ErrorCode 返回码
     */
    private String resultCode;
}
