package com.jdh.o2oservice.core.domain.product.rpc;

import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jdh.o2oservice.core.domain.product.bo.ProductLimitBuyBO;
import com.jdh.o2oservice.core.domain.product.bo.ProductLimitBuyStrategyBO;
import com.jdh.o2oservice.core.domain.product.rpc.param.ProductLimitBuyParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.SkuInfoRequestParam;

import java.util.List;

public interface ProductLimitbuyRpc {


     List<ProductLimitBuyBO> getProductLimitStrategy(List<SkuInfoRequestParam> skuInfoRequestParams, String pin);
}
