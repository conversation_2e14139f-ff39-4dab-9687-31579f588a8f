package com.jdh.o2oservice.core.domain.product.enums;

import com.jdh.o2oservice.base.model.FileBizType;

/**
 * 商品文件管理
 * @date 2024-06-12 09:30
 * <AUTHOR>
 */
public enum ProductFileBizTypeEnum implements FileBizType {

    /**
     * 指标业务分类导入失败
     */
    INDICATOR_BIZ_CATEGORY_FAIL("indicatorBizCategoryFail"),
    /** 指标分类导入失败 */
    INDICATOR_CATEGORY_FAIL("indicatorCategoryFail"),
    /** 指标导入失败） */
    INDICATOR_FAIL("indicatorFail"),
    /** 服务项目导入失败 */
    SERVICE_ITEM_FAIL("serviceItemFail"),
    /** 商品导入失败 */
    SKU_FAIL("skuFail"),
    /** 商品导出 */
    PRODUCT_ITEM_EXPORT("productItemExport"),
    /** 标准项目导入 */
    PRODUCT_STANDARD_ITEM_IMPORT("productStandardItemImport"),
    /** 标准指标导入 */
    PRODUCT_STANDARD_INDICATOR_IMPORT("productStandardIndicatorImport"),
    PRODUCT_ACTIVITY_FLOOR_IMAGE("productActivityFloorImage"),
    ;

    /**
     *
     * @param fileBizType
     */
    ProductFileBizTypeEnum(String fileBizType) {
        this.fileBizType = fileBizType;
    }

    /**
     * 文件类型
     */
    private String fileBizType;

    @Override
    public String getBizType() {
        return fileBizType;
    }
}
