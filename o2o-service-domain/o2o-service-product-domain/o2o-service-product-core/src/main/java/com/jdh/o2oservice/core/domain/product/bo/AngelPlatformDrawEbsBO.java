package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AngelPlatformDrawEbsBO
 * @Description
 * <AUTHOR>
 * @Date 2024/7/24 11:01
 **/
@Data
public class AngelPlatformDrawEbsBO {

    /**
     * <pre>
     * 京东健康，业务模式
     * </pre>
     */
    @ExcelProperty(value = "jdh_business_mode_code", index = 0)
    private String jdhBusinessModeCode;

    /**
     * <pre>
     * 业务系统唯一ID,计费单的主键
     * </pre>
     */
    @ExcelProperty(value = "pre_id", index = 1)
    private String preId;

    /**
     * <pre>
     * 主分类：消费医疗（XFYL），传XFYL即可
     * </pre>
     */
    @ExcelProperty(value = "source_type", index = 2)
    private String sourceType;

    /**
     * <pre>
     * 子分类(计费单类型) 收入: XNKTJTC-SRQR 非企销-收入 XNKTJTC-SRQRDKH 企销-收入 XNKTJTC-SRQRCYS 冲预收
     * </pre>
     */
    @ExcelProperty(value = "detail_source_type", index = 3)
    private String detailSourceType;

    /**
     * <pre>
     * 来源系统：XFYL
     * </pre>
     */
    @ExcelProperty(value = "src_system", index = 4)
    private String srcSystem;

    /**
     * <pre>
     * 业务单号（采购订单号）
     * </pre>
     */
    @ExcelProperty(value = "pre_doc_num", index = 5)
    private String preDocNum;

    /**
     * <pre>
     * 客商编码
     * </pre>
     */
    @ExcelProperty(value = "union_num", index = 6)
    private String unionNum;

    /**
     * <pre>
     * 客商名称
     * </pre>
     */
    @ExcelProperty(value = "union_name", index = 7)
    private String unionName;

    /**
     * <pre>
     * 支付时间，格式为：2020-03-01 20:01:01
     * </pre>
     */
    @ExcelProperty(value = "doc_create_time", index = 8)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date docCreateTime;

    /**
     * <pre>
     * 入账时间（完成时间），格式为：2020-03-01 20:01:01
     * </pre>
     */
    @ExcelProperty(value = "applied_date", index = 9)
    //@DateTimeFormat("yyyy/MM/dd HH:mm")
    private Date appliedDate;

    /**
     * <pre>
     * 总金额，单位:元
     * </pre>
     */
    @ExcelProperty(value = "total_amount", index = 10)
    //@DateTimeFormat("yyyy/MM/dd HH:mm")
    private BigDecimal totalAmount;

    /**
     * <pre>
     * 税率，格式如：6
     * </pre>
     */
    @ExcelProperty(value = "tax_rate", index = 11)
    private String taxRate;

    /**
     * <pre>
     * 入账主体机构
     * </pre>
     */
    @ExcelProperty(value = "org_id", index = 12)
    private String orgId;

    /**
     * <pre>
     * 币种 例：CNY
     * </pre>
     */
    @ExcelProperty(value = "currency_code", index = 13)
    private String currencyCode;

    /**
     * <pre>
     * 业务线编号:如: 100001 这个就是默认值B2C，专门指这个业务
     * </pre>
     */
    @ExcelProperty(value = "business_line", index = 14)
    private String businessLine;

    /**
     * <pre>
     * sku编码
     * </pre>
     */
    @ExcelProperty(value = "sku_id", index = 15)
    private Long skuId;

    /**
     * <pre>
     * 父订单号
     * </pre>
     */
    @ExcelProperty(value = "parent_order_id", index = 16)
    private Long parentOrderId;

    /**
     * <pre>
     * 订单号
     * </pre>
     */
    @ExcelProperty(value = "order_id", index = 17)
    private Long orderId;

    /**
     * <pre>
     * 品类(一级品类ID)
     * </pre>
     */
    @ExcelProperty(value = "first_category_code", index = 18)
    private String firstCategoryCode;

    /**
     * <pre>
     * 项目（门店id）
     * </pre>
     */
    @ExcelProperty(value = "project_code", index = 19)
    private String projectCode;

    /**
     * <pre>
     * 表组：JD_HY_PROVISION
     * </pre>
     */
    @ExcelProperty(value = "mid_tab_group_name", index = 20)
    private String midTabGroupName;

    /**
     * <pre>
     * 费用类型
     * </pre>
     */
    @ExcelProperty(value = "line_doc_type_name", index = 21)
    private String lineDocTypeName;

    /**
     * <pre>
     * 大客户的机构ID(EBS推送) 非企销 不传 企销 传企业客户的下单主体
     * </pre>
     */
    @ExcelProperty(value = "new_org_id", index = 22)
    private String newOrgId;

    /**
     * <pre>
     * 部门编码
     * </pre>
     */
    @ExcelProperty(value = "dept_code", index = 23)
    private String deptCode;

    /**
     * <pre>
     * 部门名称
     * </pre>
     */
    @ExcelProperty(value = "dept_name", index = 24)
    private String deptName;
}