package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预约门店信息
 * @author: yangxiyu
 * @date: 2022/9/14 3:13 下午
 * @version: 1.0
 */
@Data
public class AppointmentStoreBO {

    /**
     * 供应商编码
     */
    private Long channelNo;
    /**
     * 商家门店的ID
     */
    private String storeId;
    /**
     * 外部门店ID
     */
    private String storeOuterId;
    /**
     * 商家门店名称
     */
    private String storeName;
    /**
     * 商家门店地址
     */
    private String storeAddr;
    /** 门店电话 */
    private String storePhone;
    /** 省ID */
    private Integer provinceId;
    /** 省名称 */
    private String provinceName;
    /** 市ID */
    private Integer cityId;
    /** 市名称 */
    private String cityName;
    /** 区ID */
    private Integer countyId;
    /** 区名称 */
    private String countyName;
    /** 镇ID */
    private Integer townId;
    /** 镇名称 */
    private String townName;
    /** 经度 */
    private BigDecimal lng;
    /** 纬度 */
    private BigDecimal lat;
    /**
     * 门店营业时间
     */
    private String storeHours;

    private BigDecimal serviceFee;

    private Integer status;
}
