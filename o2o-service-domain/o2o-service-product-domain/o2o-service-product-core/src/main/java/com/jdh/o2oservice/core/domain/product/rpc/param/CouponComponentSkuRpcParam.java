package com.jdh.o2oservice.core.domain.product.rpc.param;

import lombok.Data;

/**
 * @Description 优惠券sku维度入参
 * @Date 2024/10/21 下午2:22
 * <AUTHOR>
 **/
@Data
public class CouponComponentSkuRpcParam {

    /**
     * sku编码
     */
    private String skuNo;

    /**
     * 商户id
     * 从商品获取
     */
    private String venderId;

    /**
     * 是否支持全球购:1-支持 0-不支持 从商品接口获取
     * 从商品获取
     */
    private String isGlobal;

    /**
     * 是否支持东券:1-支持 0-不支持 从商品接口获取
     * 从商品获取
     */
    private String isCanUseDong;

    /**
     * 是否支持京券:1-支持 0-不支持 从商品接口获取
     */
    private String isCanUseJing;

    /**
     * 分类id
     * 从商品获取
     */
    private String categoryId;

    /**
     * spu维度
     * 从商品获取
     */
    private String spuId;

    /**
     * 百亿补贴标记：1和2都是有百亿补贴  0表示没有百亿补贴 （按场景传入商品或者促销标）
     * 注意：LBS百亿补贴场景调用需传入促销标
     * 从促销获取
     */
    private String msbybt;

    /**
     * 透传的促销标
     * 1）促销的值包含1153则是秒杀月黑风高，1159是百补多人团
     * 2）不传或传value=null同等对待
     * 从促销获取
     */
    private String mutexPromos = "[]";

    /**
     * 商家业务身份
     * JXZ002,或,JX0001,时，代表京喜pop商品;,POP003,代表京东C店商品
     * 从商品获取
     */
    private String vender_bizid;

}
