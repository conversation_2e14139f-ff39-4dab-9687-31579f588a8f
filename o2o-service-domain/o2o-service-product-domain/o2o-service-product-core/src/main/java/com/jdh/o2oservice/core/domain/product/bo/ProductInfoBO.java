package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.health.medical.examination.export.base.dto.ProductServiceAreaDTO;
import com.jd.health.medical.examination.export.base.dto.ProductServiceIntroduceDTO;
import com.jd.health.medical.examination.export.base.dto.transaction.UserAddressDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ProductInfoDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 15:32
 **/
@Data
public class ProductInfoBO {

    /**
     * 服务须知信息
     */
    private ProductServiceIntroduceBO serviceIntroduce;

    /**
     * 是否登录
     */
    private Boolean isLogin;

    /**
     * 服务范围信息
     */
    private List<ProductServiceAreaBO> mainContent;

    /**
     * 用户地址
     */

    private transient UserAddressDetailBO userAddress;

    /**
     * 下单对应的采样实验室ID
     */
    private transient String storeId;

    /**
     * 限购场景
     * 1-地址围栏，2-无登录态，3-无地址，4-开关，5-其它
     *
     */
    private Integer buyLimitType;

}