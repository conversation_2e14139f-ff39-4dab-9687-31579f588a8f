package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品评价内容
 *
 * <AUTHOR>
 * @date 2023/12/22 17:58
 */
@Data
public class CommentUgcBO implements Serializable {
    /**
     * 	主键guid
     */
    private String guid;
    /**
     * 用户头像
     */
    private String portrait;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 等级
     */
    private Integer star;
    /**
     * 评价日期
     */
    private String date;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 图片集合
     */
    private List<String> picList;
    /**
     * 用户pin
     */
    private String pin;
}
