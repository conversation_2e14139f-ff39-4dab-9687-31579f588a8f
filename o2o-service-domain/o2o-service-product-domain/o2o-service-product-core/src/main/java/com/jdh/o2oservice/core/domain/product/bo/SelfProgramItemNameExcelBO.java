package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName PopProductExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2024/7/24 11:01
 **/
@Data
public class SelfProgramItemNameExcelBO {

    /**
     * 业务项目ID
     */
    @ExcelProperty(value = "业务项目ID", index = 0)
    private Long bizItemId;

    /**
     * 业务项目名称
     */
    @ExcelProperty(value = "业务项目名称", index = 1)
    private String bizItemName;

    /**
     * 业务项目适用人群
     */
    @ExcelProperty(value = "业务项目适用人群", index = 2)
    private String bizItemSuitable;
}