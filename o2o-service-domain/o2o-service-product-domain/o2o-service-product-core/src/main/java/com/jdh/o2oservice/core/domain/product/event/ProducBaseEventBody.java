package com.jdh.o2oservice.core.domain.product.event;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jdh.o2oservice.base.event.EventBody;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品变更消息体
 *
 * <AUTHOR>
 * @date 2024/06/03
 */
@Data
public class ProducBaseEventBody implements EventBody {

    /**
     * <pre>
     * 主键
     * </pre>
     */
    private Long id;

    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    private Long skuId;

    /**
     * <pre>
     * 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
     * </pre>
     */
    private Integer saleStatus;

    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private Integer serviceType;

    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;

    /**
     * <pre>
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师,多个值
     * </pre>
     */
    private String serviceResourceType;

    /**
     * <pre>
     * 需提前预约时间,单位小时
     * </pre>
     */
    private Integer advanceAppointTime;

    /**
     * <pre>
     * 未来可约天数,单位天
     * </pre>
     */
    private Integer maxScheduleDays;

    /**
     * <pre>
     * 每天可预约时间段,["08:00-12:00", "14:00-18:00"]
     * </pre>
     */
    private String dayTimeFrame;

    /**
     * <pre>
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     * </pre>
     */
    private String stationAssignType;

    /**
     * <pre>
     * 是否需要投保 0-不需要 1-需要
     * </pre>
     */
    private Integer requiredInsure;

    /**
     * <pre>
     * 商品标签,多个
     * </pre>
     */
    private String tags;

    /**
     * <pre>
     * 是否实名 0-否 1-是
     * </pre>
     */
    private Integer requiredRealName;

    /**
     * <pre>
     * 使用教程地址
     * </pre>
     */
    private String tutorialUrl;

    /**
     * <pre>
     * 知情同意书地址
     * </pre>
     */
    private String informedConsentUrl;

    /**
     * <pre>
     * 预约模板id
     * </pre>
     */
    private Integer appointTemplateId;

    /**
     * <pre>
     * 年龄范围,最小最大值逗号隔开 0,150
     * </pre>
     */
    private String ageRange;

    /**
     * <pre>
     * 适用性别，用户性别 1-男 2-女
     * </pre>
     */
    private String genderLimit;

    /**
     * <pre>
     * 客户确认信息类型,1-医嘱证明
     * </pre>
     */
    private String customerConfirmType;

    /**
     * <pre>
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     * </pre>
     */
    private String serviceRecordType;

    /**
     * <pre>
     * 服务资源结算价
     * </pre>
     */
    private BigDecimal resourceSettlementPrice;

    /**
     * <pre>
     * 渠道id
     * </pre>
     */
    private Long channelId;

    /**
     * <pre>
     * 商品类型；0为主品，1为加项品
     * </pre>
     */
    private Integer skuType;

    /**
     * <pre>
     * 技术难度 0-1000
     * </pre>
     */
    private Integer technicalLevel;

    /**
     * <pre>
     * 购买后服务有效时间,单位天
     * </pre>
     */
    private Integer buyValidPeriod;

    /**
     * 采样教程名称
     */
    private String tutorialName;

}
