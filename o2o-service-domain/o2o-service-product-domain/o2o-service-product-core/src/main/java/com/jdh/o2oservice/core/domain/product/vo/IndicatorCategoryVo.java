package com.jdh.o2oservice.core.domain.product.vo;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * @ClassName:IndicatorCategoryVo
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/3/31 10:41
 * @Vserion: 1.0
 **/
@Data
@Builder
public class IndicatorCategoryVo {

    /**
     * 指标一级分类
     */
    private Set<Long> firstIndicatorCategory;

    /**
     * 指标二级分类
     */
    private Set<Long> secondIndicatorCategory;

    /**
     * 指标三级级分类
     */
    private Set<Long> thirdIndicatorCategory;

}
