package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/21 2:44 下午
 * @Description:
 */
@Data
public class JdhContentBO implements Serializable {


    /**
     * 内容id
     */
    private Long contentId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 文章标题（分词查询）
     */
    private String contentName;
    /**
     * 别名
     */
    private String aliasName;
    /**
     * 英文名
     */
    private String englishName;
    /**
     * 拼音首字母
     */
    private String pinyinFirstLetter;
    /**
     * 内容类型
     * https://cf.jd.com/pages/viewpage.action?pageId=874311519
     */
    private Integer contentType;
    /**
     * 关联的其他内容
     */
    private List<JdhContentBO> contentRelations;
    /**
     * 内容状态 1:上线 2:下线
     */
    private Integer contentStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建者
     */
    private String createUser;
    /**
     * 修改者
     */
    private String updateUser;
    /**
     * yn
     */
    private Integer yn;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 第一次提交时间
     */
    private Date firstSubmitTime;

}
