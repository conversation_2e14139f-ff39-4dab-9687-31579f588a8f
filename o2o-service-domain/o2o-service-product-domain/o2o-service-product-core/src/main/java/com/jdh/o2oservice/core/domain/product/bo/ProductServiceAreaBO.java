package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.health.medical.examination.export.base.dto.ProductServiceAreaContentDTO;
import lombok.Data;

/**
 * @ClassName ProductServiceAreaDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 15:40
 **/
@Data
public class ProductServiceAreaBO {

    private String label;

    private Boolean isLogin;

    /**
     * 是否展示
     */
    private Boolean flag;

    private ProductServiceAreaContentBO content;

    private Boolean isSelect = true;

    private Long addressId;

    private String provinceCode;

    private String cityCode;

    private String areaCode;

    private Boolean serviceAreaMapSwitch;
}