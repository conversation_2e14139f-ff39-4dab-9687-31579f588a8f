package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 商品评价标签
 *
 * <AUTHOR>
 * @date 2023/12/22 17:58
 */
@Data
public class TagStatisticsBO implements Serializable {
    /**
     * 标签id
     */
    private Long labelId;
    /**
     * 标签
     */
    private String label;
    /**
     * 标签评价数量
     */
    private String count;
    /**
     * 4 有图/有视频 评价
     */
    private Integer score;

    public TagStatisticsBO(){}
    /**
     *
     * @param label
     * @param count
     * @param score
     */
    public TagStatisticsBO(String label, String count, Integer score){
        this.label = label;
        this.count = count;
        this.score = score;
    }
}
