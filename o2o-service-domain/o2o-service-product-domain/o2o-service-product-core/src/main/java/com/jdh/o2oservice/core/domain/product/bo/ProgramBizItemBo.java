package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐详情参数包装
 *
 * <AUTHOR>
 * @date 2024/8/1 17:29
 */
@Data
public class ProgramBizItemBo implements Serializable {

    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 二级商品类目
     */
    private String secondBizCategoryName;
    /**
     * 是否重点项目
     */
    private String importantItem;

    /**
     * 项目检测指标列表
     */
    private String indicatorList;
    /**
     * 项目检查意义 itemSignificance mean
     */
    private String itemSignificance;
    /**
     * 适用人群
     */
    private String itemSuitableList;
    /**
     * 二级分类及检查项列表
     */
    private List<ProgramBizIndicatorBo> indicatorCategoryList;

    /**
     * 用于组内排序
     */
    private Integer sortIndex;
}
