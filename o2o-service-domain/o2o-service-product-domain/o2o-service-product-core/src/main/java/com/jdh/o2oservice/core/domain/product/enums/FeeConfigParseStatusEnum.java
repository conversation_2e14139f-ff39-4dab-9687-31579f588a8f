package com.jdh.o2oservice.core.domain.product.enums;

/**
 * 文件解析状态
 *
 * <AUTHOR>
 * @date 2024/09/17
 */
public enum FeeConfigParseStatusEnum {
    /**
     * 文件解析状态
     */
    PARSE_ING(1,"配置中"),
    PARSE_SUCC(2,"配置成功"),
    PARSE_FAIL(3,"配置失败"),
    ;
    private Integer code;
    private String desc;

    FeeConfigParseStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     *
     * @param code
     * @return
     */
    public static String getParseStatusDesc(Integer code) {
        for (FeeConfigParseStatusEnum value : FeeConfigParseStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
