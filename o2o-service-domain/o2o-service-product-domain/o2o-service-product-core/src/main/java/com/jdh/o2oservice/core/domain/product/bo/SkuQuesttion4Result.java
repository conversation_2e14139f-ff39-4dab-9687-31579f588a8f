package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * 商品评价列表
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/22 22:52
 * @Vserion: 1.0
 **/
@Data
public class SkuQuesttion4Result {
    /**
     * 是否展示整个提问楼层
     */
    private Boolean showQuestionFloor = Boolean.FALSE;
    /**
     * 是否展示提问框
     */
    private Boolean showQuestionBox = Boolean.FALSE;

    /**
     * 提问列表
     */
    private List<QuesttionBO> questionDetailList;
}
