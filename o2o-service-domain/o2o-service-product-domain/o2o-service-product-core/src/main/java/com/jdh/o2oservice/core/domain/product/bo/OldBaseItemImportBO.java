package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName OldBaseItemImportBO
 * @Description
 * <AUTHOR>
 * @Date 2024/9/3 23:48
 **/
@Data
public class OldBaseItemImportBO {

    /**
     * 旧 一级
     */
    @ExcelProperty(value = "item_id", index = 0)
    private Long itemId;

    /**
     * 旧 一级
     */
    @ExcelProperty(value = "item_name", index = 1)
    private String itemame;

    /**
     * 旧 一级
     */
    @ExcelProperty(value = "item_mean", index = 2)
    private String itemMean;

    /**
     * 旧 一级
     */
    @ExcelProperty(value = "item_suitable", index = 3)
    private String itemSuitable;
}