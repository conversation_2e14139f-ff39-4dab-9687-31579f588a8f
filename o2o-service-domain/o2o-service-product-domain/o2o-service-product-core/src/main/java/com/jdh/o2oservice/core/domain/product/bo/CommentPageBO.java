package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @ClassName:SamplingCourseBO
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2023/12/14 23:56
 * @Vserion: 1.0
 **/
@Data
public class CommentPageBO {

    /**
     * skuNo
     */
    @NotEmpty(message = "商品编码不能为空")
    private String skuNo;

    /**
     * 必传，列表星级 0 全部；1 差评（一分）； 2 中评（二三分）； 3 好评 （四五分）；4 有图/有视频 评价 ；5 追评；
     * 7有视频；8 PLUS筛选；9 USE 使用后回答筛选，11壹分评价；12 二分评价；,13 三 分评价； 14 四 分评价； 15 五分评价；
     *
     * 主站支持：0，1，2，3，4，5，6，7，8 ,9 ；
     */
    private Integer score = 0;

    /**
     * 排序方式： 5 推荐排序； 6 时间排序，必传
     */
    private Integer sortType = 5;
    /**
     * 当前页数
     */
    @NotEmpty(message = "查询页数不能为空")
    private Integer pageNum = 0;
    /**
     * 查询条数
     */
    private Integer pageSize = 10;
    /**
     * labelId
     */
    private String labelId;
}
