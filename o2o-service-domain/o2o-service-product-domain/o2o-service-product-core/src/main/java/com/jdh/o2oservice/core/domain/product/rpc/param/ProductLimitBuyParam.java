package com.jdh.o2oservice.core.domain.product.rpc.param;

import lombok.Data;

import java.util.List;

/**
 * @ClassName:ProductLimitBuyBO
 * @Description: TODO
 * @Author: yaoqing<PERSON>
 * @Date: 2023/12/11 18:57
 * @Vserion: 1.0
 **/
@Data
public class ProductLimitBuyParam {

    /**
     * userPin
     */
    private String userPin;

    /**
     * 商品列表
     */
    private List<SkuInfoRequestParam> skuInfoRequestParams;

    /**
     * 省编码
     */
    private Integer provinceId;

    /**
     * 市编码
     */
    private Integer cityId;

    /**
     * 区编码
     */
    private Integer countyId;

    /**
     * 镇编码
     */
    private Integer townId;

}
