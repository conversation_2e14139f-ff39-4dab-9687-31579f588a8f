package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName ServiceProgramItemIndicatorRelBo
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 16:01
 **/
@Data
public class ServiceProgramItemIndicatorRelBo {

    /**
     * skuNo
     */
    private String skuNo;
    /**
     * <pre>
     * 销售展示方式 1-普通服务 2-组合服务 9-影子服务（基于普通服务异构化部分属性，比如根据不同渠道调整预约规则）
     * </pre>
     */
    private Integer saleShowMethod;

    /**
     * 套餐适用人群 1男 2未婚女 3已婚女 通用
     */
    private List<String> programSuitable;
    /**
     * isPopSku
     */
    private Boolean isPopSku;
    /**
     * 商品价格
     */
    private String price;
    /**
     * 检查项目数量
     */
    private Integer indicatorCount;
    /**
     * programItemIndicatorBoList
     */
    private List<ProgramItemIndicatorBo> programItemIndicatorBoList;
}