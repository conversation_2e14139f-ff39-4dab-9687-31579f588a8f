package com.jdh.o2oservice.core.domain.product.enums;

import com.jdh.o2oservice.base.enums.AlarmLevelEnum;
import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

import java.util.Objects;

/**
 * 商品域错误码
 * @author: yang<PERSON>yu
 * @date: 2023/12/18 8:11 下午
 * @version: 1.0
 */
@ToString
public enum SerivceProductErrorCode implements AbstractErrorCode {
    /**
     * 商品域错误码
     */
    SERVICE_NOT_EXIST(DomainEnum.PRODUCT, "60001", "商品不存在或已下架"),
    SERVICE_IN_POOL_EXIST(DomainEnum.PRODUCT, "60002", "套餐已加入套餐对比中"),
    SERVICE_POOL_RANG_OUT(DomainEnum.PRODUCT, "60003", "套餐池中套餐已超过最高限制"),
    SERVICE_CONTRAST_RANG_OUT(DomainEnum.PRODUCT, "60004", "最多选择3份套餐"),
    SERVICE_CONTRAST_POOL_NOT_SAME(DomainEnum.PRODUCT, "60004", "套餐池某商品已删除"),
    SERVICE_NOT_LOC(DomainEnum.PRODUCT, "60005", "非套餐对比商品"),
    SERVICE_CONTRAST_DIFF_TIME_OUT(DomainEnum.PRODUCT, "60006", "套餐对比差异，请刷新页面重新进入"),

    INDICATOR_NUM_NON_MATCH(DomainEnum.PRODUCT, "60007", "指标数据量不匹配!"),
    QUERY_SKU_STYLE_IS_ERROR(DomainEnum.PRODUCT,"60008", "获取商品样式失败"),

    QUERY_SKU_INFO_IS_ERROR(DomainEnum.PRODUCT,"60009", "获取商品信息失败"),

    QUERY_SKU_BIG_FIELD_IS_ERROR(DomainEnum.PRODUCT,"60010", "获取商品大字段信息失败"),

    PROGRAM_ITEM_REL_NOT_EXIST_ERROR(DomainEnum.PRODUCT,"60011", "套餐绑定的项目不能为空"),

    PROGRAM_SUITABLE_EMPTY_ERROR(DomainEnum.PRODUCT,"60012", "套餐适用人群不能为空"),

    PROGRAM_ITEM_SUITABLE_MATCH_ERROR(DomainEnum.PRODUCT,"60013", "套餐项目适用人群不匹配"),
    ;


    /**
     * ProviderErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    SerivceProductErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    SerivceProductErrorCode(DomainEnum domainEnum, String code, String description, AlarmLevelEnum alarmLevelEnum) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
        this.alarmLevelEnum = alarmLevelEnum;
    }

    /** */
    private DomainEnum domainEnum;
    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 报警级别
     */
    private AlarmLevelEnum alarmLevelEnum;

    /**
     * 获取代码
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return description;
    }

    /**
     * 获取报警级别
     *
     * @return
     */
    public AlarmLevelEnum getAlarmLevel() {
        if(Objects.isNull(alarmLevelEnum)) {
            return AlarmLevelEnum.WARING;
        }
        return alarmLevelEnum;
    }
}
