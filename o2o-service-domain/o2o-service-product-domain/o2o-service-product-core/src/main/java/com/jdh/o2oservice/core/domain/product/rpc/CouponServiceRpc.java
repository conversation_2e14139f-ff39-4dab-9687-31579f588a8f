package com.jdh.o2oservice.core.domain.product.rpc;
import com.jdh.o2oservice.core.domain.product.rpc.bo.CouponInfoBO;
import com.jdh.o2oservice.core.domain.product.rpc.bo.GetCouponResultBO;
import com.jdh.o2oservice.core.domain.product.rpc.param.BatchGetCouponRpcParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.FindCouponsRpcParam;
import com.jdh.o2oservice.core.domain.product.rpc.param.GetCouponRpcParam;
import java.util.List;
import java.util.Map;

/**
 * @Description 优惠券服务
 * @Date 2024/10/21 上午11:43
 * <AUTHOR>
 **/
public interface CouponServiceRpc {


    /**
     * 券购-可领券查询
     * @param param
     * @return
     */
    Map<String, List<CouponInfoBO>> findJoinActives(FindCouponsRpcParam param);

    /**
     * 券购-可用券查询
     * @param param
     * @return
     */
    Map<String, List<CouponInfoBO>> findCanUseCoupons(FindCouponsRpcParam param);

    /**
     * 领券接口：领取
     * @param param
     * @return
     */
    GetCouponResultBO getCoupon(GetCouponRpcParam param);

    /**
     * 领券接口：批量领取
     * @param param
     * @return
     */
    GetCouponResultBO batchGetCoupon(BatchGetCouponRpcParam param);

}
