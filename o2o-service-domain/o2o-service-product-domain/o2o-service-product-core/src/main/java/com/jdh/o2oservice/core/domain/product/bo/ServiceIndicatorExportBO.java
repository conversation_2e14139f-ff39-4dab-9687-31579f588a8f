package com.jdh.o2oservice.core.domain.product.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName ServiceIndicatorExportDto
 * @Description
 * <AUTHOR>
 * @Date 2024/9/9 20:31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceIndicatorExportBO {

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID", index = 0)
    private Long itemId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称", index = 1)
    private String itemName;

    /**
     * 项目意义
     */
    @ExcelProperty(value = "项目意义", index = 2)
    private String itemMean;

    /**
     * 适用人群
     */
    @ExcelProperty(value = "适用人群", index = 3)
    private String itemSuitable;

    /**
     * 指标ID
     */
    @ExcelProperty(value = "指标ID", index = 4)
    private String indicatorId;

    /**
     * 指标名称
     */
    @ExcelProperty(value = "指标名称", index = 5)
    private String indicatorName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 6)
    private String remark;

    /**
     * 绑定的京东套餐名称列表
     */
    @ExcelProperty(value = "绑定的京东套餐名称列表", index = 7)
    private String relationServiceNameList;

    /**
     * 标准项目名称
     */
    @ExcelProperty(value = "标准项目名称", index = 8)
    private String standardItemName;

    /**
     * 标准指标编码
     */
    @ExcelProperty(value = "标准指标编码", index = 9)
    private String healthIndicatorCode;

    /**
     * 指标检查意义
     */
    private String indicatorMean;

    /**
     * 适用人群
     */
    private String indicatorSuitable;

    /**
     * 一级分类编码
     */
    private String firstIndicatorCategory;

    /**
     * 一级分类名称
     */
    private String firstIndicatorCategoryName;

    /**
     * 二级分类编码
     */
    private String secondIndicatorCategory;

    /**
     * 二级分类名称
     */
    private String secondIndicatorCategoryName;

    /**
     * 三级分类编码
     */
    private String thirdIndicatorCategory;

    /**
     * 三级分类编名称
     */
    private String thirdIndicatorCategoryName;

    /**
     * 一级商品类目信息
     */
    private Long firstSkuCategory;

    /**
     * 二级商品类目信息
     */
    private Long secondSkuCategory;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 京东健康指标百科ID
     */
    private String jdhWikiId;

    /**
     * 检测结果类型 1-定量 2-定性
     */
    private Integer testResultType;

    /**
     * 特殊标签 1-条件致病菌,多个逗号隔开
     */
    private String tags;
}