package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

/**
 * @ClassName ProductDetailCustomIconConfigDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/11/24 16:15
 **/
@Data
public class ProductDetailCustomIconConfigBO {

    /**
     * 是否展示
     */
    Boolean open;

    String icon;
    String text;
    String jumpLink;
    Boolean isClick;
    /**
     * 1 店铺信息，2 咚咚客服，3 立即购买
     */
    private Integer buttonType;
    /**
     * 按钮顺序
     */
    private Integer sequence;
}