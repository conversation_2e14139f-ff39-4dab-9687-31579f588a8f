package com.jdh.o2oservice.core.domain.product.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * ProductAggregateCodeEnum
 *
 * @author: yaoqinghai
 * @date: 2024/03/28 11:13
 * @version: 1.0
 */
public enum ProductAggregateCodeEnum implements AggregateCode {

    /**
     *
     */
    JDH_PRODUCT_SERVICE(DomainEnum.PRODUCT, "JdhService"),

    JDH_PRODUCT_SERVICE_ITEM(DomainEnum.PRODUCT, "JdhServiceItem"),

    ;

    ProductAggregateCodeEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /** */
    private DomainCode domain;
    /** */
    private String code;
    /**
     * 聚合编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    /**
     * 聚合名称
     *
     * @return
     */
    @Override
    public String getCode() {
        return code;
    }
}
