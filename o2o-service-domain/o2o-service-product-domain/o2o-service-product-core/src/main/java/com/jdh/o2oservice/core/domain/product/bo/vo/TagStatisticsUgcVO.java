package com.jdh.o2oservice.core.domain.product.bo.vo;

import lombok.Data;

/**
 * 商品评价标签
 * @Description:
 * @Author: liwenming
 * @Date: 2023/12/28 21:56
 * @Vserion: 1.0
 **/
@Data
public class TagStatisticsUgcVO {
    /**
     * 标签id
     */
    private Long id;
    /**
     * 标签文本
     */
    private String name;
    /**
     * 	数量
     */
    private Integer count;
    /**
     * 是否可以筛选 true 可以筛选 false 不能筛选
     */
    private Boolean canBeFiltered;
    /**
     * 标签类型,0 普通标签，3 图书标签，4 语义标签，5 NPS标签
     */
    private Integer type;

}
