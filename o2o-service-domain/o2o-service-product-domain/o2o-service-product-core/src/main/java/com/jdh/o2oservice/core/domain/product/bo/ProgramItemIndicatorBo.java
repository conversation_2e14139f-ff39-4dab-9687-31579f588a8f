package com.jdh.o2oservice.core.domain.product.bo;

import com.jdh.o2oservice.core.domain.product.model.BizItemCategory;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ServiceProgramItemIndicatorRelBo
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 16:01
 **/
@Data
public class ProgramItemIndicatorBo {

    /**
     * 套餐id
     */
    private Long programId;
    /**
     * 套餐名称
     */
    private String programName;

    /**
     * 套餐适用人群 1男 2未婚女 3已婚女 通用
     */
    private List<String> programSuitable;
    /**
     * Biz项目分类集合
     */
    private List<BizItemCategory> bizItemCategoryList;

}