package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

import java.util.Date;

/**
 * 查询消费医疗运营端商品套餐结果
 *
 * @date 2024-08-27 16:34
 * <AUTHOR>
 */
@Data
public class ExaminationSkuBindGroupBo {

    /**
     * 体检项目组名称-套餐名称
     */
    private String groupName;

    /**
     * 项目组编码
     */
    private String groupNo;

    /**
     * 套餐特点
     */
    private String groupDesc;

    /**
     * 适用性别
     */
    private String groupSuitable;

    /**
     * 适用人群
     */
    private String groupSuitMan;

    /**
     * 体检项数量
     */
    private String groupItemNum;

    /**
     * 年龄上限
     */
    private Integer ageUpper;

    /**
     * 年龄下限
     */
    private Integer ageFloor;

    /**
     * 1是 0否
     */
    private String yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     *
     */
    private Date updateTime;
}
