package com.jdh.o2oservice.core.domain.product.rpc;


import com.jdh.o2oservice.core.domain.product.bo.ExaminationSkuBindGroupBo;
import com.jdh.o2oservice.core.domain.product.rpc.param.ExaminationSkuBindGroupParam;

import java.util.List;

/**
 * 消费医疗运营端商品查询服务
 *
 * <AUTHOR>
 * @date 2024-08-27 15:20 2024-08-27 16:27
 */
public interface ExaminationSkuInfoRpc {

    /**
     * 查询运营端商品关联的套餐列表信息
     *
     * @param groupParam
     * @return
     */
    List<ExaminationSkuBindGroupBo> queryExaminationGroupListBySku(ExaminationSkuBindGroupParam groupParam);

}
