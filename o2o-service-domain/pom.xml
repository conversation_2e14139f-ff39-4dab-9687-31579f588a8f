<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdh.o2oservice</groupId>
        <artifactId>jdh-o2o-service</artifactId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>

    <artifactId>o2o-service-domain</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <modules>
        <module>o2o-service-product-domain</module>
        <module>o2o-service-promise-domain</module>
        <module>o2o-service-provider-domain</module>
        <module>o2o-service-report-domain</module>
        <module>o2o-service-settlement-domain</module>
        <module>o2o-service-trade-domain</module>
        <module>o2o-service-support-domain</module>
        <module>o2o-service-angelpromise-domain</module>
        <module>o2o-service-medpromise-domain</module>
        <module>o2o-service-dispatch-domain</module>
        <module>o2o-service-angel-domain</module>
        <module>o2o-service-content-domain</module>
        <module>o2o-service-user-domain</module>
    </modules>

</project>
